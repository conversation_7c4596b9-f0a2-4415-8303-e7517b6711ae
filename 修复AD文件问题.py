#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复Chrome自动填充AD文件问题
"""

import os
import shutil
from pathlib import Path

def check_ad_file_problem():
    """检查AD文件问题"""
    print("检查AD文件问题...")
    
    ad_file_path = Path("data/browser_profiles/account_10/AutofillStates/2025.6.13.84507/AD")
    
    if not ad_file_path.exists():
        print("AD文件不存在")
        return False
    
    try:
        # 检查文件大小
        file_size = ad_file_path.stat().st_size
        print(f"AD文件大小: {file_size} bytes")
        
        # 尝试读取文件内容
        with open(ad_file_path, 'rb') as f:
            first_bytes = f.read(100)
        
        print(f"文件开头字节: {first_bytes[:20]}")
        
        # 检查是否包含乱码
        try:
            with open(ad_file_path, 'r', encoding='utf-8') as f:
                content = f.read(200)
            
            if '���' in content:
                print("发现编码问题: 文件包含乱码字符")
                return True
            
            if 'parròquia' in content:
                print("发现格式问题: 文件应该是二进制格式，但显示为文本")
                return True
                
        except UnicodeDecodeError:
            print("文件是二进制格式 - 这是正常的")
            return False
            
        return False
        
    except Exception as e:
        print(f"检查AD文件时出错: {e}")
        return True

def fix_ad_file():
    """修复AD文件"""
    print("修复AD文件...")
    
    ad_file_path = Path("data/browser_profiles/account_10/AutofillStates/2025.6.13.84507/AD")
    
    if not ad_file_path.exists():
        print("AD文件不存在，无需修复")
        return True
    
    try:
        # 备份原文件
        backup_path = ad_file_path.with_suffix('.backup')
        shutil.copy2(ad_file_path, backup_path)
        print(f"已备份原文件到: {backup_path}")
        
        # 删除损坏的AD文件
        ad_file_path.unlink()
        print("已删除损坏的AD文件")
        
        print("Chrome会在下次启动时重新下载AD文件")
        return True
        
    except Exception as e:
        print(f"修复AD文件失败: {e}")
        return False

def check_other_country_files():
    """检查其他国家文件是否正常"""
    print("检查其他国家文件...")
    
    autofill_dir = Path("data/browser_profiles/account_10/AutofillStates/2025.6.13.84507")
    
    if not autofill_dir.exists():
        print("自动填充目录不存在")
        return
    
    # 检查几个常见国家的文件
    test_countries = ['US', 'CN', 'GB', 'DE', 'FR']
    
    for country in test_countries:
        country_file = autofill_dir / country
        if country_file.exists():
            try:
                file_size = country_file.stat().st_size
                
                # 尝试读取文件
                with open(country_file, 'rb') as f:
                    first_bytes = f.read(20)
                
                # 检查是否是正常的二进制文件
                try:
                    with open(country_file, 'r', encoding='utf-8') as f:
                        content = f.read(100)
                    
                    if '���' in content:
                        print(f"{country}文件也有编码问题")
                    else:
                        print(f"{country}文件: {file_size} bytes - 可能正常")
                        
                except UnicodeDecodeError:
                    print(f"{country}文件: {file_size} bytes - 二进制格式正常")
                    
            except Exception as e:
                print(f"检查{country}文件失败: {e}")
        else:
            print(f"{country}文件不存在")

def clean_corrupted_autofill_files():
    """清理所有损坏的自动填充文件"""
    print("检查并清理损坏的自动填充文件...")
    
    autofill_dir = Path("data/browser_profiles/account_10/AutofillStates/2025.6.13.84507")
    
    if not autofill_dir.exists():
        print("自动填充目录不存在")
        return 0
    
    corrupted_files = []
    
    for file_path in autofill_dir.iterdir():
        if file_path.is_file() and len(file_path.name) == 2:  # 国家代码文件
            try:
                # 检查文件是否损坏
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read(200)
                
                if '���' in content or 'parròquia' in content:
                    corrupted_files.append(file_path)
                    
            except Exception:
                # 如果无法读取，可能是正常的二进制文件
                continue
    
    if corrupted_files:
        print(f"发现 {len(corrupted_files)} 个损坏的文件:")
        for file_path in corrupted_files:
            print(f"  {file_path.name}")
        
        choice = input("是否删除这些损坏的文件？(y/n): ").lower().strip()
        if choice == 'y':
            for file_path in corrupted_files:
                try:
                    # 备份
                    backup_path = file_path.with_suffix('.backup')
                    shutil.copy2(file_path, backup_path)
                    
                    # 删除
                    file_path.unlink()
                    print(f"  删除并备份: {file_path.name}")
                except Exception as e:
                    print(f"  删除失败 {file_path.name}: {e}")
            
            print("Chrome会重新下载这些文件")
            return len(corrupted_files)
    else:
        print("没有发现损坏的文件")
        return 0

def main():
    """主函数"""
    print("Chrome AD文件问题修复工具")
    print("=" * 40)
    
    # 1. 检查AD文件问题
    print("1. 检查AD文件问题")
    has_problem = check_ad_file_problem()
    
    # 2. 检查其他国家文件
    print("\n2. 检查其他国家文件")
    check_other_country_files()
    
    # 3. 修复选项
    print("\n3. 修复选项")
    
    if has_problem:
        print("发现AD文件问题")
        
        choice = input("选择修复方式:\n1. 只修复AD文件\n2. 检查并清理所有损坏文件\n3. 跳过修复\n请选择 (1/2/3): ").strip()
        
        if choice == '1':
            success = fix_ad_file()
            if success:
                print("AD文件修复完成")
            else:
                print("AD文件修复失败")
                
        elif choice == '2':
            cleaned_count = clean_corrupted_autofill_files()
            print(f"清理了 {cleaned_count} 个损坏的文件")
            
        else:
            print("跳过修复")
    else:
        print("AD文件看起来正常")
    
    print("\n修复说明:")
    print("  - 删除损坏的文件后，Chrome会自动重新下载")
    print("  - 这不会影响浏览器的核心功能")
    print("  - 自动填充功能会在重新下载后恢复")
    print("  - 原文件已备份，可以恢复")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"\n异常: {e}")
        import traceback
        traceback.print_exc()
