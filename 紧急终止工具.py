#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
紧急终止工具 - 当Ctrl+C无效时使用
"""

import os
import sys
import psutil
import subprocess
import time

def emergency_kill_all():
    """紧急终止所有相关进程"""
    print("🚨 紧急终止所有相关进程")
    print("=" * 40)
    
    current_dir = os.getcwd()
    killed_processes = []
    
    # 1. 终止Python进程
    print("1. 终止Python进程...")
    current_pid = os.getpid()
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if 'python' in proc.info['name'].lower() and proc.info['pid'] != current_pid:
                cmdline = ' '.join(proc.info['cmdline'] or [])
                if any(keyword in cmdline for keyword in [current_dir, 'main.py', '测试', '修复']):
                    proc.kill()
                    killed_processes.append(f"Python PID {proc.info['pid']}")
                    print(f"  ✅ 强制终止Python: PID {proc.info['pid']}")
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    # 2. 终止Chrome进程
    print("2. 终止Chrome进程...")
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if 'chrome' in proc.info['name'].lower():
                cmdline = ' '.join(proc.info['cmdline'] or [])
                if any(keyword in cmdline for keyword in ['browser_profiles', 'remote-debugging-port=9']):
                    proc.kill()
                    killed_processes.append(f"Chrome PID {proc.info['pid']}")
                    print(f"  ✅ 强制终止Chrome: PID {proc.info['pid']}")
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    # 3. 终止ChromeDriver进程
    print("3. 终止ChromeDriver进程...")
    for proc in psutil.process_iter(['pid', 'name']):
        try:
            if 'chromedriver' in proc.info['name'].lower():
                proc.kill()
                killed_processes.append(f"ChromeDriver PID {proc.info['pid']}")
                print(f"  ✅ 强制终止ChromeDriver: PID {proc.info['pid']}")
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    # 4. 系统级清理
    print("4. 系统级清理...")
    try:
        subprocess.run(['taskkill', '/f', '/im', 'chrome.exe'], capture_output=True, check=False)
        subprocess.run(['taskkill', '/f', '/im', 'chromedriver.exe'], capture_output=True, check=False)
        print("  ✅ 系统级清理完成")
    except Exception as e:
        print(f"  ⚠️  系统级清理失败: {e}")
    
    print(f"\n📊 总共终止了 {len(killed_processes)} 个进程")
    for proc in killed_processes:
        print(f"  - {proc}")
    
    print("\n✅ 紧急终止完成")
    print("建议:")
    print("  1. 等待5秒让系统稳定")
    print("  2. 关闭所有相关终端窗口")
    print("  3. 重新启动程序")

if __name__ == "__main__":
    try:
        emergency_kill_all()
        time.sleep(2)
        print("\n程序将在3秒后自动退出...")
        time.sleep(3)
    except Exception as e:
        print(f"紧急终止失败: {e}")
        print("建议使用任务管理器手动终止进程")
