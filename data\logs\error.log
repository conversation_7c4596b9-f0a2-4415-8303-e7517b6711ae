2025-07-24 23:41:55 | ERROR    | __main__:initialize:76 | 应用程序初始化失败: 核心组件初始化失败: 数据库管理器未初始化
2025-07-24 23:46:45 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x24300d42940> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:47:03 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x24308fa1c70> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:47:33 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x24308fa1070> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:48:03 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x24308fa1c70> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:48:33 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x24308fa1520> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:49:03 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x24308fa1d90> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:49:33 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x24308fa1040> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:50:03 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x24308fa1d90> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:50:33 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x24308fa1640> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:51:03 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x24308fa1910> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:51:33 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x24308fa17f0> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:52:03 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x24308fa1430> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:52:22 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x24308fd6370> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:52:33 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x24308fcdf70> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:52:36 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x24308fd6820> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:52:51 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x243245c4a30> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:53:03 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x243245c48b0> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:53:14 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x243245c4cd0> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:53:17 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x243245c4970> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:53:22 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x243245c44c0> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:53:25 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x243245c4bb0> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:53:28 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x243245dc1c0> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:53:30 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x243245dc2e0> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:53:32 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x243245dc5b0> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-24 23:53:33 | ERROR    | src.ui.widgets.account_widget:update_table:257 | 更新表格失败: Parent instance <Account at 0x243245dc130> is not bound to a Session; lazy load operation of attribute 'group' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-25 00:18:18 | ERROR    | src.ui.widgets.account_widget:import_accounts:752 | 打开导入对话框失败: name 'QDialog' is not defined
2025-07-25 00:21:42 | ERROR    | src.ui.dialogs.group_management_dialog:update_group_table:212 | 更新分组表格失败: Parent instance <AccountGroup at 0x26fb8fb78e0> is not bound to a Session; lazy load operation of attribute 'accounts' cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-25 00:24:31 | ERROR    | src.ui.widgets.account_widget:import_accounts:752 | 打开导入对话框失败: name 'QDialog' is not defined
2025-07-25 00:47:22 | ERROR    | src.ui.widgets.account_widget:import_accounts:830 | 打开导入对话框失败: name 'QDialog' is not defined
2025-07-25 01:13:08 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:13:08 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:13:08 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:13:09 | ERROR    | src.ui.widgets.account_widget:import_accounts:830 | 打开导入对话框失败: name 'QDialog' is not defined
2025-07-25 01:13:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:13:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:13:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:13:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:13:26 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:13:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:13:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:13:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:14:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:14:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:14:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:14:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:14:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:14:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:15:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:15:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:15:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:15:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:15:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:15:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:16:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:16:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:16:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:16:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:16:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:16:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:17:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:17:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:17:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:17:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:17:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:17:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:18:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:18:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:18:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:18:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:18:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:18:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:19:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:19:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:19:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:19:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:19:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:19:53 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:20:02 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:20:02 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:20:02 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:20:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:20:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:20:23 | ERROR    | src.ui.widgets.account_widget:update_table:625 | 更新表格失败: name 'QFont' is not defined
2025-07-25 01:33:08 | ERROR    | src.core.proxy_manager:test_proxies:377 | 测试代理失败: 代理状态必须是: active, inactive, testing
2025-07-25 01:33:14 | ERROR    | src.core.proxy_manager:test_proxies:377 | 测试代理失败: 代理状态必须是: active, inactive, testing
2025-07-25 01:33:39 | ERROR    | src.core.proxy_manager:test_proxies:377 | 测试代理失败: 代理状态必须是: active, inactive, testing
2025-07-25 01:33:47 | ERROR    | src.core.proxy_manager:test_proxies:377 | 测试代理失败: 代理状态必须是: active, inactive, testing
2025-07-25 01:47:27 | ERROR    | src.ui.widgets.proxy_widget:assign_proxies:451 | 分配代理失败: 'AccountGroupManager' object has no attribute 'get_groups'
2025-07-25 01:57:58 | ERROR    | src.core.account_manager:get_accounts:557 | 获取账号列表失败: No module named 'src.models'
2025-07-25 01:58:03 | ERROR    | src.core.account_manager:get_accounts:557 | 获取账号列表失败: No module named 'src.models'
2025-07-25 01:58:08 | ERROR    | src.core.account_manager:get_accounts:557 | 获取账号列表失败: No module named 'src.models'
2025-07-25 01:58:13 | ERROR    | src.core.account_manager:get_accounts:557 | 获取账号列表失败: No module named 'src.models'
2025-07-25 01:58:18 | ERROR    | src.core.account_manager:get_accounts:557 | 获取账号列表失败: No module named 'src.models'
2025-07-25 01:58:23 | ERROR    | src.core.account_manager:get_accounts:557 | 获取账号列表失败: No module named 'src.models'
2025-07-25 01:58:28 | ERROR    | src.core.account_manager:get_accounts:557 | 获取账号列表失败: No module named 'src.models'
2025-07-25 01:58:28 | ERROR    | src.core.account_manager:get_accounts:557 | 获取账号列表失败: No module named 'src.models'
2025-07-25 01:58:28 | ERROR    | src.core.account_manager:get_accounts:557 | 获取账号列表失败: No module named 'src.models'
2025-07-25 01:58:28 | ERROR    | src.core.account_manager:get_accounts:557 | 获取账号列表失败: No module named 'src.models'
2025-07-25 01:58:33 | ERROR    | src.core.account_manager:get_accounts:557 | 获取账号列表失败: No module named 'src.models'
2025-07-25 01:58:38 | ERROR    | src.core.account_manager:get_accounts:557 | 获取账号列表失败: No module named 'src.models'
2025-07-25 03:13:10 | ERROR    | src.ui.main_window:create_tabs:111 | 创建标签页失败: type object 'Qt' has no attribute 'Horizontal'
2025-07-25 03:34:20 | ERROR    | src.modules.posting.content_editor_widget:analyze_content:551 | 内容分析失败: wrapped C/C++ object of type QLabel has been deleted
2025-07-25 03:39:16 | ERROR    | __main__:handle_exception:323 | 未捕获的异常
2025-07-25 03:39:34 | ERROR    | src.modules.posting.content_editor_widget:update_variable_list:597 | 更新变量列表失败: wrapped C/C++ object of type QListWidget has been deleted
2025-07-25 03:39:34 | ERROR    | src.modules.posting.content_editor_widget:update_history_list:779 | 更新历史记录失败: wrapped C/C++ object of type QListWidget has been deleted
2025-07-25 03:56:00 | ERROR    | src.modules.posting.content_editor_widget:analyze_content:551 | 内容分析失败: wrapped C/C++ object of type QLabel has been deleted
2025-07-25 03:57:39 | ERROR    | src.modules.variables.variable_manager:add_variable:123 | 添加变量失败: the JSON object must be str, bytes or bytearray, not InstrumentedAttribute
2025-07-25 03:58:03 | ERROR    | src.modules.variables.variable_manager:add_variable:123 | 添加变量失败: the JSON object must be str, bytes or bytearray, not InstrumentedAttribute
2025-07-25 04:04:07 | ERROR    | __main__:handle_exception:323 | 未捕获的异常
2025-07-25 04:04:11 | ERROR    | __main__:handle_exception:323 | 未捕获的异常
2025-07-25 04:04:44 | ERROR    | __main__:handle_exception:323 | 未捕获的异常
2025-07-25 04:05:55 | ERROR    | __main__:handle_exception:323 | 未捕获的异常: KeyError: 'name'
2025-07-25 04:06:11 | ERROR    | __main__:handle_exception:323 | 未捕获的异常: KeyError: 'name'
2025-07-25 04:06:41 | ERROR    | __main__:handle_exception:323 | 未捕获的异常: KeyError: 'name'
2025-07-25 04:06:45 | ERROR    | __main__:handle_exception:323 | 未捕获的异常: KeyError: 'name'
2025-07-25 04:07:27 | ERROR    | __main__:handle_exception:325 | 未捕获的异常: KeyError: 'name'
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\variable_widget.py", line 526, in <lambda>
    edit_btn.clicked.connect(lambda checked, n=name: self.edit_variable(n))
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\variable_widget.py", line 559, in edit_variable
    dialog = VariableEditDialog(variable_data, parent=self)
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\variable_widget.py", line 236, in __init__
    self.load_variable_data()
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\variable_widget.py", line 307, in load_variable_data
    self.name_edit.setText(self.variable_data['name'])
KeyError: 'name'

2025-07-25 04:07:42 | ERROR    | __main__:handle_exception:325 | 未捕获的异常: KeyError: 'name'
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\variable_widget.py", line 526, in <lambda>
    edit_btn.clicked.connect(lambda checked, n=name: self.edit_variable(n))
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\variable_widget.py", line 559, in edit_variable
    dialog = VariableEditDialog(variable_data, parent=self)
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\variable_widget.py", line 236, in __init__
    self.load_variable_data()
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\variable_widget.py", line 307, in load_variable_data
    self.name_edit.setText(self.variable_data['name'])
KeyError: 'name'

2025-07-25 04:10:16 | ERROR    | __main__:handle_exception:325 | 未捕获的异常: RuntimeError: wrapped C/C++ object of type QLineEdit has been deleted
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 524, in open_variables_dialog
    dialog = VariablesDialog(self)
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 951, in __init__
    self.setup_ui()
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 966, in setup_ui
    self.parent_widget.var_search_edit.textChanged.connect(self.parent_widget.filter_variables)
RuntimeError: wrapped C/C++ object of type QLineEdit has been deleted

2025-07-25 04:10:25 | ERROR    | __main__:handle_exception:325 | 未捕获的异常: RuntimeError: wrapped C/C++ object of type QLineEdit has been deleted
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 524, in open_variables_dialog
    dialog = VariablesDialog(self)
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 951, in __init__
    self.setup_ui()
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 966, in setup_ui
    self.parent_widget.var_search_edit.textChanged.connect(self.parent_widget.filter_variables)
RuntimeError: wrapped C/C++ object of type QLineEdit has been deleted

2025-07-25 04:10:25 | ERROR    | __main__:handle_exception:325 | 未捕获的异常: RuntimeError: wrapped C/C++ object of type QLineEdit has been deleted
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 524, in open_variables_dialog
    dialog = VariablesDialog(self)
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 951, in __init__
    self.setup_ui()
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 966, in setup_ui
    self.parent_widget.var_search_edit.textChanged.connect(self.parent_widget.filter_variables)
RuntimeError: wrapped C/C++ object of type QLineEdit has been deleted

2025-07-25 04:11:00 | ERROR    | src.modules.posting.content_editor_widget:update_analysis_display:592 | 更新分析显示失败: wrapped C/C++ object of type QLabel has been deleted
2025-07-25 04:16:51 | ERROR    | __main__:handle_exception:325 | 未捕获的异常: RuntimeError: wrapped C/C++ object of type QListWidget has been deleted
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 532, in open_variables_dialog
    dialog = VariablesDialog(self)
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 974, in __init__
    self.setup_ui()
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 1006, in setup_ui
    self.parent_widget.variable_list.itemDoubleClicked.connect(self.insert_and_close)
RuntimeError: wrapped C/C++ object of type QListWidget has been deleted

2025-07-25 04:20:24 | ERROR    | __main__:handle_exception:325 | 未捕获的异常: SystemError: <class 'PyQt6.QtWidgets.QListWidget'> returned a result with an error set
堆栈跟踪:
RuntimeError: wrapped C/C++ object of type QListWidget has been deleted

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 532, in open_variables_dialog
    dialog = VariablesDialog(self)
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 1030, in __init__
    self.setup_ui()
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 1071, in setup_ui
    temp_variable_list = QListWidget()
SystemError: <class 'PyQt6.QtWidgets.QListWidget'> returned a result with an error set

2025-07-25 04:20:33 | ERROR    | __main__:handle_exception:325 | 未捕获的异常: SystemError: <class 'PyQt6.QtWidgets.QListWidget'> returned a result with an error set
堆栈跟踪:
RuntimeError: wrapped C/C++ object of type QListWidget has been deleted

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 532, in open_variables_dialog
    dialog = VariablesDialog(self)
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 1030, in __init__
    self.setup_ui()
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 1071, in setup_ui
    temp_variable_list = QListWidget()
SystemError: <class 'PyQt6.QtWidgets.QListWidget'> returned a result with an error set

2025-07-25 04:20:37 | ERROR    | __main__:handle_exception:325 | 未捕获的异常: SystemError: <class 'PyQt6.QtWidgets.QListWidget'> returned a result with an error set
堆栈跟踪:
RuntimeError: wrapped C/C++ object of type QListWidget has been deleted

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 532, in open_variables_dialog
    dialog = VariablesDialog(self)
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 1030, in __init__
    self.setup_ui()
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 1071, in setup_ui
    temp_variable_list = QListWidget()
SystemError: <class 'PyQt6.QtWidgets.QListWidget'> returned a result with an error set

2025-07-25 04:21:05 | ERROR    | __main__:handle_exception:325 | 未捕获的异常: SystemError: <class 'PyQt6.QtWidgets.QListWidget'> returned a result with an error set
堆栈跟踪:
RuntimeError: wrapped C/C++ object of type QListWidget has been deleted

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 532, in open_variables_dialog
    dialog = VariablesDialog(self)
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 1030, in __init__
    self.setup_ui()
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 1071, in setup_ui
    temp_variable_list = QListWidget()
SystemError: <class 'PyQt6.QtWidgets.QListWidget'> returned a result with an error set

2025-07-25 04:22:14 | ERROR    | __main__:handle_exception:325 | 未捕获的异常: NameError: name 'custom_layout' is not defined
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 532, in open_variables_dialog
    dialog = VariablesDialog(self)
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 1049, in __init__
    self.setup_ui()
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 1094, in setup_ui
    self._create_temp_variable_list(custom_layout)
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 1129, in _create_temp_variable_list
    custom_layout.addLayout(var_btn_layout)
NameError: name 'custom_layout' is not defined

2025-07-25 04:23:26 | ERROR    | __main__:handle_exception:325 | 未捕获的异常: NameError: name 'tab_widget' is not defined
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 532, in open_variables_dialog
    dialog = VariablesDialog(self)
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 1049, in __init__
    self.setup_ui()
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 1094, in setup_ui
    self._create_temp_variable_list(custom_layout)
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 1131, in _create_temp_variable_list
    tab_widget.addTab(custom_tab, "自定义变量")
NameError: name 'tab_widget' is not defined

2025-07-25 04:25:30 | ERROR    | __main__:handle_exception:325 | 未捕获的异常: RuntimeError: wrapped C/C++ object of type QListWidget has been deleted
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 532, in open_variables_dialog
    dialog = VariablesDialog(self)
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 1049, in __init__
    self.setup_ui()
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 1094, in setup_ui
    self._create_temp_variable_list(custom_layout)
RuntimeError: wrapped C/C++ object of type QListWidget has been deleted

2025-07-25 04:30:19 | ERROR    | __main__:handle_exception:325 | 未捕获的异常: RuntimeError: wrapped C/C++ object of type QListWidget has been deleted
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 532, in open_variables_dialog
    dialog = VariablesDialog(self)
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 1086, in __init__
    self.setup_ui()
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\content_editor_widget.py", line 1151, in setup_ui
    self._create_temp_variable_list(custom_layout)
RuntimeError: wrapped C/C++ object of type QListWidget has been deleted

2025-07-25 04:57:25 | ERROR    | src.ui.widgets.account_widget:load_groups:528 | 加载分组失败: 数据库管理器未初始化
2025-07-25 04:57:26 | ERROR    | src.ui.widgets.proxy_widget:load_data:202 | 加载代理数据失败: 数据库管理器未初始化
2025-07-25 04:57:26 | ERROR    | src.ui.main_window:create_tabs:111 | 创建标签页失败: 数据库管理器未初始化
2025-07-25 04:57:26 | ERROR    | src.ui.widgets.account_widget:load_data:560 | 加载账号数据失败: 数据库管理器未初始化
2025-07-25 04:57:56 | ERROR    | src.ui.widgets.account_widget:load_data:560 | 加载账号数据失败: 数据库管理器未初始化
2025-07-25 04:58:05 | ERROR    | src.ui.widgets.account_widget:load_data:560 | 加载账号数据失败: 数据库管理器未初始化
2025-07-25 04:58:06 | ERROR    | src.ui.widgets.account_widget:load_groups:528 | 加载分组失败: 数据库管理器未初始化
2025-07-25 04:58:06 | ERROR    | src.ui.widgets.account_widget:load_data:560 | 加载账号数据失败: 数据库管理器未初始化
2025-07-25 04:58:13 | ERROR    | src.ui.main_window:update_status:248 | 更新状态失败: 数据库管理器未初始化
2025-07-25 04:58:18 | ERROR    | src.ui.main_window:update_status:248 | 更新状态失败: 数据库管理器未初始化
2025-07-25 04:58:23 | ERROR    | src.ui.main_window:update_status:248 | 更新状态失败: 数据库管理器未初始化
2025-07-25 05:26:23 | ERROR    | src.modules.variables.variable_manager:add_variable:123 | 添加变量失败: 变量类型必须是: emoji, text, tag
2025-07-25 05:27:10 | ERROR    | __main__:handle_exception:332 | 未捕获的异常: AttributeError: 'QMenu' object has no attribute 'exec_'
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yyu7\src\modules\posting\variable_widget.py", line 706, in show_context_menu
    menu.exec_(self.variable_table.mapToGlobal(position))
AttributeError: 'QMenu' object has no attribute 'exec_'

2025-07-25 05:27:34 | ERROR    | src.modules.variables.variable_manager:add_variable:123 | 添加变量失败: 变量类型必须是: emoji, text, tag
2025-07-25 05:30:34 | ERROR    | src.modules.posting.content_editor_widget:refresh_data:544 | 刷新数据失败: 'ContentEditorWidget' object has no attribute 'update_variable_list'
2025-07-25 05:31:15 | ERROR    | src.modules.variables.variable_manager:add_variable:82 | 变量名格式不正确: 123
2025-07-25 05:32:54 | ERROR    | src.modules.posting.content_editor_widget:refresh_data:544 | 刷新数据失败: 'ContentEditorWidget' object has no attribute 'update_variable_list'
2025-07-25 05:34:24 | ERROR    | src.modules.posting.content_editor_widget:refresh_data:544 | 刷新数据失败: 'ContentEditorWidget' object has no attribute 'update_variable_list'
2025-07-25 06:17:19 | ERROR    | src.ui.main_window:open_template_config:387 | 打开模板池配置失败: QDialog(parent: Optional[QWidget] = None, flags: Union[Qt.WindowFlags, Qt.WindowType] = Qt.WindowFlags()): argument 1 has unexpected type 'MainWindow'
2025-07-25 06:19:21 | ERROR    | src.ui.main_window:open_template_config:387 | 打开模板池配置失败: QDialog(parent: Optional[QWidget] = None, flags: Union[Qt.WindowFlags, Qt.WindowType] = Qt.WindowFlags()): argument 1 has unexpected type 'MainWindow'
2025-07-25 06:22:11 | ERROR    | src.ui.main_window:open_template_config:387 | 打开模板池配置失败: 'TemplatePoolConfigDialog' object has no attribute 'exec_'
2025-07-25 06:25:16 | ERROR    | src.ui.dialogs.template_pool_config_dialog:save_config:467 | 保存配置失败: type object 'Qt' has no attribute 'UserRole'
2025-07-25 06:26:16 | ERROR    | src.ui.main_window:open_template_config:387 | 打开模板池配置失败: type object 'QDialog' has no attribute 'Accepted'
2025-07-25 06:28:17 | ERROR    | src.ui.main_window:open_template_config:387 | 打开模板池配置失败: type object 'QDialog' has no attribute 'Accepted'
2025-07-25 06:34:03 | ERROR    | src.modules.posting.template_pool:get_template_for_account:272 | 获取模板失败: string indices must be integers
2025-07-25 06:34:03 | ERROR    | src.modules.posting.template_pool:get_template_for_account:272 | 获取模板失败: string indices must be integers
2025-07-25 06:34:03 | ERROR    | src.modules.posting.template_pool:get_template_for_account:272 | 获取模板失败: string indices must be integers
2025-07-25 06:34:03 | ERROR    | src.modules.posting.template_pool:get_template_for_account:272 | 获取模板失败: string indices must be integers
2025-07-25 06:34:03 | ERROR    | src.modules.posting.template_pool:get_template_for_account:272 | 获取模板失败: string indices must be integers
2025-07-25 06:34:11 | ERROR    | src.modules.posting.template_pool:get_template_for_account:272 | 获取模板失败: string indices must be integers
2025-07-25 06:34:11 | ERROR    | src.modules.posting.template_pool:get_template_for_account:272 | 获取模板失败: string indices must be integers
2025-07-25 06:34:11 | ERROR    | src.modules.posting.template_pool:get_template_for_account:272 | 获取模板失败: string indices must be integers
2025-07-25 06:34:11 | ERROR    | src.modules.posting.template_pool:get_template_for_account:272 | 获取模板失败: string indices must be integers
2025-07-25 06:34:18 | ERROR    | src.modules.posting.template_pool:get_template_for_account:272 | 获取模板失败: string indices must be integers
2025-07-25 06:34:18 | ERROR    | src.modules.posting.template_pool:get_template_for_account:272 | 获取模板失败: string indices must be integers
2025-07-25 06:34:18 | ERROR    | src.modules.posting.template_pool:get_template_for_account:272 | 获取模板失败: string indices must be integers
2025-07-25 06:37:31 | ERROR    | src.ui.main_window:create_tabs:144 | 创建标签页失败: No module named 'src.utils.settings'
2025-07-25 06:38:18 | ERROR    | src.modules.posting.template_pool:get_template_for_account:261 | 获取模板失败: string indices must be integers
2025-07-25 06:38:18 | ERROR    | src.modules.posting.template_pool:get_template_for_account:261 | 获取模板失败: string indices must be integers
2025-07-25 06:38:18 | ERROR    | src.modules.posting.template_pool:get_template_for_account:261 | 获取模板失败: string indices must be integers
2025-07-25 06:40:27 | ERROR    | src.modules.posting.template_pool:get_template_for_account:261 | 获取模板失败: string indices must be integers
2025-07-25 06:40:27 | ERROR    | src.modules.posting.template_pool:get_template_for_account:261 | 获取模板失败: string indices must be integers
2025-07-25 06:40:27 | ERROR    | src.modules.posting.template_pool:get_template_for_account:261 | 获取模板失败: string indices must be integers
2025-07-25 06:40:27 | ERROR    | src.modules.posting.template_pool:get_template_for_account:261 | 获取模板失败: string indices must be integers
2025-07-25 06:40:27 | ERROR    | src.modules.posting.template_pool:get_template_for_account:261 | 获取模板失败: string indices must be integers
2025-07-25 06:40:42 | ERROR    | src.modules.posting.template_pool:get_template_for_account:261 | 获取模板失败: string indices must be integers
2025-07-25 06:40:42 | ERROR    | src.modules.posting.template_pool:get_template_for_account:261 | 获取模板失败: string indices must be integers
2025-07-25 06:40:42 | ERROR    | src.modules.posting.template_pool:get_template_for_account:261 | 获取模板失败: string indices must be integers
2025-07-25 06:40:42 | ERROR    | src.modules.posting.template_pool:get_template_for_account:261 | 获取模板失败: string indices must be integers
2025-07-25 06:40:42 | ERROR    | src.modules.posting.template_pool:get_template_for_account:261 | 获取模板失败: string indices must be integers
2025-07-25 06:41:35 | ERROR    | src.modules.posting.template_pool:get_template_for_account:267 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:41:35 | ERROR    | src.modules.posting.template_pool:get_template_for_account:267 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:41:35 | ERROR    | src.modules.posting.template_pool:get_template_for_account:267 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:41:35 | ERROR    | src.modules.posting.template_pool:get_template_for_account:267 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:41:35 | ERROR    | src.modules.posting.template_pool:get_template_for_account:267 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:41:45 | ERROR    | src.modules.posting.template_pool:get_template_for_account:267 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:41:45 | ERROR    | src.modules.posting.template_pool:get_template_for_account:267 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:41:45 | ERROR    | src.modules.posting.template_pool:get_template_for_account:267 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:41:45 | ERROR    | src.modules.posting.template_pool:get_template_for_account:267 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:41:45 | ERROR    | src.modules.posting.template_pool:get_template_for_account:267 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:42:20 | ERROR    | src.modules.posting.template_pool:get_template_for_account:270 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:42:20 | ERROR    | src.modules.posting.template_pool:get_template_for_account:270 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:42:20 | ERROR    | src.modules.posting.template_pool:get_template_for_account:270 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:42:20 | ERROR    | src.modules.posting.template_pool:get_template_for_account:270 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:42:20 | ERROR    | src.modules.posting.template_pool:get_template_for_account:270 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:43:52 | ERROR    | src.modules.posting.template_pool:get_template_for_account:286 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:43:52 | ERROR    | src.modules.posting.template_pool:get_template_for_account:286 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:43:52 | ERROR    | src.modules.posting.template_pool:get_template_for_account:286 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:43:52 | ERROR    | src.modules.posting.template_pool:get_template_for_account:286 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:43:52 | ERROR    | src.modules.posting.template_pool:get_template_for_account:286 | 获取模板失败: 'str' object has no attribute 'get'
2025-07-25 06:47:45 | ERROR    | src.modules.posting.template_pool:get_template_for_account:270 | 意外的字符串模板，这不应该发生: 上帝是
我们哈哈哈...
2025-07-25 06:47:45 | ERROR    | src.modules.posting.template_pool:get_template_for_account:270 | 意外的字符串模板，这不应该发生: 哈啊{{random_emoji}} {{custom_text}}...
2025-07-25 06:47:45 | ERROR    | src.modules.posting.template_pool:get_template_for_account:270 | 意外的字符串模板，这不应该发生: {{random_emoji}}

{{random_emoji}}才说的   {{custom_t...
2025-07-25 06:47:45 | ERROR    | src.modules.posting.template_pool:get_template_for_account:270 | 意外的字符串模板，这不应该发生: 哈哈{{random_emoji}} {{custom_text}}...
2025-07-25 06:47:45 | ERROR    | src.modules.posting.template_pool:get_template_for_account:270 | 意外的字符串模板，这不应该发生: 上帝是
我们哈哈哈...
2025-07-25 14:38:42 | ERROR    | src.modules.variables.variable_manager:add_variable:123 | 添加变量失败: 变量类型必须是: emoji, text, tag
2025-07-25 14:39:03 | ERROR    | src.modules.variables.variable_manager:add_variable:123 | 添加变量失败: 变量类型必须是: emoji, text, tag
2025-07-25 15:07:45 | ERROR    | src.modules.variables.variable_manager:add_variable:123 | 添加变量失败: 变量类型必须是: emoji, text, tag
2025-07-25 15:08:16 | ERROR    | src.modules.variables.variable_manager:add_variable:123 | 添加变量失败: 变量类型必须是: emoji, text, tag
2025-07-25 15:31:08 | ERROR    | src.ui.widgets.account_widget:batch_login_accounts:1048 | 批量登录账号失败: 'AccountWidget' object has no attribute 'get_selected_accounts'
2025-07-25 15:33:55 | ERROR    | src.modules.posting.executor:login_account:97 | 登录异常: PaigeBaldw49027, Page.goto: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://x.com/i/flow/login", waiting until "networkidle"

2025-07-25 15:34:30 | ERROR    | src.modules.posting.executor:login_account:97 | 登录异常: CharleneCa2057, Locator.wait_for: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("[data-testid=\"ocfEnterTextNextButton\"]") to be visible

2025-07-25 15:34:30 | ERROR    | __main__:handle_exception:332 | 未捕获的异常: AttributeError: 'AccountWidget' object has no attribute 'refresh_accounts'
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yyu7\src\ui\widgets\account_widget.py", line 1106, in on_login_finished
    self.refresh_accounts()
AttributeError: 'AccountWidget' object has no attribute 'refresh_accounts'

2025-07-25 15:37:05 | ERROR    | src.modules.posting.executor:login_account:97 | 登录异常: PaigeBaldw49027, Page.goto: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://x.com/i/flow/login", waiting until "networkidle"

2025-07-25 15:37:45 | ERROR    | src.modules.posting.executor:login_account:97 | 登录异常: CharleneCa2057, Page.goto: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://x.com/i/flow/login", waiting until "networkidle"

2025-07-25 15:54:52 | ERROR    | src.modules.posting.executor:login_account:129 | 登录异常: PaigeBaldw49027 (尝试 1/3), Locator.wait_for: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("[data-testid=\"ocfEnterTextNextButton\"]") to be visible

2025-07-25 15:55:57 | ERROR    | src.modules.posting.executor:login_account:129 | 登录异常: PaigeBaldw49027 (尝试 2/3), Locator.wait_for: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("[data-testid=\"ocfEnterTextNextButton\"]") to be visible

2025-07-25 15:57:01 | ERROR    | src.modules.posting.executor:login_account:129 | 登录异常: PaigeBaldw49027 (尝试 3/3), Locator.wait_for: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("[data-testid=\"ocfEnterTextNextButton\"]") to be visible

2025-07-25 15:57:42 | ERROR    | src.modules.posting.executor:login_account:129 | 登录异常: CharleneCa2057 (尝试 1/3), Page.goto: Target page, context or browser has been closed
2025-07-25 15:57:49 | ERROR    | src.modules.posting.executor:login_account:129 | 登录异常: CharleneCa2057 (尝试 2/3), Page.goto: Target page, context or browser has been closed
2025-07-25 15:57:54 | ERROR    | src.modules.posting.executor:login_account:129 | 登录异常: CharleneCa2057 (尝试 3/3), Page.goto: Target page, context or browser has been closed
2025-07-25 16:21:55 | ERROR    | src.modules.posting.executor:login_account:177 | 登录异常: CharleneCa2057 (尝试 1/3), Page.goto: net::ERR_PROXY_CONNECTION_FAILED at https://x.com/i/flow/login
Call log:
  - navigating to "https://x.com/i/flow/login", waiting until "domcontentloaded"

2025-07-25 16:22:08 | ERROR    | src.modules.posting.executor:login_account:177 | 登录异常: CharleneCa2057 (尝试 2/3), Page.goto: net::ERR_PROXY_CONNECTION_FAILED at https://x.com/i/flow/login
Call log:
  - navigating to "https://x.com/i/flow/login", waiting until "domcontentloaded"

2025-07-25 16:22:22 | ERROR    | src.modules.posting.executor:login_account:177 | 登录异常: CharleneCa2057 (尝试 3/3), Page.goto: net::ERR_PROXY_CONNECTION_FAILED at https://x.com/i/flow/login
Call log:
  - navigating to "https://x.com/i/flow/login", waiting until "domcontentloaded"

2025-07-25 16:38:25 | ERROR    | __main__:handle_exception:332 | 未捕获的异常: ImportError: cannot import name 'X_SELECTORS' from 'src.config.constants' (C:\Users\<USER>\Desktop\yyu7\src\config\constants.py)
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\yyu7\src\ui\widgets\account_widget.py", line 39, in run
    from src.core.account_status_manager import AccountStatusManager
  File "C:\Users\<USER>\Desktop\yyu7\src\core\account_status_manager.py", line 15, in <module>
    from src.config.constants import ACCOUNT_STATUS, X_SELECTORS
ImportError: cannot import name 'X_SELECTORS' from 'src.config.constants' (C:\Users\<USER>\Desktop\yyu7\src\config\constants.py)

2025-07-25 17:15:15 | ERROR    | src.modules.posting.content_manager:prepare_content:355 | 准备内容失败: get_random_media() got an unexpected keyword argument 'image_count'
2025-07-25 17:15:15 | ERROR    | src.modules.posting.workflow:_create_posting_task:158 | 创建发帖任务失败: the JSON object must be str, bytes or bytearray, not InstrumentedAttribute
2025-07-25 17:15:15 | ERROR    | src.modules.posting.content_manager:prepare_content:355 | 准备内容失败: get_random_media() got an unexpected keyword argument 'image_count'
2025-07-25 17:15:15 | ERROR    | src.modules.posting.workflow:_create_posting_task:158 | 创建发帖任务失败: the JSON object must be str, bytes or bytearray, not InstrumentedAttribute
2025-07-25 17:15:46 | ERROR    | src.modules.posting.content_manager:prepare_content:355 | 准备内容失败: get_random_media() got an unexpected keyword argument 'image_count'
2025-07-25 17:15:46 | ERROR    | src.modules.posting.workflow:_create_posting_task:158 | 创建发帖任务失败: the JSON object must be str, bytes or bytearray, not InstrumentedAttribute
2025-07-25 17:15:46 | ERROR    | src.modules.posting.content_manager:prepare_content:355 | 准备内容失败: get_random_media() got an unexpected keyword argument 'image_count'
2025-07-25 17:15:46 | ERROR    | src.modules.posting.workflow:_create_posting_task:158 | 创建发帖任务失败: the JSON object must be str, bytes or bytearray, not InstrumentedAttribute
2025-07-25 17:22:27 | ERROR    | src.modules.posting.content_manager:prepare_content:355 | 准备内容失败: get_random_media() got an unexpected keyword argument 'image_count'
2025-07-25 17:22:27 | ERROR    | src.modules.posting.workflow:_create_posting_task:158 | 创建发帖任务失败: the JSON object must be str, bytes or bytearray, not InstrumentedAttribute
2025-07-25 17:22:27 | ERROR    | src.modules.posting.content_manager:prepare_content:355 | 准备内容失败: get_random_media() got an unexpected keyword argument 'image_count'
2025-07-25 17:22:27 | ERROR    | src.modules.posting.workflow:_create_posting_task:158 | 创建发帖任务失败: the JSON object must be str, bytes or bytearray, not InstrumentedAttribute
2025-07-25 17:27:04 | ERROR    | src.modules.posting.workflow:_create_posting_task:158 | 创建发帖任务失败: the JSON object must be str, bytes or bytearray, not InstrumentedAttribute
2025-07-25 17:27:04 | ERROR    | src.modules.posting.workflow:_create_posting_task:158 | 创建发帖任务失败: the JSON object must be str, bytes or bytearray, not InstrumentedAttribute
2025-07-25 17:31:09 | ERROR    | src.modules.posting.workflow:_create_posting_task:162 | 创建发帖任务失败: the JSON object must be str, bytes or bytearray, not InstrumentedAttribute
2025-07-25 17:31:09 | ERROR    | src.modules.posting.workflow:_create_posting_task:162 | 创建发帖任务失败: the JSON object must be str, bytes or bytearray, not InstrumentedAttribute
2025-07-25 17:33:15 | ERROR    | src.modules.posting.workflow:_execute_sequential_posting:198 | 执行发帖任务失败: Instance <PostingTask at 0x222d99bfa90> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-25 17:33:15 | ERROR    | src.modules.posting.workflow:execute_batch_posting:105 | 批量发帖失败: Instance <PostingTask at 0x222d99bfa90> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-25 17:34:36 | ERROR    | src.modules.posting.workflow:_execute_sequential_posting:198 | 执行发帖任务失败: Instance <PostingTask at 0x1f8bbdd4850> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-25 17:34:36 | ERROR    | src.modules.posting.workflow:execute_batch_posting:105 | 批量发帖失败: Instance <PostingTask at 0x1f8bbdd4850> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-25 17:35:27 | ERROR    | src.modules.posting.workflow:execute_batch_posting:114 | 批量发帖失败: Instance <PostingTask at 0x18f87a85850> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-25 17:37:32 | ERROR    | src.modules.posting.workflow:execute_batch_posting:105 | 批量发帖失败: Instance <PostingTask at 0x1d690174850> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-25 17:38:30 | ERROR    | src.modules.posting.workflow:execute_batch_posting:105 | 批量发帖失败: Instance <PostingTask at 0x1369abc4b80> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-07-25 17:39:33 | ERROR    | src.modules.posting.workflow:_execute_single_posting_task_by_id:316 | 执行发帖任务失败: 'PostingWorkflow' object has no attribute 'poster'
2025-07-25 17:39:33 | ERROR    | src.modules.posting.workflow:_execute_single_posting_task_by_id:316 | 执行发帖任务失败: 'PostingWorkflow' object has no attribute 'poster'
2025-07-25 17:40:44 | ERROR    | src.modules.posting.workflow:_execute_single_posting_task_by_id:316 | 执行发帖任务失败: 'PostingExecutor' object has no attribute 'post'
2025-07-25 17:40:44 | ERROR    | src.modules.posting.workflow:_execute_single_posting_task_by_id:316 | 执行发帖任务失败: 'PostingExecutor' object has no attribute 'post'
2025-07-25 17:43:49 | ERROR    | src.ui.widgets.account_widget:load_groups:694 | 加载分组失败: 数据库管理器未初始化
2025-07-25 17:43:49 | ERROR    | src.ui.widgets.proxy_widget:load_data:202 | 加载代理数据失败: 数据库管理器未初始化
2025-07-25 17:43:49 | ERROR    | src.ui.main_window:create_tabs:144 | 创建标签页失败: 数据库管理器未初始化
2025-07-25 17:43:49 | ERROR    | src.ui.widgets.account_widget:load_data:726 | 加载账号数据失败: 数据库管理器未初始化
2025-07-25 17:44:00 | ERROR    | src.ui.main_window:update_status:288 | 更新状态失败: 数据库管理器未初始化
2025-07-25 17:44:05 | ERROR    | src.ui.main_window:update_status:288 | 更新状态失败: 数据库管理器未初始化
2025-07-25 17:44:10 | ERROR    | src.ui.main_window:update_status:288 | 更新状态失败: 数据库管理器未初始化
2025-07-25 17:46:08 | ERROR    | src.modules.posting.executor:_input_text_content:364 | 输入文本内容失败: Locator.wait_for: Error: strict mode violation: locator("[data-testid=\"tweetTextarea_0\"]") resolved to 2 elements:
    1) <div tabindex="0" role="textbox" spellcheck="true" aria-multiline="true" aria-label="Post text" contenteditable="true" aria-autocomplete="list" data-testid="tweetTextarea_0" no-focustrapview-refocus="true" aria-describedby="placeholder-6r22t" aria-controls="typeaheadDropdownWrapped-7" class="notranslate public-DraftEditor-content" aria-activedescendant="typeaheadFocus-0.7887473660550232">…</div> aka get_by_role("textbox", name="Post text")
    2) <div tabindex="0" role="textbox" spellcheck="true" aria-multiline="true" aria-label="Post text" contenteditable="true" aria-autocomplete="list" data-testid="tweetTextarea_0" no-focustrapview-refocus="true" aria-describedby="placeholder-c77cc" aria-controls="typeaheadDropdownWrapped-6" class="notranslate public-DraftEditor-content" aria-activedescendant="typeaheadFocus-0.5782926927736265">…</div> aka get_by_test_id("primaryColumn").get_by_test_id("tweetTextarea_0")

Call log:
  - waiting for locator("[data-testid=\"tweetTextarea_0\"]") to be visible

2025-07-25 17:46:08 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, Locator.wait_for: Error: strict mode violation: locator("[data-testid=\"tweetTextarea_0\"]") resolved to 2 elements:
    1) <div tabindex="0" role="textbox" spellcheck="true" aria-multiline="true" aria-label="Post text" contenteditable="true" aria-autocomplete="list" data-testid="tweetTextarea_0" no-focustrapview-refocus="true" aria-describedby="placeholder-6r22t" aria-controls="typeaheadDropdownWrapped-7" class="notranslate public-DraftEditor-content" aria-activedescendant="typeaheadFocus-0.7887473660550232">…</div> aka get_by_role("textbox", name="Post text")
    2) <div tabindex="0" role="textbox" spellcheck="true" aria-multiline="true" aria-label="Post text" contenteditable="true" aria-autocomplete="list" data-testid="tweetTextarea_0" no-focustrapview-refocus="true" aria-describedby="placeholder-c77cc" aria-controls="typeaheadDropdownWrapped-6" class="notranslate public-DraftEditor-content" aria-activedescendant="typeaheadFocus-0.5782926927736265">…</div> aka get_by_test_id("primaryColumn").get_by_test_id("tweetTextarea_0")

Call log:
  - waiting for locator("[data-testid=\"tweetTextarea_0\"]") to be visible

2025-07-25 17:46:37 | ERROR    | src.modules.posting.executor:_input_text_content:364 | 输入文本内容失败: Locator.wait_for: Error: strict mode violation: locator("[data-testid=\"tweetTextarea_0\"]") resolved to 2 elements:
    1) <div tabindex="0" role="textbox" spellcheck="true" aria-multiline="true" aria-label="Post text" contenteditable="true" aria-autocomplete="list" data-testid="tweetTextarea_0" no-focustrapview-refocus="true" aria-describedby="placeholder-4mesc" aria-controls="typeaheadDropdownWrapped-9" class="notranslate public-DraftEditor-content" aria-activedescendant="typeaheadFocus-0.18096618257532016">…</div> aka get_by_role("textbox", name="Post text")
    2) <div tabindex="0" role="textbox" spellcheck="true" aria-multiline="true" aria-label="Post text" contenteditable="true" aria-autocomplete="list" data-testid="tweetTextarea_0" no-focustrapview-refocus="true" aria-describedby="placeholder-bf4eo" aria-controls="typeaheadDropdownWrapped-8" class="notranslate public-DraftEditor-content" aria-activedescendant="typeaheadFocus-0.12133633962791812">…</div> aka get_by_test_id("primaryColumn").get_by_test_id("tweetTextarea_0")

Call log:
  - waiting for locator("[data-testid=\"tweetTextarea_0\"]") to be visible

2025-07-25 17:46:37 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, Locator.wait_for: Error: strict mode violation: locator("[data-testid=\"tweetTextarea_0\"]") resolved to 2 elements:
    1) <div tabindex="0" role="textbox" spellcheck="true" aria-multiline="true" aria-label="Post text" contenteditable="true" aria-autocomplete="list" data-testid="tweetTextarea_0" no-focustrapview-refocus="true" aria-describedby="placeholder-4mesc" aria-controls="typeaheadDropdownWrapped-9" class="notranslate public-DraftEditor-content" aria-activedescendant="typeaheadFocus-0.18096618257532016">…</div> aka get_by_role("textbox", name="Post text")
    2) <div tabindex="0" role="textbox" spellcheck="true" aria-multiline="true" aria-label="Post text" contenteditable="true" aria-autocomplete="list" data-testid="tweetTextarea_0" no-focustrapview-refocus="true" aria-describedby="placeholder-bf4eo" aria-controls="typeaheadDropdownWrapped-8" class="notranslate public-DraftEditor-content" aria-activedescendant="typeaheadFocus-0.12133633962791812">…</div> aka get_by_test_id("primaryColumn").get_by_test_id("tweetTextarea_0")

Call log:
  - waiting for locator("[data-testid=\"tweetTextarea_0\"]") to be visible

2025-07-25 17:46:49 | ERROR    | src.core.browser_manager:close_all:392 | 关闭浏览器失败: Event loop is closed
2025-07-25 17:51:11 | ERROR    | src.modules.posting.executor:_publish_post:436 | 发布帖子失败: Locator.click: Timeout 30000ms exceeded.
Call log:
  - waiting for locator("[data-testid=\"tweetButtonInline\"]")
    - locator resolved to <button disabled role="button" tabindex="-1" type="button" aria-disabled="true" data-testid="tweetButtonInline" class="css-175oi2r r-sdzlij r-1phboty r-rs99b7 r-lrvibr r-1cwvpvk r-2yi16 r-1qi8awa r-3pj75a r-o7ynqc r-6416eg r-icoktb r-1ny4l3l">…</button>
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is not enabled
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is not enabled
    - retrying click action
      - waiting 100ms
    57 × waiting for element to be visible, enabled and stable
       - element is not enabled
     - retrying click action
       - waiting 500ms

2025-07-25 17:51:11 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, Locator.click: Timeout 30000ms exceeded.
Call log:
  - waiting for locator("[data-testid=\"tweetButtonInline\"]")
    - locator resolved to <button disabled role="button" tabindex="-1" type="button" aria-disabled="true" data-testid="tweetButtonInline" class="css-175oi2r r-sdzlij r-1phboty r-rs99b7 r-lrvibr r-1cwvpvk r-2yi16 r-1qi8awa r-3pj75a r-o7ynqc r-6416eg r-icoktb r-1ny4l3l">…</button>
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is not enabled
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is not enabled
    - retrying click action
      - waiting 100ms
    57 × waiting for element to be visible, enabled and stable
       - element is not enabled
     - retrying click action
       - waiting 500ms

2025-07-25 17:52:14 | ERROR    | src.modules.posting.executor:_publish_post:436 | 发布帖子失败: Locator.click: Timeout 30000ms exceeded.
Call log:
  - waiting for locator("[data-testid=\"tweetButtonInline\"]")
    - locator resolved to <button disabled role="button" tabindex="-1" type="button" aria-disabled="true" data-testid="tweetButtonInline" class="css-175oi2r r-sdzlij r-1phboty r-rs99b7 r-lrvibr r-1cwvpvk r-2yi16 r-1qi8awa r-3pj75a r-o7ynqc r-6416eg r-icoktb r-1ny4l3l">…</button>
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is not enabled
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is not enabled
    - retrying click action
      - waiting 100ms
    58 × waiting for element to be visible, enabled and stable
       - element is not enabled
     - retrying click action
       - waiting 500ms

2025-07-25 17:52:14 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, Locator.click: Timeout 30000ms exceeded.
Call log:
  - waiting for locator("[data-testid=\"tweetButtonInline\"]")
    - locator resolved to <button disabled role="button" tabindex="-1" type="button" aria-disabled="true" data-testid="tweetButtonInline" class="css-175oi2r r-sdzlij r-1phboty r-rs99b7 r-lrvibr r-1cwvpvk r-2yi16 r-1qi8awa r-3pj75a r-o7ynqc r-6416eg r-icoktb r-1ny4l3l">…</button>
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is not enabled
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is not enabled
    - retrying click action
      - waiting 100ms
    58 × waiting for element to be visible, enabled and stable
       - element is not enabled
     - retrying click action
       - waiting 500ms

2025-07-25 17:54:04 | ERROR    | src.core.browser_manager:close_all:392 | 关闭浏览器失败: Event loop is closed
2025-07-25 18:15:57 | ERROR    | src.modules.posting.executor:_publish_post:465 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态
2025-07-25 18:15:57 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, 发布按钮在等待时间内未变为可用状态
2025-07-25 18:17:10 | ERROR    | src.modules.posting.executor:_publish_post:465 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态
2025-07-25 18:17:10 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, 发布按钮在等待时间内未变为可用状态
2025-07-25 18:18:15 | ERROR    | src.core.browser_manager:close_all:392 | 关闭浏览器失败: Event loop is closed
2025-07-25 22:38:54 | ERROR    | src.modules.posting.executor:_publish_post:511 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-25 22:38:54 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-25 22:40:00 | ERROR    | src.modules.posting.executor:_publish_post:511 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-25 22:40:00 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-25 22:41:00 | ERROR    | src.core.browser_manager:close_all:392 | 关闭浏览器失败: Event loop is closed
2025-07-26 03:38:15 | ERROR    | src.modules.posting.executor:_publish_post:511 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 03:38:15 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 03:39:28 | ERROR    | src.modules.posting.executor:_publish_post:511 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 03:39:28 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 03:42:22 | ERROR    | src.core.browser_manager:close_all:392 | 关闭浏览器失败: Event loop is closed
2025-07-26 03:42:53 | ERROR    | __main__:handle_exception:332 | 未捕获的异常: AttributeError: 'PostingWidget' object has no attribute 'update_status'
堆栈跟踪:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\小美ai助手的源码\yyu11\src\ui\widgets\posting_widget.py", line 382, in stop_posting
    self.update_status("正在停止发帖任务...")
AttributeError: 'PostingWidget' object has no attribute 'update_status'

2025-07-26 03:43:43 | ERROR    | src.modules.posting.executor:_publish_post:511 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 03:43:43 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 03:43:58 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:43:58 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:43:58 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:43:58 | ERROR    | src.core.browser_manager:get_browser_context:85 | 创建浏览器上下文最终失败: PaigeBaldw49027
2025-07-26 03:43:58 | ERROR    | src.core.browser_manager:get_page:408 | 无法获取浏览器上下文: PaigeBaldw49027
2025-07-26 03:43:58 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, 获取页面失败
2025-07-26 03:43:58 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:43:58 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:43:58 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:43:58 | ERROR    | src.core.browser_manager:get_browser_context:85 | 创建浏览器上下文最终失败: CharleneCa2057
2025-07-26 03:43:58 | ERROR    | src.core.browser_manager:get_page:408 | 无法获取浏览器上下文: CharleneCa2057
2025-07-26 03:43:58 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, 获取页面失败
2025-07-26 03:44:14 | ERROR    | src.core.browser_manager:close_all:392 | 关闭浏览器失败: Event loop is closed
2025-07-26 03:47:45 | ERROR    | src.modules.posting.executor:_publish_post:511 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 03:47:45 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 03:48:13 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:48:13 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:48:13 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:48:13 | ERROR    | src.core.browser_manager:get_browser_context:85 | 创建浏览器上下文最终失败: PaigeBaldw49027
2025-07-26 03:48:13 | ERROR    | src.core.browser_manager:get_page:419 | 无法获取浏览器上下文: PaigeBaldw49027
2025-07-26 03:48:13 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, 获取页面失败
2025-07-26 03:48:13 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:48:13 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:48:13 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:48:13 | ERROR    | src.core.browser_manager:get_browser_context:85 | 创建浏览器上下文最终失败: CharleneCa2057
2025-07-26 03:48:13 | ERROR    | src.core.browser_manager:get_page:419 | 无法获取浏览器上下文: CharleneCa2057
2025-07-26 03:48:13 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, 获取页面失败
2025-07-26 03:50:27 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:50:27 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:50:27 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:50:27 | ERROR    | src.core.browser_manager:get_browser_context:85 | 创建浏览器上下文最终失败: PaigeBaldw49027
2025-07-26 03:50:27 | ERROR    | src.core.browser_manager:get_page:419 | 无法获取浏览器上下文: PaigeBaldw49027
2025-07-26 03:50:27 | ERROR    | src.modules.posting.executor:execute_post:395 | 发帖失败: PaigeBaldw49027, 获取页面失败
2025-07-26 03:50:27 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:50:27 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:50:27 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:50:27 | ERROR    | src.core.browser_manager:get_browser_context:85 | 创建浏览器上下文最终失败: CharleneCa2057
2025-07-26 03:50:27 | ERROR    | src.core.browser_manager:get_page:419 | 无法获取浏览器上下文: CharleneCa2057
2025-07-26 03:50:27 | ERROR    | src.modules.posting.executor:execute_post:395 | 发帖失败: CharleneCa2057, 获取页面失败
2025-07-26 03:53:54 | ERROR    | src.modules.posting.executor:_publish_post:511 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 03:53:54 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 03:54:50 | ERROR    | src.modules.posting.executor:_publish_post:511 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 03:54:50 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 03:55:00 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:55:00 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:55:00 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:55:00 | ERROR    | src.core.browser_manager:get_browser_context:85 | 创建浏览器上下文最终失败: PaigeBaldw49027
2025-07-26 03:55:00 | ERROR    | src.core.browser_manager:get_page:408 | 无法获取浏览器上下文: PaigeBaldw49027
2025-07-26 03:55:00 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, 获取页面失败
2025-07-26 03:55:00 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:55:00 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:55:00 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 03:55:00 | ERROR    | src.core.browser_manager:get_browser_context:85 | 创建浏览器上下文最终失败: CharleneCa2057
2025-07-26 03:55:00 | ERROR    | src.core.browser_manager:get_page:408 | 无法获取浏览器上下文: CharleneCa2057
2025-07-26 03:55:00 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, 获取页面失败
2025-07-26 03:55:09 | ERROR    | src.core.browser_manager:close_all:392 | 关闭浏览器失败: Event loop is closed
2025-07-26 03:59:57 | ERROR    | src.modules.posting.executor:_publish_post:542 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 03:59:57 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 04:00:58 | ERROR    | src.modules.posting.executor:_publish_post:542 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 04:00:58 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 04:01:11 | ERROR    | src.core.browser_manager:_create_browser_context:200 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 04:01:11 | ERROR    | src.core.browser_manager:_create_browser_context:200 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 04:01:11 | ERROR    | src.core.browser_manager:_create_browser_context:200 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 04:01:11 | ERROR    | src.core.browser_manager:get_browser_context:102 | 创建浏览器上下文最终失败: PaigeBaldw49027
2025-07-26 04:01:11 | ERROR    | src.core.browser_manager:get_page:438 | 无法获取浏览器上下文: PaigeBaldw49027
2025-07-26 04:01:11 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, 获取页面失败
2025-07-26 04:01:11 | ERROR    | src.core.browser_manager:_create_browser_context:200 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 04:01:11 | ERROR    | src.core.browser_manager:_create_browser_context:200 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 04:01:11 | ERROR    | src.core.browser_manager:_create_browser_context:200 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 04:01:11 | ERROR    | src.core.browser_manager:get_browser_context:102 | 创建浏览器上下文最终失败: CharleneCa2057
2025-07-26 04:01:11 | ERROR    | src.core.browser_manager:get_page:438 | 无法获取浏览器上下文: CharleneCa2057
2025-07-26 04:01:11 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, 获取页面失败
2025-07-26 04:05:42 | ERROR    | src.modules.posting.executor:_publish_post:542 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 04:05:42 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 04:07:01 | ERROR    | src.modules.posting.executor:_publish_post:542 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 04:07:01 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 04:07:11 | ERROR    | src.core.browser_manager:_create_browser_context:215 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 04:07:11 | ERROR    | src.core.browser_manager:_create_browser_context:215 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 04:07:11 | ERROR    | src.core.browser_manager:_create_browser_context:215 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 04:07:11 | ERROR    | src.core.browser_manager:get_browser_context:117 | 创建浏览器上下文最终失败: PaigeBaldw49027
2025-07-26 04:07:11 | ERROR    | src.core.browser_manager:get_page:471 | 无法获取浏览器上下文: PaigeBaldw49027
2025-07-26 04:07:11 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, 获取页面失败
2025-07-26 04:07:11 | ERROR    | src.core.browser_manager:_create_browser_context:215 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 04:07:11 | ERROR    | src.core.browser_manager:_create_browser_context:215 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 04:07:11 | ERROR    | src.core.browser_manager:_create_browser_context:215 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 04:07:11 | ERROR    | src.core.browser_manager:get_browser_context:117 | 创建浏览器上下文最终失败: CharleneCa2057
2025-07-26 04:07:11 | ERROR    | src.core.browser_manager:get_page:471 | 无法获取浏览器上下文: CharleneCa2057
2025-07-26 04:07:11 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, 获取页面失败
2025-07-26 04:15:59 | ERROR    | src.modules.posting.executor:_publish_post:511 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 04:15:59 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 04:17:00 | ERROR    | src.modules.posting.executor:_publish_post:511 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 04:17:00 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 04:17:15 | ERROR    | src.core.browser_manager:_create_browser_context:188 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 04:17:15 | ERROR    | src.core.browser_manager:_create_browser_context:188 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 04:17:15 | ERROR    | src.core.browser_manager:_create_browser_context:188 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 04:17:15 | ERROR    | src.core.browser_manager:get_browser_context:90 | 创建浏览器上下文最终失败: PaigeBaldw49027
2025-07-26 04:17:15 | ERROR    | src.core.browser_manager:get_page:432 | 无法获取浏览器上下文: PaigeBaldw49027
2025-07-26 04:17:15 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, 获取页面失败
2025-07-26 04:17:15 | ERROR    | src.core.browser_manager:_create_browser_context:188 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 04:17:15 | ERROR    | src.core.browser_manager:_create_browser_context:188 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 04:17:15 | ERROR    | src.core.browser_manager:_create_browser_context:188 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: 'NoneType' object has no attribute 'send'
2025-07-26 04:17:15 | ERROR    | src.core.browser_manager:get_browser_context:90 | 创建浏览器上下文最终失败: CharleneCa2057
2025-07-26 04:17:15 | ERROR    | src.core.browser_manager:get_page:432 | 无法获取浏览器上下文: CharleneCa2057
2025-07-26 04:17:15 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, 获取页面失败
2025-07-26 04:26:19 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: The future belongs to a different loop than the one specified as the loop argument
2025-07-26 04:26:19 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: The future belongs to a different loop than the one specified as the loop argument
2025-07-26 04:26:19 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: The future belongs to a different loop than the one specified as the loop argument
2025-07-26 04:26:19 | ERROR    | src.core.browser_manager:get_browser_context:85 | 创建浏览器上下文最终失败: PaigeBaldw49027
2025-07-26 04:26:19 | ERROR    | src.core.browser_manager:get_page:408 | 无法获取浏览器上下文: PaigeBaldw49027
2025-07-26 04:26:19 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, 获取页面失败
2025-07-26 04:26:19 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: The future belongs to a different loop than the one specified as the loop argument
2025-07-26 04:26:19 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: The future belongs to a different loop than the one specified as the loop argument
2025-07-26 04:26:19 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: The future belongs to a different loop than the one specified as the loop argument
2025-07-26 04:26:19 | ERROR    | src.core.browser_manager:get_browser_context:85 | 创建浏览器上下文最终失败: CharleneCa2057
2025-07-26 04:26:19 | ERROR    | src.core.browser_manager:get_page:408 | 无法获取浏览器上下文: CharleneCa2057
2025-07-26 04:26:19 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, 获取页面失败
2025-07-26 04:26:35 | ERROR    | src.core.browser_manager:close_all:392 | 关闭浏览器失败: Task <Task pending name='Task-6' coro=<BrowserPool.close_all() running at C:\Users\<USER>\Desktop\小美ai助手的源码\yyu11\src\core\browser_manager.py:384> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-07-26 04:27:19 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: The future belongs to a different loop than the one specified as the loop argument
2025-07-26 04:27:19 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: The future belongs to a different loop than the one specified as the loop argument
2025-07-26 04:27:19 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: The future belongs to a different loop than the one specified as the loop argument
2025-07-26 04:27:19 | ERROR    | src.core.browser_manager:get_browser_context:85 | 创建浏览器上下文最终失败: PaigeBaldw49027
2025-07-26 04:27:19 | ERROR    | src.core.browser_manager:get_page:408 | 无法获取浏览器上下文: PaigeBaldw49027
2025-07-26 04:27:19 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, 获取页面失败
2025-07-26 04:27:19 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: The future belongs to a different loop than the one specified as the loop argument
2025-07-26 04:27:19 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: The future belongs to a different loop than the one specified as the loop argument
2025-07-26 04:27:19 | ERROR    | src.core.browser_manager:_create_browser_context:179 | 创建浏览器上下文失败: BrowserType.launch_persistent_context: The future belongs to a different loop than the one specified as the loop argument
2025-07-26 04:27:19 | ERROR    | src.core.browser_manager:get_browser_context:85 | 创建浏览器上下文最终失败: CharleneCa2057
2025-07-26 04:27:19 | ERROR    | src.core.browser_manager:get_page:408 | 无法获取浏览器上下文: CharleneCa2057
2025-07-26 04:27:19 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, 获取页面失败
2025-07-26 04:27:30 | ERROR    | src.core.browser_manager:close_all:392 | 关闭浏览器失败: Task <Task pending name='Task-6' coro=<BrowserPool.close_all() running at C:\Users\<USER>\Desktop\小美ai助手的源码\yyu11\src\core\browser_manager.py:384> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-07-26 04:34:15 | ERROR    | src.modules.posting.executor:_publish_post:511 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 04:34:15 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: PaigeBaldw49027, 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 04:35:18 | ERROR    | src.modules.posting.executor:_publish_post:511 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 04:35:18 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, 发布按钮在等待时间内未变为可用状态，使用选择器: [data-testid="tweetButtonInline"]
2025-07-26 05:39:56 | ERROR    | src.modules.posting.content_editor_widget:init_content_system:139 | 新系统初始化失败: name 'DatabaseManager' is not defined
2025-07-26 05:41:15 | ERROR    | src.modules.posting.content_editor_widget:init_content_system:139 | 新系统初始化失败: name 'DatabaseManager' is not defined
2025-07-26 05:43:17 | ERROR    | src.modules.posting.content_editor_widget:init_content_system:139 | 新系统初始化失败: name 'DatabaseManager' is not defined
2025-07-26 05:46:17 | ERROR    | src.modules.posting.content_editor_widget:load_template_list:963 | 加载模板列表失败: 'TemplateSelector' object has no attribute 'get_all_templates'
2025-07-26 05:46:17 | ERROR    | src.modules.posting.content_editor_widget:load_personalization_settings:1056 | 加载个性化设置失败: a coroutine was expected, got {}
2025-07-26 05:48:42 | ERROR    | src.modules.posting.content_editor_widget:generate_smart_content:558 | 启动内容生成失败: __init__() got an unexpected keyword argument 'content_type'
2025-07-26 05:48:45 | ERROR    | src.modules.posting.content_editor_widget:preview_smart_content:633 | 预览失败: __init__() got an unexpected keyword argument 'content_type'
2025-07-26 05:48:53 | ERROR    | __main__:handle_exception:382 | 未捕获的异常: NameError: name 'QInputDialog' is not defined
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\小美ai助手的源码\yyu11\src\modules\posting\content_editor_widget.py", line 821, in insert_variable
    variable, ok = QInputDialog.getItem(
NameError: name 'QInputDialog' is not defined

2025-07-26 05:48:54 | ERROR    | __main__:handle_exception:382 | 未捕获的异常: NameError: name 'QInputDialog' is not defined
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\小美ai助手的源码\yyu11\src\modules\posting\content_editor_widget.py", line 821, in insert_variable
    variable, ok = QInputDialog.getItem(
NameError: name 'QInputDialog' is not defined

2025-07-26 05:48:54 | ERROR    | __main__:handle_exception:382 | 未捕获的异常: NameError: name 'QInputDialog' is not defined
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\小美ai助手的源码\yyu11\src\modules\posting\content_editor_widget.py", line 821, in insert_variable
    variable, ok = QInputDialog.getItem(
NameError: name 'QInputDialog' is not defined

2025-07-26 05:48:54 | ERROR    | __main__:handle_exception:382 | 未捕获的异常: NameError: name 'QInputDialog' is not defined
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\小美ai助手的源码\yyu11\src\modules\posting\content_editor_widget.py", line 821, in insert_variable
    variable, ok = QInputDialog.getItem(
NameError: name 'QInputDialog' is not defined

2025-07-26 05:48:55 | ERROR    | __main__:handle_exception:382 | 未捕获的异常: NameError: name 'QInputDialog' is not defined
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\小美ai助手的源码\yyu11\src\modules\posting\content_editor_widget.py", line 821, in insert_variable
    variable, ok = QInputDialog.getItem(
NameError: name 'QInputDialog' is not defined

2025-07-26 05:48:55 | ERROR    | __main__:handle_exception:382 | 未捕获的异常: NameError: name 'QInputDialog' is not defined
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\小美ai助手的源码\yyu11\src\modules\posting\content_editor_widget.py", line 821, in insert_variable
    variable, ok = QInputDialog.getItem(
NameError: name 'QInputDialog' is not defined

2025-07-26 05:48:57 | ERROR    | __main__:handle_exception:382 | 未捕获的异常: NameError: name 'QInputDialog' is not defined
堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\小美ai助手的源码\yyu11\src\modules\posting\content_editor_widget.py", line 821, in insert_variable
    variable, ok = QInputDialog.getItem(
NameError: name 'QInputDialog' is not defined

2025-07-26 05:48:59 | ERROR    | src.modules.posting.quality_assessor:assess_content:271 | 内容质量评估失败: invalid group reference 1 at position 9
2025-07-26 05:48:59 | ERROR    | src.modules.posting.content_editor_widget:check_content_quality:874 | 质量检查失败: An asyncio.Future, a coroutine or an awaitable is required
2025-07-26 15:47:59 | ERROR    | src.ui.widgets.posting_widget:load_groups:441 | 加载分组失败: 'AccountManager' object has no attribute 'get_groups'
2025-07-26 15:49:50 | ERROR    | src.ui.widgets.posting_widget:load_groups:441 | 加载分组失败: 'AccountGroup' object has no attribute 'get'
2025-07-26 16:21:58 | ERROR    | src.ui.widgets.posting_widget:update_template_stats:582 | 更新文案库统计失败: 'TemplateManager' object has no attribute 'get_template_stats'
2025-07-26 16:22:03 | ERROR    | src.ui.widgets.posting_widget:open_template_management:556 | 打开文案库管理失败: 'TemplateLibraryDialog' object has no attribute 'template_updated'
2025-07-26 16:23:21 | ERROR    | src.ui.dialogs.template_library_dialog:load_templates:180 | 加载文案模板失败: 'PostingWidget' object has no attribute 'load_templates'
2025-07-26 16:24:42 | ERROR    | src.ui.dialogs.template_library_dialog:save_template:265 | 保存文案失败: 'PostingWidget' object has no attribute 'add_template'
2025-07-26 16:25:21 | ERROR    | src.ui.dialogs.template_library_dialog:load_templates:180 | 加载文案模板失败: 'PostingWidget' object has no attribute 'load_templates'
2025-07-26 17:10:12 | ERROR    | src.ui.main_window:open_variable_manager:384 | 打开变量管理失败: 'SimpleVariablesDialog' object has no attribute 'exec_'
2025-07-26 17:10:23 | ERROR    | src.ui.main_window:open_variable_manager:384 | 打开变量管理失败: 'SimpleVariablesDialog' object has no attribute 'exec_'
2025-07-26 21:20:01 | ERROR    | src.modules.posting.content_manager:process_template:81 | 处理模板失败: expected string or bytes-like object
2025-07-26 21:20:01 | ERROR    | src.modules.posting.workflow:_create_posting_task:209 | 创建发帖任务失败: 'dict' object has no attribute 'strip'
2025-07-26 21:20:01 | ERROR    | src.modules.posting.content_manager:process_template:81 | 处理模板失败: expected string or bytes-like object
2025-07-26 21:20:01 | ERROR    | src.modules.posting.workflow:_create_posting_task:209 | 创建发帖任务失败: 'dict' object has no attribute 'strip'
2025-07-26 21:21:05 | ERROR    | src.modules.posting.content_manager:process_template:81 | 处理模板失败: expected string or bytes-like object
2025-07-26 21:21:05 | ERROR    | src.modules.posting.workflow:_create_posting_task:209 | 创建发帖任务失败: 'dict' object has no attribute 'strip'
2025-07-26 21:21:05 | ERROR    | src.modules.posting.content_manager:process_template:81 | 处理模板失败: expected string or bytes-like object
2025-07-26 21:21:05 | ERROR    | src.modules.posting.workflow:_create_posting_task:209 | 创建发帖任务失败: 'dict' object has no attribute 'strip'
2025-07-26 23:36:26 | ERROR    | src.modules.posting.workflow:_execute_single_posting_task_by_id:366 | 执行发帖任务失败: name 'MediaFile' is not defined
2025-07-26 23:48:00 | ERROR    | src.modules.posting.executor:_upload_media_files:418 | 上传媒体文件失败: Locator.click: Timeout 30000ms exceeded.
Call log:
  - waiting for locator("[data-testid=\"attachments\"]")

2025-07-26 23:48:00 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, Locator.click: Timeout 30000ms exceeded.
Call log:
  - waiting for locator("[data-testid=\"attachments\"]")

2025-07-26 23:52:27 | ERROR    | src.modules.posting.executor:_publish_post:593 | 发布帖子失败: Locator.click: Timeout 30000ms exceeded.
Call log:
  - waiting for locator("[role=\"button\"]:has-text(\"Post\")").first
    - locator resolved to <button role="button" tabindex="-1" type="button" aria-hidden="true" aria-label="New posts are available. Push the period key to go to the them." class="css-175oi2r r-l5o3uw r-sdzlij r-y3da5r r-1777fci r-cnw61z r-3pj75a r-1loqt21 r-o7ynqc r-6416eg r-1ny4l3l">…</button>
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <div data-testid="mask" class="css-175oi2r r-1p0dtai r-1d2f490 r-1xcajam r-zchlnj r-ipm5af r-1ffj0ar"></div> from <div id="layers" class="r-zchlnj r-1d2f490 r-u8s1d r-ipm5af">…</div> subtree intercepts pointer events
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <div data-testid="mask" class="css-175oi2r r-1p0dtai r-1d2f490 r-1xcajam r-zchlnj r-ipm5af r-1ffj0ar"></div> from <div id="layers" class="r-zchlnj r-1d2f490 r-u8s1d r-ipm5af">…</div> subtree intercepts pointer events
    - retrying click action
      - waiting 100ms
    57 × waiting for element to be visible, enabled and stable
       - element is visible, enabled and stable
       - scrolling into view if needed
       - done scrolling
       - <div data-testid="mask" class="css-175oi2r r-1p0dtai r-1d2f490 r-1xcajam r-zchlnj r-ipm5af r-1ffj0ar"></div> from <div id="layers" class="r-zchlnj r-1d2f490 r-u8s1d r-ipm5af">…</div> subtree intercepts pointer events
     - retrying click action
       - waiting 500ms

2025-07-26 23:52:27 | ERROR    | src.modules.posting.executor:execute_post:334 | 发帖失败: CharleneCa2057, Locator.click: Timeout 30000ms exceeded.
Call log:
  - waiting for locator("[role=\"button\"]:has-text(\"Post\")").first
    - locator resolved to <button role="button" tabindex="-1" type="button" aria-hidden="true" aria-label="New posts are available. Push the period key to go to the them." class="css-175oi2r r-l5o3uw r-sdzlij r-y3da5r r-1777fci r-cnw61z r-3pj75a r-1loqt21 r-o7ynqc r-6416eg r-1ny4l3l">…</button>
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <div data-testid="mask" class="css-175oi2r r-1p0dtai r-1d2f490 r-1xcajam r-zchlnj r-ipm5af r-1ffj0ar"></div> from <div id="layers" class="r-zchlnj r-1d2f490 r-u8s1d r-ipm5af">…</div> subtree intercepts pointer events
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <div data-testid="mask" class="css-175oi2r r-1p0dtai r-1d2f490 r-1xcajam r-zchlnj r-ipm5af r-1ffj0ar"></div> from <div id="layers" class="r-zchlnj r-1d2f490 r-u8s1d r-ipm5af">…</div> subtree intercepts pointer events
    - retrying click action
      - waiting 100ms
    57 × waiting for element to be visible, enabled and stable
       - element is visible, enabled and stable
       - scrolling into view if needed
       - done scrolling
       - <div data-testid="mask" class="css-175oi2r r-1p0dtai r-1d2f490 r-1xcajam r-zchlnj r-ipm5af r-1ffj0ar"></div> from <div id="layers" class="r-zchlnj r-1d2f490 r-u8s1d r-ipm5af">…</div> subtree intercepts pointer events
     - retrying click action
       - waiting 500ms

2025-07-28 06:03:08 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x9d1af3+62339]
	GetHandleVerifier [0x0x9d1b34+62404]
	(No symbol) [0x0x812123]
	(No symbol) [0x0x839693]
	(No symbol) [0x0x83ae20]
	(No symbol) [0x0x835f5a]
	(No symbol) [0x0x889782]
	(No symbol) [0x0x88926c]
	(No symbol) [0x0x88a960]
	(No symbol) [0x0x88a76a]
	(No symbol) [0x0x87f1b6]
	(No symbol) [0x0x84e7a2]
	(No symbol) [0x0x84f644]
	GetHandleVerifier [0x0xc46683+2637587]
	GetHandleVerifier [0x0xc41a8a+2618138]
	GetHandleVerifier [0x0x9f856a+220666]
	GetHandleVerifier [0x0x9e8998+156200]
	GetHandleVerifier [0x0x9ef12d+182717]
	GetHandleVerifier [0x0x9d9a38+94920]
	GetHandleVerifier [0x0x9d9bc2+95314]
	GetHandleVerifier [0x0x9c4d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 06:03:09 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x9d1af3+62339]
	GetHandleVerifier [0x0x9d1b34+62404]
	(No symbol) [0x0x812123]
	(No symbol) [0x0x839693]
	(No symbol) [0x0x83ae20]
	(No symbol) [0x0x835f5a]
	(No symbol) [0x0x889782]
	(No symbol) [0x0x88926c]
	(No symbol) [0x0x88a960]
	(No symbol) [0x0x88a76a]
	(No symbol) [0x0x87f1b6]
	(No symbol) [0x0x84e7a2]
	(No symbol) [0x0x84f644]
	GetHandleVerifier [0x0xc46683+2637587]
	GetHandleVerifier [0x0xc41a8a+2618138]
	GetHandleVerifier [0x0x9f856a+220666]
	GetHandleVerifier [0x0x9e8998+156200]
	GetHandleVerifier [0x0x9ef12d+182717]
	GetHandleVerifier [0x0x9d9a38+94920]
	GetHandleVerifier [0x0x9d9bc2+95314]
	GetHandleVerifier [0x0x9c4d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 06:03:09 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x9d1af3+62339]
	GetHandleVerifier [0x0x9d1b34+62404]
	(No symbol) [0x0x812123]
	(No symbol) [0x0x839693]
	(No symbol) [0x0x83ae20]
	(No symbol) [0x0x835f5a]
	(No symbol) [0x0x889782]
	(No symbol) [0x0x88926c]
	(No symbol) [0x0x88a960]
	(No symbol) [0x0x88a76a]
	(No symbol) [0x0x87f1b6]
	(No symbol) [0x0x84e7a2]
	(No symbol) [0x0x84f644]
	GetHandleVerifier [0x0xc46683+2637587]
	GetHandleVerifier [0x0xc41a8a+2618138]
	GetHandleVerifier [0x0x9f856a+220666]
	GetHandleVerifier [0x0x9e8998+156200]
	GetHandleVerifier [0x0x9ef12d+182717]
	GetHandleVerifier [0x0x9d9a38+94920]
	GetHandleVerifier [0x0x9d9bc2+95314]
	GetHandleVerifier [0x0x9c4d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 06:03:09 | ERROR    | src.core.browser_manager:get_driver:147 | 创建WebDriver最终失败: PaigeBaldw49027
2025-07-28 06:03:09 | ERROR    | src.modules.posting.executor:execute_post:368 | 发帖失败: PaigeBaldw49027, 获取WebDriver失败
2025-07-28 16:33:08 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0xef1af3+62339]
	GetHandleVerifier [0x0xef1b34+62404]
	(No symbol) [0x0xd32123]
	(No symbol) [0x0xd59693]
	(No symbol) [0x0xd5ae20]
	(No symbol) [0x0xd55f5a]
	(No symbol) [0x0xda9782]
	(No symbol) [0x0xda926c]
	(No symbol) [0x0xdaa960]
	(No symbol) [0x0xdaa76a]
	(No symbol) [0x0xd9f1b6]
	(No symbol) [0x0xd6e7a2]
	(No symbol) [0x0xd6f644]
	GetHandleVerifier [0x0x1166683+2637587]
	GetHandleVerifier [0x0x1161a8a+2618138]
	GetHandleVerifier [0x0xf1856a+220666]
	GetHandleVerifier [0x0xf08998+156200]
	GetHandleVerifier [0x0xf0f12d+182717]
	GetHandleVerifier [0x0xef9a38+94920]
	GetHandleVerifier [0x0xef9bc2+95314]
	GetHandleVerifier [0x0xee4d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:33:09 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0xef1af3+62339]
	GetHandleVerifier [0x0xef1b34+62404]
	(No symbol) [0x0xd32123]
	(No symbol) [0x0xd59693]
	(No symbol) [0x0xd5ae20]
	(No symbol) [0x0xd55f5a]
	(No symbol) [0x0xda9782]
	(No symbol) [0x0xda926c]
	(No symbol) [0x0xdaa960]
	(No symbol) [0x0xdaa76a]
	(No symbol) [0x0xd9f1b6]
	(No symbol) [0x0xd6e7a2]
	(No symbol) [0x0xd6f644]
	GetHandleVerifier [0x0x1166683+2637587]
	GetHandleVerifier [0x0x1161a8a+2618138]
	GetHandleVerifier [0x0xf1856a+220666]
	GetHandleVerifier [0x0xf08998+156200]
	GetHandleVerifier [0x0xf0f12d+182717]
	GetHandleVerifier [0x0xef9a38+94920]
	GetHandleVerifier [0x0xef9bc2+95314]
	GetHandleVerifier [0x0xee4d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:33:09 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0xef1af3+62339]
	GetHandleVerifier [0x0xef1b34+62404]
	(No symbol) [0x0xd32123]
	(No symbol) [0x0xd59693]
	(No symbol) [0x0xd5ae20]
	(No symbol) [0x0xd55f5a]
	(No symbol) [0x0xda9782]
	(No symbol) [0x0xda926c]
	(No symbol) [0x0xdaa960]
	(No symbol) [0x0xdaa76a]
	(No symbol) [0x0xd9f1b6]
	(No symbol) [0x0xd6e7a2]
	(No symbol) [0x0xd6f644]
	GetHandleVerifier [0x0x1166683+2637587]
	GetHandleVerifier [0x0x1161a8a+2618138]
	GetHandleVerifier [0x0xf1856a+220666]
	GetHandleVerifier [0x0xf08998+156200]
	GetHandleVerifier [0x0xf0f12d+182717]
	GetHandleVerifier [0x0xef9a38+94920]
	GetHandleVerifier [0x0xef9bc2+95314]
	GetHandleVerifier [0x0xee4d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:33:09 | ERROR    | src.core.browser_manager:get_driver:147 | 创建WebDriver最终失败: PaigeBaldw49027
2025-07-28 16:33:09 | ERROR    | src.modules.posting.executor:execute_post:368 | 发帖失败: PaigeBaldw49027, 获取WebDriver失败
2025-07-28 16:38:31 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0xa81af3+62339]
	GetHandleVerifier [0x0xa81b34+62404]
	(No symbol) [0x0x8c2123]
	(No symbol) [0x0x8e9693]
	(No symbol) [0x0x8eae20]
	(No symbol) [0x0x8e5f5a]
	(No symbol) [0x0x939782]
	(No symbol) [0x0x93926c]
	(No symbol) [0x0x93a960]
	(No symbol) [0x0x93a76a]
	(No symbol) [0x0x92f1b6]
	(No symbol) [0x0x8fe7a2]
	(No symbol) [0x0x8ff644]
	GetHandleVerifier [0x0xcf6683+2637587]
	GetHandleVerifier [0x0xcf1a8a+2618138]
	GetHandleVerifier [0x0xaa856a+220666]
	GetHandleVerifier [0x0xa98998+156200]
	GetHandleVerifier [0x0xa9f12d+182717]
	GetHandleVerifier [0x0xa89a38+94920]
	GetHandleVerifier [0x0xa89bc2+95314]
	GetHandleVerifier [0x0xa74d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:38:31 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0xa81af3+62339]
	GetHandleVerifier [0x0xa81b34+62404]
	(No symbol) [0x0x8c2123]
	(No symbol) [0x0x8e9693]
	(No symbol) [0x0x8eae20]
	(No symbol) [0x0x8e5f5a]
	(No symbol) [0x0x939782]
	(No symbol) [0x0x93926c]
	(No symbol) [0x0x93a960]
	(No symbol) [0x0x93a76a]
	(No symbol) [0x0x92f1b6]
	(No symbol) [0x0x8fe7a2]
	(No symbol) [0x0x8ff644]
	GetHandleVerifier [0x0xcf6683+2637587]
	GetHandleVerifier [0x0xcf1a8a+2618138]
	GetHandleVerifier [0x0xaa856a+220666]
	GetHandleVerifier [0x0xa98998+156200]
	GetHandleVerifier [0x0xa9f12d+182717]
	GetHandleVerifier [0x0xa89a38+94920]
	GetHandleVerifier [0x0xa89bc2+95314]
	GetHandleVerifier [0x0xa74d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:38:32 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0xa81af3+62339]
	GetHandleVerifier [0x0xa81b34+62404]
	(No symbol) [0x0x8c2123]
	(No symbol) [0x0x8e9693]
	(No symbol) [0x0x8eae20]
	(No symbol) [0x0x8e5f5a]
	(No symbol) [0x0x939782]
	(No symbol) [0x0x93926c]
	(No symbol) [0x0x93a960]
	(No symbol) [0x0x93a76a]
	(No symbol) [0x0x92f1b6]
	(No symbol) [0x0x8fe7a2]
	(No symbol) [0x0x8ff644]
	GetHandleVerifier [0x0xcf6683+2637587]
	GetHandleVerifier [0x0xcf1a8a+2618138]
	GetHandleVerifier [0x0xaa856a+220666]
	GetHandleVerifier [0x0xa98998+156200]
	GetHandleVerifier [0x0xa9f12d+182717]
	GetHandleVerifier [0x0xa89a38+94920]
	GetHandleVerifier [0x0xa89bc2+95314]
	GetHandleVerifier [0x0xa74d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:38:32 | ERROR    | src.core.browser_manager:get_driver:147 | 创建WebDriver最终失败: PaigeBaldw49027
2025-07-28 16:38:32 | ERROR    | src.modules.posting.executor:execute_post:368 | 发帖失败: PaigeBaldw49027, 获取WebDriver失败
2025-07-28 16:38:52 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x551af3+62339]
	GetHandleVerifier [0x0x551b34+62404]
	(No symbol) [0x0x392123]
	(No symbol) [0x0x3b9693]
	(No symbol) [0x0x3bae20]
	(No symbol) [0x0x3b5f5a]
	(No symbol) [0x0x409782]
	(No symbol) [0x0x40926c]
	(No symbol) [0x0x40a960]
	(No symbol) [0x0x40a76a]
	(No symbol) [0x0x3ff1b6]
	(No symbol) [0x0x3ce7a2]
	(No symbol) [0x0x3cf644]
	GetHandleVerifier [0x0x7c6683+2637587]
	GetHandleVerifier [0x0x7c1a8a+2618138]
	GetHandleVerifier [0x0x57856a+220666]
	GetHandleVerifier [0x0x568998+156200]
	GetHandleVerifier [0x0x56f12d+182717]
	GetHandleVerifier [0x0x559a38+94920]
	GetHandleVerifier [0x0x559bc2+95314]
	GetHandleVerifier [0x0x544d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:38:53 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x551af3+62339]
	GetHandleVerifier [0x0x551b34+62404]
	(No symbol) [0x0x392123]
	(No symbol) [0x0x3b9693]
	(No symbol) [0x0x3bae20]
	(No symbol) [0x0x3b5f5a]
	(No symbol) [0x0x409782]
	(No symbol) [0x0x40926c]
	(No symbol) [0x0x40a960]
	(No symbol) [0x0x40a76a]
	(No symbol) [0x0x3ff1b6]
	(No symbol) [0x0x3ce7a2]
	(No symbol) [0x0x3cf644]
	GetHandleVerifier [0x0x7c6683+2637587]
	GetHandleVerifier [0x0x7c1a8a+2618138]
	GetHandleVerifier [0x0x57856a+220666]
	GetHandleVerifier [0x0x568998+156200]
	GetHandleVerifier [0x0x56f12d+182717]
	GetHandleVerifier [0x0x559a38+94920]
	GetHandleVerifier [0x0x559bc2+95314]
	GetHandleVerifier [0x0x544d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:38:53 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x551af3+62339]
	GetHandleVerifier [0x0x551b34+62404]
	(No symbol) [0x0x392123]
	(No symbol) [0x0x3b9693]
	(No symbol) [0x0x3bae20]
	(No symbol) [0x0x3b5f5a]
	(No symbol) [0x0x409782]
	(No symbol) [0x0x40926c]
	(No symbol) [0x0x40a960]
	(No symbol) [0x0x40a76a]
	(No symbol) [0x0x3ff1b6]
	(No symbol) [0x0x3ce7a2]
	(No symbol) [0x0x3cf644]
	GetHandleVerifier [0x0x7c6683+2637587]
	GetHandleVerifier [0x0x7c1a8a+2618138]
	GetHandleVerifier [0x0x57856a+220666]
	GetHandleVerifier [0x0x568998+156200]
	GetHandleVerifier [0x0x56f12d+182717]
	GetHandleVerifier [0x0x559a38+94920]
	GetHandleVerifier [0x0x559bc2+95314]
	GetHandleVerifier [0x0x544d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:38:53 | ERROR    | src.core.browser_manager:get_driver:147 | 创建WebDriver最终失败: CharleneCa2057
2025-07-28 16:38:53 | ERROR    | src.modules.posting.executor:execute_post:368 | 发帖失败: CharleneCa2057, 获取WebDriver失败
2025-07-28 16:47:34 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x561af3+62339]
	GetHandleVerifier [0x0x561b34+62404]
	(No symbol) [0x0x3a2123]
	(No symbol) [0x0x3c9693]
	(No symbol) [0x0x3cae20]
	(No symbol) [0x0x3c5f5a]
	(No symbol) [0x0x419782]
	(No symbol) [0x0x41926c]
	(No symbol) [0x0x41a960]
	(No symbol) [0x0x41a76a]
	(No symbol) [0x0x40f1b6]
	(No symbol) [0x0x3de7a2]
	(No symbol) [0x0x3df644]
	GetHandleVerifier [0x0x7d6683+2637587]
	GetHandleVerifier [0x0x7d1a8a+2618138]
	GetHandleVerifier [0x0x58856a+220666]
	GetHandleVerifier [0x0x578998+156200]
	GetHandleVerifier [0x0x57f12d+182717]
	GetHandleVerifier [0x0x569a38+94920]
	GetHandleVerifier [0x0x569bc2+95314]
	GetHandleVerifier [0x0x554d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:47:34 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x561af3+62339]
	GetHandleVerifier [0x0x561b34+62404]
	(No symbol) [0x0x3a2123]
	(No symbol) [0x0x3c9693]
	(No symbol) [0x0x3cae20]
	(No symbol) [0x0x3c5f5a]
	(No symbol) [0x0x419782]
	(No symbol) [0x0x41926c]
	(No symbol) [0x0x41a960]
	(No symbol) [0x0x41a76a]
	(No symbol) [0x0x40f1b6]
	(No symbol) [0x0x3de7a2]
	(No symbol) [0x0x3df644]
	GetHandleVerifier [0x0x7d6683+2637587]
	GetHandleVerifier [0x0x7d1a8a+2618138]
	GetHandleVerifier [0x0x58856a+220666]
	GetHandleVerifier [0x0x578998+156200]
	GetHandleVerifier [0x0x57f12d+182717]
	GetHandleVerifier [0x0x569a38+94920]
	GetHandleVerifier [0x0x569bc2+95314]
	GetHandleVerifier [0x0x554d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:47:34 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x561af3+62339]
	GetHandleVerifier [0x0x561b34+62404]
	(No symbol) [0x0x3a2123]
	(No symbol) [0x0x3c9693]
	(No symbol) [0x0x3cae20]
	(No symbol) [0x0x3c5f5a]
	(No symbol) [0x0x419782]
	(No symbol) [0x0x41926c]
	(No symbol) [0x0x41a960]
	(No symbol) [0x0x41a76a]
	(No symbol) [0x0x40f1b6]
	(No symbol) [0x0x3de7a2]
	(No symbol) [0x0x3df644]
	GetHandleVerifier [0x0x7d6683+2637587]
	GetHandleVerifier [0x0x7d1a8a+2618138]
	GetHandleVerifier [0x0x58856a+220666]
	GetHandleVerifier [0x0x578998+156200]
	GetHandleVerifier [0x0x57f12d+182717]
	GetHandleVerifier [0x0x569a38+94920]
	GetHandleVerifier [0x0x569bc2+95314]
	GetHandleVerifier [0x0x554d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:47:34 | ERROR    | src.core.browser_manager:get_driver:147 | 创建WebDriver最终失败: EWebb32471
2025-07-28 16:47:34 | ERROR    | src.modules.posting.executor:login_account:193 | 登录异常: EWebb32471 (尝试 1/3), 无法获取WebDriver
2025-07-28 16:47:44 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x561af3+62339]
	GetHandleVerifier [0x0x561b34+62404]
	(No symbol) [0x0x3a2123]
	(No symbol) [0x0x3c9693]
	(No symbol) [0x0x3cae20]
	(No symbol) [0x0x3c5f5a]
	(No symbol) [0x0x419782]
	(No symbol) [0x0x41926c]
	(No symbol) [0x0x41a960]
	(No symbol) [0x0x41a76a]
	(No symbol) [0x0x40f1b6]
	(No symbol) [0x0x3de7a2]
	(No symbol) [0x0x3df644]
	GetHandleVerifier [0x0x7d6683+2637587]
	GetHandleVerifier [0x0x7d1a8a+2618138]
	GetHandleVerifier [0x0x58856a+220666]
	GetHandleVerifier [0x0x578998+156200]
	GetHandleVerifier [0x0x57f12d+182717]
	GetHandleVerifier [0x0x569a38+94920]
	GetHandleVerifier [0x0x569bc2+95314]
	GetHandleVerifier [0x0x554d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:47:44 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x561af3+62339]
	GetHandleVerifier [0x0x561b34+62404]
	(No symbol) [0x0x3a2123]
	(No symbol) [0x0x3c9693]
	(No symbol) [0x0x3cae20]
	(No symbol) [0x0x3c5f5a]
	(No symbol) [0x0x419782]
	(No symbol) [0x0x41926c]
	(No symbol) [0x0x41a960]
	(No symbol) [0x0x41a76a]
	(No symbol) [0x0x40f1b6]
	(No symbol) [0x0x3de7a2]
	(No symbol) [0x0x3df644]
	GetHandleVerifier [0x0x7d6683+2637587]
	GetHandleVerifier [0x0x7d1a8a+2618138]
	GetHandleVerifier [0x0x58856a+220666]
	GetHandleVerifier [0x0x578998+156200]
	GetHandleVerifier [0x0x57f12d+182717]
	GetHandleVerifier [0x0x569a38+94920]
	GetHandleVerifier [0x0x569bc2+95314]
	GetHandleVerifier [0x0x554d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:47:45 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x561af3+62339]
	GetHandleVerifier [0x0x561b34+62404]
	(No symbol) [0x0x3a2123]
	(No symbol) [0x0x3c9693]
	(No symbol) [0x0x3cae20]
	(No symbol) [0x0x3c5f5a]
	(No symbol) [0x0x419782]
	(No symbol) [0x0x41926c]
	(No symbol) [0x0x41a960]
	(No symbol) [0x0x41a76a]
	(No symbol) [0x0x40f1b6]
	(No symbol) [0x0x3de7a2]
	(No symbol) [0x0x3df644]
	GetHandleVerifier [0x0x7d6683+2637587]
	GetHandleVerifier [0x0x7d1a8a+2618138]
	GetHandleVerifier [0x0x58856a+220666]
	GetHandleVerifier [0x0x578998+156200]
	GetHandleVerifier [0x0x57f12d+182717]
	GetHandleVerifier [0x0x569a38+94920]
	GetHandleVerifier [0x0x569bc2+95314]
	GetHandleVerifier [0x0x554d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:47:45 | ERROR    | src.core.browser_manager:get_driver:147 | 创建WebDriver最终失败: EWebb32471
2025-07-28 16:47:45 | ERROR    | src.modules.posting.executor:login_account:193 | 登录异常: EWebb32471 (尝试 2/3), 无法获取WebDriver
2025-07-28 16:47:55 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x561af3+62339]
	GetHandleVerifier [0x0x561b34+62404]
	(No symbol) [0x0x3a2123]
	(No symbol) [0x0x3c9693]
	(No symbol) [0x0x3cae20]
	(No symbol) [0x0x3c5f5a]
	(No symbol) [0x0x419782]
	(No symbol) [0x0x41926c]
	(No symbol) [0x0x41a960]
	(No symbol) [0x0x41a76a]
	(No symbol) [0x0x40f1b6]
	(No symbol) [0x0x3de7a2]
	(No symbol) [0x0x3df644]
	GetHandleVerifier [0x0x7d6683+2637587]
	GetHandleVerifier [0x0x7d1a8a+2618138]
	GetHandleVerifier [0x0x58856a+220666]
	GetHandleVerifier [0x0x578998+156200]
	GetHandleVerifier [0x0x57f12d+182717]
	GetHandleVerifier [0x0x569a38+94920]
	GetHandleVerifier [0x0x569bc2+95314]
	GetHandleVerifier [0x0x554d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:47:55 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x561af3+62339]
	GetHandleVerifier [0x0x561b34+62404]
	(No symbol) [0x0x3a2123]
	(No symbol) [0x0x3c9693]
	(No symbol) [0x0x3cae20]
	(No symbol) [0x0x3c5f5a]
	(No symbol) [0x0x419782]
	(No symbol) [0x0x41926c]
	(No symbol) [0x0x41a960]
	(No symbol) [0x0x41a76a]
	(No symbol) [0x0x40f1b6]
	(No symbol) [0x0x3de7a2]
	(No symbol) [0x0x3df644]
	GetHandleVerifier [0x0x7d6683+2637587]
	GetHandleVerifier [0x0x7d1a8a+2618138]
	GetHandleVerifier [0x0x58856a+220666]
	GetHandleVerifier [0x0x578998+156200]
	GetHandleVerifier [0x0x57f12d+182717]
	GetHandleVerifier [0x0x569a38+94920]
	GetHandleVerifier [0x0x569bc2+95314]
	GetHandleVerifier [0x0x554d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:47:55 | ERROR    | src.core.browser_manager:_create_driver:285 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x561af3+62339]
	GetHandleVerifier [0x0x561b34+62404]
	(No symbol) [0x0x3a2123]
	(No symbol) [0x0x3c9693]
	(No symbol) [0x0x3cae20]
	(No symbol) [0x0x3c5f5a]
	(No symbol) [0x0x419782]
	(No symbol) [0x0x41926c]
	(No symbol) [0x0x41a960]
	(No symbol) [0x0x41a76a]
	(No symbol) [0x0x40f1b6]
	(No symbol) [0x0x3de7a2]
	(No symbol) [0x0x3df644]
	GetHandleVerifier [0x0x7d6683+2637587]
	GetHandleVerifier [0x0x7d1a8a+2618138]
	GetHandleVerifier [0x0x58856a+220666]
	GetHandleVerifier [0x0x578998+156200]
	GetHandleVerifier [0x0x57f12d+182717]
	GetHandleVerifier [0x0x569a38+94920]
	GetHandleVerifier [0x0x569bc2+95314]
	GetHandleVerifier [0x0x554d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:47:55 | ERROR    | src.core.browser_manager:get_driver:147 | 创建WebDriver最终失败: EWebb32471
2025-07-28 16:47:55 | ERROR    | src.modules.posting.executor:login_account:193 | 登录异常: EWebb32471 (尝试 3/3), 无法获取WebDriver
2025-07-28 16:53:58 | ERROR    | src.core.browser_manager:create_driver:251 | 创建Chrome实例失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: useAutomationExtension
Stacktrace:
	GetHandleVerifier [0x0x411af3+62339]
	GetHandleVerifier [0x0x411b34+62404]
	(No symbol) [0x0x252123]
	(No symbol) [0x0x279693]
	(No symbol) [0x0x27ae20]
	(No symbol) [0x0x275f5a]
	(No symbol) [0x0x2c9782]
	(No symbol) [0x0x2c926c]
	(No symbol) [0x0x2ca960]
	(No symbol) [0x0x2ca76a]
	(No symbol) [0x0x2bf1b6]
	(No symbol) [0x0x28e7a2]
	(No symbol) [0x0x28f644]
	GetHandleVerifier [0x0x686683+2637587]
	GetHandleVerifier [0x0x681a8a+2618138]
	GetHandleVerifier [0x0x43856a+220666]
	GetHandleVerifier [0x0x428998+156200]
	GetHandleVerifier [0x0x42f12d+182717]
	GetHandleVerifier [0x0x419a38+94920]
	GetHandleVerifier [0x0x419bc2+95314]
	GetHandleVerifier [0x0x404d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:53:58 | ERROR    | src.core.browser_manager:_create_driver:278 | 创建WebDriver失败: you cannot reuse the ChromeOptions object
2025-07-28 16:53:59 | ERROR    | src.core.browser_manager:create_driver:251 | 创建Chrome实例失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: useAutomationExtension
Stacktrace:
	GetHandleVerifier [0x0x411af3+62339]
	GetHandleVerifier [0x0x411b34+62404]
	(No symbol) [0x0x252123]
	(No symbol) [0x0x279693]
	(No symbol) [0x0x27ae20]
	(No symbol) [0x0x275f5a]
	(No symbol) [0x0x2c9782]
	(No symbol) [0x0x2c926c]
	(No symbol) [0x0x2ca960]
	(No symbol) [0x0x2ca76a]
	(No symbol) [0x0x2bf1b6]
	(No symbol) [0x0x28e7a2]
	(No symbol) [0x0x28f644]
	GetHandleVerifier [0x0x686683+2637587]
	GetHandleVerifier [0x0x681a8a+2618138]
	GetHandleVerifier [0x0x43856a+220666]
	GetHandleVerifier [0x0x428998+156200]
	GetHandleVerifier [0x0x42f12d+182717]
	GetHandleVerifier [0x0x419a38+94920]
	GetHandleVerifier [0x0x419bc2+95314]
	GetHandleVerifier [0x0x404d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:53:59 | ERROR    | src.core.browser_manager:_create_driver:278 | 创建WebDriver失败: you cannot reuse the ChromeOptions object
2025-07-28 16:53:59 | ERROR    | src.core.browser_manager:create_driver:251 | 创建Chrome实例失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: useAutomationExtension
Stacktrace:
	GetHandleVerifier [0x0x411af3+62339]
	GetHandleVerifier [0x0x411b34+62404]
	(No symbol) [0x0x252123]
	(No symbol) [0x0x279693]
	(No symbol) [0x0x27ae20]
	(No symbol) [0x0x275f5a]
	(No symbol) [0x0x2c9782]
	(No symbol) [0x0x2c926c]
	(No symbol) [0x0x2ca960]
	(No symbol) [0x0x2ca76a]
	(No symbol) [0x0x2bf1b6]
	(No symbol) [0x0x28e7a2]
	(No symbol) [0x0x28f644]
	GetHandleVerifier [0x0x686683+2637587]
	GetHandleVerifier [0x0x681a8a+2618138]
	GetHandleVerifier [0x0x43856a+220666]
	GetHandleVerifier [0x0x428998+156200]
	GetHandleVerifier [0x0x42f12d+182717]
	GetHandleVerifier [0x0x419a38+94920]
	GetHandleVerifier [0x0x419bc2+95314]
	GetHandleVerifier [0x0x404d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:53:59 | ERROR    | src.core.browser_manager:_create_driver:278 | 创建WebDriver失败: you cannot reuse the ChromeOptions object
2025-07-28 16:53:59 | ERROR    | src.core.browser_manager:get_driver:147 | 创建WebDriver最终失败: DavidMarti65523
2025-07-28 16:53:59 | ERROR    | src.modules.posting.executor:login_account:193 | 登录异常: DavidMarti65523 (尝试 1/3), 无法获取WebDriver
2025-07-28 16:54:04 | ERROR    | src.modules.posting.executor:login_account:193 | 登录异常: EWebb32471 (尝试 1/3), Task <Task pending name='Task-2' coro=<AccountLoginThread.run.<locals>.login_accounts() running at D:\yaoya\Documents\chonggou\yyy1 - 副本\src\ui\widgets\account_widget.py:130> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-07-28 16:54:05 | ERROR    | src.core.browser_manager:create_driver:251 | 创建Chrome实例失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: useAutomationExtension
Stacktrace:
	GetHandleVerifier [0x0x411af3+62339]
	GetHandleVerifier [0x0x411b34+62404]
	(No symbol) [0x0x252123]
	(No symbol) [0x0x279693]
	(No symbol) [0x0x27ae20]
	(No symbol) [0x0x275f5a]
	(No symbol) [0x0x2c9782]
	(No symbol) [0x0x2c926c]
	(No symbol) [0x0x2ca960]
	(No symbol) [0x0x2ca76a]
	(No symbol) [0x0x2bf1b6]
	(No symbol) [0x0x28e7a2]
	(No symbol) [0x0x28f644]
	GetHandleVerifier [0x0x686683+2637587]
	GetHandleVerifier [0x0x681a8a+2618138]
	GetHandleVerifier [0x0x43856a+220666]
	GetHandleVerifier [0x0x428998+156200]
	GetHandleVerifier [0x0x42f12d+182717]
	GetHandleVerifier [0x0x419a38+94920]
	GetHandleVerifier [0x0x419bc2+95314]
	GetHandleVerifier [0x0x404d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:54:05 | ERROR    | src.core.browser_manager:_create_driver:278 | 创建WebDriver失败: you cannot reuse the ChromeOptions object
2025-07-28 16:54:06 | ERROR    | src.core.browser_manager:create_driver:251 | 创建Chrome实例失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: useAutomationExtension
Stacktrace:
	GetHandleVerifier [0x0x411af3+62339]
	GetHandleVerifier [0x0x411b34+62404]
	(No symbol) [0x0x252123]
	(No symbol) [0x0x279693]
	(No symbol) [0x0x27ae20]
	(No symbol) [0x0x275f5a]
	(No symbol) [0x0x2c9782]
	(No symbol) [0x0x2c926c]
	(No symbol) [0x0x2ca960]
	(No symbol) [0x0x2ca76a]
	(No symbol) [0x0x2bf1b6]
	(No symbol) [0x0x28e7a2]
	(No symbol) [0x0x28f644]
	GetHandleVerifier [0x0x686683+2637587]
	GetHandleVerifier [0x0x681a8a+2618138]
	GetHandleVerifier [0x0x43856a+220666]
	GetHandleVerifier [0x0x428998+156200]
	GetHandleVerifier [0x0x42f12d+182717]
	GetHandleVerifier [0x0x419a38+94920]
	GetHandleVerifier [0x0x419bc2+95314]
	GetHandleVerifier [0x0x404d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:54:06 | ERROR    | src.core.browser_manager:_create_driver:278 | 创建WebDriver失败: you cannot reuse the ChromeOptions object
2025-07-28 16:54:07 | ERROR    | src.core.browser_manager:create_driver:251 | 创建Chrome实例失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: useAutomationExtension
Stacktrace:
	GetHandleVerifier [0x0x411af3+62339]
	GetHandleVerifier [0x0x411b34+62404]
	(No symbol) [0x0x252123]
	(No symbol) [0x0x279693]
	(No symbol) [0x0x27ae20]
	(No symbol) [0x0x275f5a]
	(No symbol) [0x0x2c9782]
	(No symbol) [0x0x2c926c]
	(No symbol) [0x0x2ca960]
	(No symbol) [0x0x2ca76a]
	(No symbol) [0x0x2bf1b6]
	(No symbol) [0x0x28e7a2]
	(No symbol) [0x0x28f644]
	GetHandleVerifier [0x0x686683+2637587]
	GetHandleVerifier [0x0x681a8a+2618138]
	GetHandleVerifier [0x0x43856a+220666]
	GetHandleVerifier [0x0x428998+156200]
	GetHandleVerifier [0x0x42f12d+182717]
	GetHandleVerifier [0x0x419a38+94920]
	GetHandleVerifier [0x0x419bc2+95314]
	GetHandleVerifier [0x0x404d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:54:07 | ERROR    | src.core.browser_manager:_create_driver:278 | 创建WebDriver失败: you cannot reuse the ChromeOptions object
2025-07-28 16:54:07 | ERROR    | src.core.browser_manager:get_driver:147 | 创建WebDriver最终失败: DavidMarti65523
2025-07-28 16:54:07 | ERROR    | src.modules.posting.executor:login_account:193 | 登录异常: DavidMarti65523 (尝试 2/3), 无法获取WebDriver
2025-07-28 16:54:12 | ERROR    | src.modules.posting.executor:login_account:193 | 登录异常: EWebb32471 (尝试 2/3), Task <Task pending name='Task-2' coro=<AccountLoginThread.run.<locals>.login_accounts() running at D:\yaoya\Documents\chonggou\yyy1 - 副本\src\ui\widgets\account_widget.py:130> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-07-28 16:54:14 | ERROR    | src.core.browser_manager:create_driver:251 | 创建Chrome实例失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: useAutomationExtension
Stacktrace:
	GetHandleVerifier [0x0x411af3+62339]
	GetHandleVerifier [0x0x411b34+62404]
	(No symbol) [0x0x252123]
	(No symbol) [0x0x279693]
	(No symbol) [0x0x27ae20]
	(No symbol) [0x0x275f5a]
	(No symbol) [0x0x2c9782]
	(No symbol) [0x0x2c926c]
	(No symbol) [0x0x2ca960]
	(No symbol) [0x0x2ca76a]
	(No symbol) [0x0x2bf1b6]
	(No symbol) [0x0x28e7a2]
	(No symbol) [0x0x28f644]
	GetHandleVerifier [0x0x686683+2637587]
	GetHandleVerifier [0x0x681a8a+2618138]
	GetHandleVerifier [0x0x43856a+220666]
	GetHandleVerifier [0x0x428998+156200]
	GetHandleVerifier [0x0x42f12d+182717]
	GetHandleVerifier [0x0x419a38+94920]
	GetHandleVerifier [0x0x419bc2+95314]
	GetHandleVerifier [0x0x404d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:54:14 | ERROR    | src.core.browser_manager:_create_driver:278 | 创建WebDriver失败: you cannot reuse the ChromeOptions object
2025-07-28 16:54:15 | ERROR    | src.core.browser_manager:create_driver:251 | 创建Chrome实例失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: useAutomationExtension
Stacktrace:
	GetHandleVerifier [0x0x411af3+62339]
	GetHandleVerifier [0x0x411b34+62404]
	(No symbol) [0x0x252123]
	(No symbol) [0x0x279693]
	(No symbol) [0x0x27ae20]
	(No symbol) [0x0x275f5a]
	(No symbol) [0x0x2c9782]
	(No symbol) [0x0x2c926c]
	(No symbol) [0x0x2ca960]
	(No symbol) [0x0x2ca76a]
	(No symbol) [0x0x2bf1b6]
	(No symbol) [0x0x28e7a2]
	(No symbol) [0x0x28f644]
	GetHandleVerifier [0x0x686683+2637587]
	GetHandleVerifier [0x0x681a8a+2618138]
	GetHandleVerifier [0x0x43856a+220666]
	GetHandleVerifier [0x0x428998+156200]
	GetHandleVerifier [0x0x42f12d+182717]
	GetHandleVerifier [0x0x419a38+94920]
	GetHandleVerifier [0x0x419bc2+95314]
	GetHandleVerifier [0x0x404d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:54:15 | ERROR    | src.core.browser_manager:_create_driver:278 | 创建WebDriver失败: you cannot reuse the ChromeOptions object
2025-07-28 16:54:16 | ERROR    | src.core.browser_manager:create_driver:251 | 创建Chrome实例失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: useAutomationExtension
Stacktrace:
	GetHandleVerifier [0x0x411af3+62339]
	GetHandleVerifier [0x0x411b34+62404]
	(No symbol) [0x0x252123]
	(No symbol) [0x0x279693]
	(No symbol) [0x0x27ae20]
	(No symbol) [0x0x275f5a]
	(No symbol) [0x0x2c9782]
	(No symbol) [0x0x2c926c]
	(No symbol) [0x0x2ca960]
	(No symbol) [0x0x2ca76a]
	(No symbol) [0x0x2bf1b6]
	(No symbol) [0x0x28e7a2]
	(No symbol) [0x0x28f644]
	GetHandleVerifier [0x0x686683+2637587]
	GetHandleVerifier [0x0x681a8a+2618138]
	GetHandleVerifier [0x0x43856a+220666]
	GetHandleVerifier [0x0x428998+156200]
	GetHandleVerifier [0x0x42f12d+182717]
	GetHandleVerifier [0x0x419a38+94920]
	GetHandleVerifier [0x0x419bc2+95314]
	GetHandleVerifier [0x0x404d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 16:54:16 | ERROR    | src.core.browser_manager:_create_driver:278 | 创建WebDriver失败: you cannot reuse the ChromeOptions object
2025-07-28 16:54:16 | ERROR    | src.core.browser_manager:get_driver:147 | 创建WebDriver最终失败: DavidMarti65523
2025-07-28 16:54:16 | ERROR    | src.modules.posting.executor:login_account:193 | 登录异常: DavidMarti65523 (尝试 3/3), 无法获取WebDriver
2025-07-28 16:54:20 | ERROR    | src.modules.posting.executor:login_account:193 | 登录异常: EWebb32471 (尝试 3/3), Task <Task pending name='Task-2' coro=<AccountLoginThread.run.<locals>.login_accounts() running at D:\yaoya\Documents\chonggou\yyy1 - 副本\src\ui\widgets\account_widget.py:130> cb=[_run_until_complete_cb() at D:\python39\lib\asyncio\base_events.py:184]> got Future <Future pending> attached to a different loop
2025-07-28 17:00:05 | ERROR    | src.core.browser_manager:create_driver:275 | 创建Chrome实例失败: Message: session not created: cannot connect to chrome at 127.0.0.1:34566
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x10f1af3+62339]
	GetHandleVerifier [0x0x10f1b34+62404]
	(No symbol) [0x0xf31f80]
	(No symbol) [0x0xf25f2a]
	(No symbol) [0x0xf6adc6]
	(No symbol) [0x0xf6128f]
	(No symbol) [0x0xf610c6]
	(No symbol) [0x0xfaae77]
	(No symbol) [0x0xfaa76a]
	(No symbol) [0x0xf9f1b6]
	(No symbol) [0x0xf6e7a2]
	(No symbol) [0x0xf6f644]
	GetHandleVerifier [0x0x1366683+2637587]
	GetHandleVerifier [0x0x1361a8a+2618138]
	GetHandleVerifier [0x0x111856a+220666]
	GetHandleVerifier [0x0x1108998+156200]
	GetHandleVerifier [0x0x110f12d+182717]
	GetHandleVerifier [0x0x10f9a38+94920]
	GetHandleVerifier [0x0x10f9bc2+95314]
	GetHandleVerifier [0x0x10e4d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 17:00:05 | ERROR    | src.core.browser_manager:_create_driver:301 | 创建WebDriver失败: Message: session not created: cannot connect to chrome at 127.0.0.1:34566
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x10f1af3+62339]
	GetHandleVerifier [0x0x10f1b34+62404]
	(No symbol) [0x0xf31f80]
	(No symbol) [0x0xf25f2a]
	(No symbol) [0x0xf6adc6]
	(No symbol) [0x0xf6128f]
	(No symbol) [0x0xf610c6]
	(No symbol) [0x0xfaae77]
	(No symbol) [0x0xfaa76a]
	(No symbol) [0x0xf9f1b6]
	(No symbol) [0x0xf6e7a2]
	(No symbol) [0x0xf6f644]
	GetHandleVerifier [0x0x1366683+2637587]
	GetHandleVerifier [0x0x1361a8a+2618138]
	GetHandleVerifier [0x0x111856a+220666]
	GetHandleVerifier [0x0x1108998+156200]
	GetHandleVerifier [0x0x110f12d+182717]
	GetHandleVerifier [0x0x10f9a38+94920]
	GetHandleVerifier [0x0x10f9bc2+95314]
	GetHandleVerifier [0x0x10e4d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 17:34:56 | ERROR    | src.modules.posting.executor:login_account:193 | 登录异常: EWebb32471 (尝试 1/3), 无法找到下一步按钮
2025-07-28 17:35:11 | ERROR    | src.modules.posting.executor:login_account:193 | 登录异常: EWebb32471 (尝试 2/3), Message: invalid session id
Stacktrace:
	GetHandleVerifier [0x0x1041af3+62339]
	GetHandleVerifier [0x0x1041b34+62404]
	(No symbol) [0x0xe81f80]
	(No symbol) [0x0xebda98]
	(No symbol) [0x0xeef276]
	(No symbol) [0x0xeeae75]
	(No symbol) [0x0xeea3f6]
	(No symbol) [0x0xe53a45]
	(No symbol) [0x0xe53f9e]
	(No symbol) [0x0xe5442d]
	GetHandleVerifier [0x0x12b6683+2637587]
	GetHandleVerifier [0x0x12b1a8a+2618138]
	GetHandleVerifier [0x0x106856a+220666]
	GetHandleVerifier [0x0x1058998+156200]
	GetHandleVerifier [0x0x105f12d+182717]
	(No symbol) [0x0xe53710]
	(No symbol) [0x0xe52f1d]
	GetHandleVerifier [0x0x13ea71c+3899308]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-28 17:35:59 | ERROR    | src.modules.posting.executor:login_account:193 | 登录异常: EWebb32471 (尝试 3/3), 无法找到下一步按钮
2025-07-28 17:44:26 | ERROR    | src.modules.posting.executor:login_account:193 | 登录异常: EWebb32471 (尝试 1/3), 无法找到密码输入框
2025-07-28 17:44:43 | ERROR    | src.modules.posting.executor:login_account:193 | 登录异常: EWebb32471 (尝试 2/3), Message: Driver已关闭

2025-07-28 17:50:35 | ERROR    | src.modules.posting.executor:login_account:251 | 登录异常: EWebb32471 (尝试 1/3), 无法找到密码输入框
2025-07-28 17:55:33 | ERROR    | src.modules.posting.executor:login_account:251 | 登录异常: EWebb32471 (尝试 2/3), 无法找到密码输入框
2025-07-28 17:56:47 | ERROR    | src.modules.posting.executor:login_account:251 | 登录异常: EWebb32471 (尝试 3/3), 无法找到密码输入框
2025-07-28 19:23:52 | ERROR    | src.core.browser_manager:create_driver:451 | 创建Chrome实例最终失败: you cannot reuse the ChromeOptions object
2025-07-28 19:23:52 | ERROR    | src.core.browser_manager:_create_driver:477 | 创建WebDriver失败: you cannot reuse the ChromeOptions object
2025-07-28 19:25:42 | ERROR    | src.core.browser_manager:create_driver:451 | 创建Chrome实例最终失败: you cannot reuse the ChromeOptions object
2025-07-28 19:25:42 | ERROR    | src.core.browser_manager:_create_driver:477 | 创建WebDriver失败: you cannot reuse the ChromeOptions object
2025-07-28 19:27:26 | ERROR    | src.core.browser_manager:create_driver:451 | 创建Chrome实例最终失败: you cannot reuse the ChromeOptions object
2025-07-28 19:27:26 | ERROR    | src.core.browser_manager:_create_driver:477 | 创建WebDriver失败: you cannot reuse the ChromeOptions object
2025-07-28 19:27:26 | ERROR    | src.core.browser_manager:get_driver:164 | 创建WebDriver最终失败: MercedesFl59831
2025-07-28 19:27:26 | ERROR    | src.modules.posting.executor:login_account:360 | 登录异常: MercedesFl59831 (尝试 1/3), 无法获取WebDriver
2025-07-28 19:29:18 | ERROR    | src.core.browser_manager:create_driver:451 | 创建Chrome实例最终失败: you cannot reuse the ChromeOptions object
2025-07-28 19:29:18 | ERROR    | src.core.browser_manager:_create_driver:477 | 创建WebDriver失败: you cannot reuse the ChromeOptions object
2025-07-28 19:30:40 | ERROR    | src.core.browser_manager:create_driver:451 | 创建Chrome实例最终失败: you cannot reuse the ChromeOptions object
2025-07-28 19:30:40 | ERROR    | src.core.browser_manager:_create_driver:477 | 创建WebDriver失败: you cannot reuse the ChromeOptions object
2025-07-28 19:30:40 | ERROR    | src.core.browser_manager:_create_driver:477 | 创建WebDriver失败: cannot schedule new futures after shutdown
2025-07-28 19:55:48 | ERROR    | src.modules.posting.executor:login_account:360 | 登录异常: MercedesFl59831 (尝试 1/3), Message: Driver已关闭

2025-07-28 22:30:44 | ERROR    | src.modules.posting.executor:_publish_post:1021 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态，已尝试所有选择器
2025-07-28 22:30:44 | ERROR    | src.modules.posting.executor:execute_post:636 | 发帖失败: MercedesFl59831, 发布按钮在等待时间内未变为可用状态，已尝试所有选择器
2025-07-28 22:51:09 | ERROR    | src.modules.posting.executor:_verify_media_upload:1020 | ❌ 没有检测到已上传的媒体文件
2025-07-28 22:51:58 | ERROR    | src.modules.posting.executor:_publish_post:1244 | 发布帖子失败: 发布按钮在等待时间内未变为可用状态，已尝试所有选择器
2025-07-28 22:51:58 | ERROR    | src.modules.posting.executor:execute_post:646 | 发帖失败: MercedesFl59831, 发布按钮在等待时间内未变为可用状态，已尝试所有选择器
2025-07-29 01:01:17 | ERROR    | src.modules.posting.executor:_verify_media_upload:1356 | ❌ 没有检测到已上传的媒体文件
2025-07-29 01:54:03 | ERROR    | src.modules.posting.executor:_verify_media_upload:1381 | ❌ 没有检测到已上传的媒体文件
2025-07-29 01:55:34 | ERROR    | src.core.anti_detection:enhanced_safe_click:854 | 所有点击尝试都失败: [data-testid="tweetButton"]
2025-07-29 01:59:38 | ERROR    | src.modules.posting.executor:_verify_media_upload:1381 | ❌ 没有检测到已上传的媒体文件
2025-07-29 04:05:03 | ERROR    | src.modules.posting.executor:_upload_media_files:1376 | 上传媒体文件失败: 强制使用逐个上传模式
2025-07-29 04:05:03 | ERROR    | src.modules.posting.executor:execute_post:650 | 发帖失败: PaigeBaldw49027, 强制使用逐个上传模式
2025-07-29 04:07:18 | ERROR    | src.modules.posting.executor:_upload_media_files:1376 | 上传媒体文件失败: 强制使用逐个上传模式
2025-07-29 04:07:18 | ERROR    | src.modules.posting.executor:execute_post:650 | 发帖失败: PaigeBaldw49027, 强制使用逐个上传模式
2025-07-29 04:10:45 | ERROR    | src.modules.posting.executor:_navigate_to_compose_page:1776 | 导航到发帖页面失败: Message: Driver已关闭

2025-07-29 04:10:45 | ERROR    | src.modules.posting.executor:execute_post:650 | 发帖失败: PaigeBaldw49027, 无法导航到发帖页面
2025-07-29 15:42:26 | ERROR    | src.modules.posting.executor:_force_text_input:840 | ❌ 强力输入仍然失败: 预期'✨✨✨✨✨✨✨
你好啊，今天很好✨值得拥有', 实际'✨✨✨✨✨✨✨
你好啊，今天很好✨值得拥✨好啊，今天很好✨得拥有有'
2025-07-29 17:15:02 | ERROR    | src.core.browser_manager:create_driver:325 | Chrome启动超时（30秒）
2025-07-29 17:16:27 | ERROR    | src.core.browser_manager:create_driver:392 | 创建Chrome实例最终失败: you cannot reuse the ChromeOptions object
2025-07-29 17:16:27 | ERROR    | src.core.browser_manager:_create_driver:418 | 创建WebDriver失败: you cannot reuse the ChromeOptions object
2025-07-29 17:16:57 | ERROR    | src.core.browser_manager:create_driver:325 | Chrome启动超时（30秒）
2025-07-29 17:18:21 | ERROR    | src.core.browser_manager:create_driver:392 | 创建Chrome实例最终失败: you cannot reuse the ChromeOptions object
2025-07-29 23:57:46 | ERROR    | src.core.browser_manager:create_chrome_fast:495 | Chrome快速启动失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x651af3+62339]
	GetHandleVerifier [0x0x651b34+62404]
	(No symbol) [0x0x492123]
	(No symbol) [0x0x4b9693]
	(No symbol) [0x0x4bae20]
	(No symbol) [0x0x4b5f5a]
	(No symbol) [0x0x509782]
	(No symbol) [0x0x50926c]
	(No symbol) [0x0x50a960]
	(No symbol) [0x0x50a76a]
	(No symbol) [0x0x4ff1b6]
	(No symbol) [0x0x4ce7a2]
	(No symbol) [0x0x4cf644]
	GetHandleVerifier [0x0x8c6683+2637587]
	GetHandleVerifier [0x0x8c1a8a+2618138]
	GetHandleVerifier [0x0x67856a+220666]
	GetHandleVerifier [0x0x668998+156200]
	GetHandleVerifier [0x0x66f12d+182717]
	GetHandleVerifier [0x0x659a38+94920]
	GetHandleVerifier [0x0x659bc2+95314]
	GetHandleVerifier [0x0x644d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-29 23:57:46 | ERROR    | src.core.browser_manager:_fast_create_driver:519 | 快速创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x651af3+62339]
	GetHandleVerifier [0x0x651b34+62404]
	(No symbol) [0x0x492123]
	(No symbol) [0x0x4b9693]
	(No symbol) [0x0x4bae20]
	(No symbol) [0x0x4b5f5a]
	(No symbol) [0x0x509782]
	(No symbol) [0x0x50926c]
	(No symbol) [0x0x50a960]
	(No symbol) [0x0x50a76a]
	(No symbol) [0x0x4ff1b6]
	(No symbol) [0x0x4ce7a2]
	(No symbol) [0x0x4cf644]
	GetHandleVerifier [0x0x8c6683+2637587]
	GetHandleVerifier [0x0x8c1a8a+2618138]
	GetHandleVerifier [0x0x67856a+220666]
	GetHandleVerifier [0x0x668998+156200]
	GetHandleVerifier [0x0x66f12d+182717]
	GetHandleVerifier [0x0x659a38+94920]
	GetHandleVerifier [0x0x659bc2+95314]
	GetHandleVerifier [0x0x644d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-29 23:57:47 | ERROR    | src.core.browser_manager:create_driver:353 | Chrome创建失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x651af3+62339]
	GetHandleVerifier [0x0x651b34+62404]
	(No symbol) [0x0x492123]
	(No symbol) [0x0x4b9693]
	(No symbol) [0x0x4bae20]
	(No symbol) [0x0x4b5f5a]
	(No symbol) [0x0x509782]
	(No symbol) [0x0x50926c]
	(No symbol) [0x0x50a960]
	(No symbol) [0x0x50a76a]
	(No symbol) [0x0x4ff1b6]
	(No symbol) [0x0x4ce7a2]
	(No symbol) [0x0x4cf644]
	GetHandleVerifier [0x0x8c6683+2637587]
	GetHandleVerifier [0x0x8c1a8a+2618138]
	GetHandleVerifier [0x0x67856a+220666]
	GetHandleVerifier [0x0x668998+156200]
	GetHandleVerifier [0x0x66f12d+182717]
	GetHandleVerifier [0x0x659a38+94920]
	GetHandleVerifier [0x0x659bc2+95314]
	GetHandleVerifier [0x0x644d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-29 23:57:47 | ERROR    | src.core.browser_manager:_create_driver:379 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x651af3+62339]
	GetHandleVerifier [0x0x651b34+62404]
	(No symbol) [0x0x492123]
	(No symbol) [0x0x4b9693]
	(No symbol) [0x0x4bae20]
	(No symbol) [0x0x4b5f5a]
	(No symbol) [0x0x509782]
	(No symbol) [0x0x50926c]
	(No symbol) [0x0x50a960]
	(No symbol) [0x0x50a76a]
	(No symbol) [0x0x4ff1b6]
	(No symbol) [0x0x4ce7a2]
	(No symbol) [0x0x4cf644]
	GetHandleVerifier [0x0x8c6683+2637587]
	GetHandleVerifier [0x0x8c1a8a+2618138]
	GetHandleVerifier [0x0x67856a+220666]
	GetHandleVerifier [0x0x668998+156200]
	GetHandleVerifier [0x0x66f12d+182717]
	GetHandleVerifier [0x0x659a38+94920]
	GetHandleVerifier [0x0x659bc2+95314]
	GetHandleVerifier [0x0x644d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-29 23:57:49 | ERROR    | src.core.browser_manager:create_driver:353 | Chrome创建失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x651af3+62339]
	GetHandleVerifier [0x0x651b34+62404]
	(No symbol) [0x0x492123]
	(No symbol) [0x0x4b9693]
	(No symbol) [0x0x4bae20]
	(No symbol) [0x0x4b5f5a]
	(No symbol) [0x0x509782]
	(No symbol) [0x0x50926c]
	(No symbol) [0x0x50a960]
	(No symbol) [0x0x50a76a]
	(No symbol) [0x0x4ff1b6]
	(No symbol) [0x0x4ce7a2]
	(No symbol) [0x0x4cf644]
	GetHandleVerifier [0x0x8c6683+2637587]
	GetHandleVerifier [0x0x8c1a8a+2618138]
	GetHandleVerifier [0x0x67856a+220666]
	GetHandleVerifier [0x0x668998+156200]
	GetHandleVerifier [0x0x66f12d+182717]
	GetHandleVerifier [0x0x659a38+94920]
	GetHandleVerifier [0x0x659bc2+95314]
	GetHandleVerifier [0x0x644d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-29 23:57:49 | ERROR    | src.core.browser_manager:_create_driver:379 | 创建WebDriver失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x651af3+62339]
	GetHandleVerifier [0x0x651b34+62404]
	(No symbol) [0x0x492123]
	(No symbol) [0x0x4b9693]
	(No symbol) [0x0x4bae20]
	(No symbol) [0x0x4b5f5a]
	(No symbol) [0x0x509782]
	(No symbol) [0x0x50926c]
	(No symbol) [0x0x50a960]
	(No symbol) [0x0x50a76a]
	(No symbol) [0x0x4ff1b6]
	(No symbol) [0x0x4ce7a2]
	(No symbol) [0x0x4cf644]
	GetHandleVerifier [0x0x8c6683+2637587]
	GetHandleVerifier [0x0x8c1a8a+2618138]
	GetHandleVerifier [0x0x67856a+220666]
	GetHandleVerifier [0x0x668998+156200]
	GetHandleVerifier [0x0x66f12d+182717]
	GetHandleVerifier [0x0x659a38+94920]
	GetHandleVerifier [0x0x659bc2+95314]
	GetHandleVerifier [0x0x644d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-29 23:57:49 | ERROR    | src.core.browser_manager:get_driver:241 | 创建WebDriver最终失败: PaigeBaldw49027
2025-07-29 23:57:49 | ERROR    | src.modules.posting.executor:execute_post:650 | 发帖失败: PaigeBaldw49027, 获取WebDriver失败
2025-07-29 23:58:10 | ERROR    | src.core.browser_manager:create_chrome_fast:495 | Chrome快速启动失败: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0x411af3+62339]
	GetHandleVerifier [0x0x411b34+62404]
	(No symbol) [0x0x252123]
	(No symbol) [0x0x279693]
	(No symbol) [0x0x27ae20]
	(No symbol) [0x0x275f5a]
	(No symbol) [0x0x2c9782]
	(No symbol) [0x0x2c926c]
	(No symbol) [0x0x2ca960]
	(No symbol) [0x0x2ca76a]
	(No symbol) [0x0x2bf1b6]
	(No symbol) [0x0x28e7a2]
	(No symbol) [0x0x28f644]
	GetHandleVerifier [0x0x686683+2637587]
	GetHandleVerifier [0x0x681a8a+2618138]
	GetHandleVerifier [0x0x43856a+220666]
	GetHandleVerifier [0x0x428998+156200]
	GetHandleVerifier [0x0x42f12d+182717]
	GetHandleVerifier [0x0x419a38+94920]
	GetHandleVerifier [0x0x419bc2+95314]
	GetHandleVerifier [0x0x404d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-30 00:43:28 | ERROR    | src.modules.posting.workflow:_create_posting_task:213 | 创建发帖任务失败: (sqlite3.OperationalError) table posting_tasks has no column named posted_group_id
[SQL: INSERT INTO posting_tasks (account_id, content, media_ids, post_url, status, scheduled_time, completed_time, error_message, posted_group_id, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: (9, '💯👍\n\n又是美好的一天\n\n你们在吗✨🎊', '[6, 9]', None, 'pending', None, None, None, None, '2025-07-30 00:43:28.976868')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-30 00:43:28 | ERROR    | src.modules.posting.workflow:_create_posting_task:213 | 创建发帖任务失败: (sqlite3.OperationalError) table posting_tasks has no column named posted_group_id
[SQL: INSERT INTO posting_tasks (account_id, content, media_ids, post_url, status, scheduled_time, completed_time, error_message, posted_group_id, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: (10, '👌🤗\n\n又是美好的一天\n\n你们在吗💫👍', '[4, 11]', None, 'pending', None, None, None, None, '2025-07-30 00:43:28.994140')]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-30 01:33:37 | ERROR    | src.core.browser_manager:create_chrome_fast:510 | Chrome快速启动失败: Message: session not created: cannot connect to chrome at 127.0.0.1:34847
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x10c1af3+62339]
	GetHandleVerifier [0x0x10c1b34+62404]
	(No symbol) [0x0xf01f80]
	(No symbol) [0x0xef5f2a]
	(No symbol) [0x0xf3adc6]
	(No symbol) [0x0xf3128f]
	(No symbol) [0x0xf310c6]
	(No symbol) [0x0xf7ae77]
	(No symbol) [0x0xf7a76a]
	(No symbol) [0x0xf6f1b6]
	(No symbol) [0x0xf3e7a2]
	(No symbol) [0x0xf3f644]
	GetHandleVerifier [0x0x1336683+2637587]
	GetHandleVerifier [0x0x1331a8a+2618138]
	GetHandleVerifier [0x0x10e856a+220666]
	GetHandleVerifier [0x0x10d8998+156200]
	GetHandleVerifier [0x0x10df12d+182717]
	GetHandleVerifier [0x0x10c9a38+94920]
	GetHandleVerifier [0x0x10c9bc2+95314]
	GetHandleVerifier [0x0x10b4d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-30 01:33:37 | ERROR    | src.core.browser_manager:_fast_create_driver:534 | 快速创建WebDriver失败: Message: session not created: cannot connect to chrome at 127.0.0.1:34847
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x10c1af3+62339]
	GetHandleVerifier [0x0x10c1b34+62404]
	(No symbol) [0x0xf01f80]
	(No symbol) [0x0xef5f2a]
	(No symbol) [0x0xf3adc6]
	(No symbol) [0x0xf3128f]
	(No symbol) [0x0xf310c6]
	(No symbol) [0x0xf7ae77]
	(No symbol) [0x0xf7a76a]
	(No symbol) [0x0xf6f1b6]
	(No symbol) [0x0xf3e7a2]
	(No symbol) [0x0xf3f644]
	GetHandleVerifier [0x0x1336683+2637587]
	GetHandleVerifier [0x0x1331a8a+2618138]
	GetHandleVerifier [0x0x10e856a+220666]
	GetHandleVerifier [0x0x10d8998+156200]
	GetHandleVerifier [0x0x10df12d+182717]
	GetHandleVerifier [0x0x10c9a38+94920]
	GetHandleVerifier [0x0x10c9bc2+95314]
	GetHandleVerifier [0x0x10b4d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-30 04:16:11 | ERROR    | src.core.browser_manager:create_chrome_fast:510 | Chrome快速启动失败: Message: session not created: cannot connect to chrome at 127.0.0.1:25862
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x711af3+62339]
	GetHandleVerifier [0x0x711b34+62404]
	(No symbol) [0x0x551f80]
	(No symbol) [0x0x545f2a]
	(No symbol) [0x0x58adc6]
	(No symbol) [0x0x58128f]
	(No symbol) [0x0x5810c6]
	(No symbol) [0x0x5cae77]
	(No symbol) [0x0x5ca76a]
	(No symbol) [0x0x5bf1b6]
	(No symbol) [0x0x58e7a2]
	(No symbol) [0x0x58f644]
	GetHandleVerifier [0x0x986683+2637587]
	GetHandleVerifier [0x0x981a8a+2618138]
	GetHandleVerifier [0x0x73856a+220666]
	GetHandleVerifier [0x0x728998+156200]
	GetHandleVerifier [0x0x72f12d+182717]
	GetHandleVerifier [0x0x719a38+94920]
	GetHandleVerifier [0x0x719bc2+95314]
	GetHandleVerifier [0x0x704d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-30 04:16:11 | ERROR    | src.core.browser_manager:_fast_create_driver:534 | 快速创建WebDriver失败: Message: session not created: cannot connect to chrome at 127.0.0.1:25862
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x711af3+62339]
	GetHandleVerifier [0x0x711b34+62404]
	(No symbol) [0x0x551f80]
	(No symbol) [0x0x545f2a]
	(No symbol) [0x0x58adc6]
	(No symbol) [0x0x58128f]
	(No symbol) [0x0x5810c6]
	(No symbol) [0x0x5cae77]
	(No symbol) [0x0x5ca76a]
	(No symbol) [0x0x5bf1b6]
	(No symbol) [0x0x58e7a2]
	(No symbol) [0x0x58f644]
	GetHandleVerifier [0x0x986683+2637587]
	GetHandleVerifier [0x0x981a8a+2618138]
	GetHandleVerifier [0x0x73856a+220666]
	GetHandleVerifier [0x0x728998+156200]
	GetHandleVerifier [0x0x72f12d+182717]
	GetHandleVerifier [0x0x719a38+94920]
	GetHandleVerifier [0x0x719bc2+95314]
	GetHandleVerifier [0x0x704d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-30 04:17:48 | ERROR    | src.core.browser_manager:create_driver:353 | Chrome创建失败: Message: session not created: cannot connect to chrome at 127.0.0.1:26019
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x301af3+62339]
	GetHandleVerifier [0x0x301b34+62404]
	(No symbol) [0x0x141f80]
	(No symbol) [0x0x135f2a]
	(No symbol) [0x0x17adc6]
	(No symbol) [0x0x17128f]
	(No symbol) [0x0x1710c6]
	(No symbol) [0x0x1bae77]
	(No symbol) [0x0x1ba76a]
	(No symbol) [0x0x1af1b6]
	(No symbol) [0x0x17e7a2]
	(No symbol) [0x0x17f644]
	GetHandleVerifier [0x0x576683+2637587]
	GetHandleVerifier [0x0x571a8a+2618138]
	GetHandleVerifier [0x0x32856a+220666]
	GetHandleVerifier [0x0x318998+156200]
	GetHandleVerifier [0x0x31f12d+182717]
	GetHandleVerifier [0x0x309a38+94920]
	GetHandleVerifier [0x0x309bc2+95314]
	GetHandleVerifier [0x0x2f4d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]
	(No symbol) [0x0]

2025-07-30 04:17:48 | ERROR    | src.core.browser_manager:_create_driver:379 | 创建WebDriver失败: Message: session not created: cannot connect to chrome at 127.0.0.1:26019
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x301af3+62339]
	GetHandleVerifier [0x0x301b34+62404]
	(No symbol) [0x0x141f80]
	(No symbol) [0x0x135f2a]
	(No symbol) [0x0x17adc6]
	(No symbol) [0x0x17128f]
	(No symbol) [0x0x1710c6]
	(No symbol) [0x0x1bae77]
	(No symbol) [0x0x1ba76a]
	(No symbol) [0x0x1af1b6]
	(No symbol) [0x0x17e7a2]
	(No symbol) [0x0x17f644]
	GetHandleVerifier [0x0x576683+2637587]
	GetHandleVerifier [0x0x571a8a+2618138]
	GetHandleVerifier [0x0x32856a+220666]
	GetHandleVerifier [0x0x318998+156200]
	GetHandleVerifier [0x0x31f12d+182717]
	GetHandleVerifier [0x0x309a38+94920]
	GetHandleVerifier [0x0x309bc2+95314]
	GetHandleVerifier [0x0x2f4d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]
	(No symbol) [0x0]

2025-07-30 04:25:29 | ERROR    | src.core.browser_manager:_fast_create_driver:584 | 快速创建WebDriver失败: you cannot reuse the ChromeOptions object
2025-07-30 04:27:07 | ERROR    | src.core.browser_manager:create_driver:370 | Chrome创建失败: Message: session not created: cannot connect to chrome at 127.0.0.1:43987
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x1221af3+62339]
	GetHandleVerifier [0x0x1221b34+62404]
	(No symbol) [0x0x1061f80]
	(No symbol) [0x0x1055f2a]
	(No symbol) [0x0x109adc6]
	(No symbol) [0x0x109128f]
	(No symbol) [0x0x10910c6]
	(No symbol) [0x0x10dae77]
	(No symbol) [0x0x10da76a]
	(No symbol) [0x0x10cf1b6]
	(No symbol) [0x0x109e7a2]
	(No symbol) [0x0x109f644]
	GetHandleVerifier [0x0x1496683+2637587]
	GetHandleVerifier [0x0x1491a8a+2618138]
	GetHandleVerifier [0x0x124856a+220666]
	GetHandleVerifier [0x0x1238998+156200]
	GetHandleVerifier [0x0x123f12d+182717]
	GetHandleVerifier [0x0x1229a38+94920]
	GetHandleVerifier [0x0x1229bc2+95314]
	GetHandleVerifier [0x0x1214d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-30 04:27:07 | ERROR    | src.core.browser_manager:_create_driver:396 | 创建WebDriver失败: Message: session not created: cannot connect to chrome at 127.0.0.1:43987
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x1221af3+62339]
	GetHandleVerifier [0x0x1221b34+62404]
	(No symbol) [0x0x1061f80]
	(No symbol) [0x0x1055f2a]
	(No symbol) [0x0x109adc6]
	(No symbol) [0x0x109128f]
	(No symbol) [0x0x10910c6]
	(No symbol) [0x0x10dae77]
	(No symbol) [0x0x10da76a]
	(No symbol) [0x0x10cf1b6]
	(No symbol) [0x0x109e7a2]
	(No symbol) [0x0x109f644]
	GetHandleVerifier [0x0x1496683+2637587]
	GetHandleVerifier [0x0x1491a8a+2618138]
	GetHandleVerifier [0x0x124856a+220666]
	GetHandleVerifier [0x0x1238998+156200]
	GetHandleVerifier [0x0x123f12d+182717]
	GetHandleVerifier [0x0x1229a38+94920]
	GetHandleVerifier [0x0x1229bc2+95314]
	GetHandleVerifier [0x0x1214d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-30 04:28:46 | ERROR    | src.core.browser_manager:create_driver:370 | Chrome创建失败: Message: session not created: cannot connect to chrome at 127.0.0.1:20726
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x1191af3+62339]
	GetHandleVerifier [0x0x1191b34+62404]
	(No symbol) [0x0xfd1f80]
	(No symbol) [0x0xfc5f2a]
	(No symbol) [0x0x100adc6]
	(No symbol) [0x0x100128f]
	(No symbol) [0x0x10010c6]
	(No symbol) [0x0x104ae77]
	(No symbol) [0x0x104a76a]
	(No symbol) [0x0x103f1b6]
	(No symbol) [0x0x100e7a2]
	(No symbol) [0x0x100f644]
	GetHandleVerifier [0x0x1406683+2637587]
	GetHandleVerifier [0x0x1401a8a+2618138]
	GetHandleVerifier [0x0x11b856a+220666]
	GetHandleVerifier [0x0x11a8998+156200]
	GetHandleVerifier [0x0x11af12d+182717]
	GetHandleVerifier [0x0x1199a38+94920]
	GetHandleVerifier [0x0x1199bc2+95314]
	GetHandleVerifier [0x0x1184d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-30 04:28:46 | ERROR    | src.core.browser_manager:_create_driver:396 | 创建WebDriver失败: Message: session not created: cannot connect to chrome at 127.0.0.1:20726
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x1191af3+62339]
	GetHandleVerifier [0x0x1191b34+62404]
	(No symbol) [0x0xfd1f80]
	(No symbol) [0x0xfc5f2a]
	(No symbol) [0x0x100adc6]
	(No symbol) [0x0x100128f]
	(No symbol) [0x0x10010c6]
	(No symbol) [0x0x104ae77]
	(No symbol) [0x0x104a76a]
	(No symbol) [0x0x103f1b6]
	(No symbol) [0x0x100e7a2]
	(No symbol) [0x0x100f644]
	GetHandleVerifier [0x0x1406683+2637587]
	GetHandleVerifier [0x0x1401a8a+2618138]
	GetHandleVerifier [0x0x11b856a+220666]
	GetHandleVerifier [0x0x11a8998+156200]
	GetHandleVerifier [0x0x11af12d+182717]
	GetHandleVerifier [0x0x1199a38+94920]
	GetHandleVerifier [0x0x1199bc2+95314]
	GetHandleVerifier [0x0x1184d0a+9626]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-30 04:28:46 | ERROR    | src.core.browser_manager:get_driver:258 | 创建WebDriver最终失败: PaigeBaldw49027
2025-07-30 04:28:46 | ERROR    | src.modules.posting.executor:execute_post:650 | 发帖失败: PaigeBaldw49027, 获取WebDriver失败
2025-07-30 04:31:57 | ERROR    | src.modules.posting.executor:_navigate_to_compose_page:2368 | 导航到发帖页面失败: Message: Driver已关闭

2025-07-30 04:31:57 | ERROR    | src.modules.posting.executor:execute_post:650 | 发帖失败: CharleneCa2057, 无法导航到发帖页面
2025-07-30 04:37:59 | ERROR    | src.modules.posting.executor:_navigate_to_compose_page:2368 | 导航到发帖页面失败: Message: Driver已关闭

2025-07-30 04:37:59 | ERROR    | src.modules.posting.executor:execute_post:650 | 发帖失败: CharleneCa2057, 无法导航到发帖页面
2025-07-30 04:46:26 | ERROR    | src.modules.posting.executor:execute_post:695 | 发帖失败: CharleneCa2057, 'PostingExecutor' object has no attribute 'cookie_manager'
2025-07-30 04:50:37 | ERROR    | src.modules.posting.executor:_smart_click_with_recovery:1820 | 所有智能点击尝试都失败
2025-07-30 04:50:37 | ERROR    | src.modules.posting.executor:_publish_post:2194 | 发布帖子失败: 所有智能点击策略都失败了
2025-07-30 04:50:37 | ERROR    | src.modules.posting.executor:execute_post:696 | 发帖失败: CharleneCa2057, 所有智能点击策略都失败了
2025-07-30 05:27:41 | ERROR    | src.modules.posting.executor:_find_element_elegantly:669 | ❌ 无法找到元素: compose_button
2025-07-30 05:27:47 | ERROR    | src.modules.posting.executor:_find_element_elegantly:669 | ❌ 无法找到元素: compose_button
2025-07-30 05:27:53 | ERROR    | src.modules.posting.executor:_find_element_elegantly:669 | ❌ 无法找到元素: compose_button
2025-07-30 05:27:53 | ERROR    | src.modules.posting.executor:execute_post:748 | ❌ 发帖失败: 无法找到发帖按钮
2025-07-30 05:30:23 | ERROR    | src.modules.posting.executor:_find_element_elegantly:691 | ❌ 无法找到元素: compose_button
2025-07-30 05:30:30 | ERROR    | src.modules.posting.executor:_find_element_elegantly:691 | ❌ 无法找到元素: compose_button
2025-07-30 05:30:36 | ERROR    | src.modules.posting.executor:_find_element_elegantly:691 | ❌ 无法找到元素: compose_button
2025-07-30 05:30:36 | ERROR    | src.modules.posting.executor:execute_post:770 | ❌ 发帖失败: 无法找到发帖按钮
2025-07-30 05:38:49 | ERROR    | src.modules.posting.executor:_find_element_elegantly:699 | ❌ 无法找到元素: media_input
2025-07-30 05:39:09 | ERROR    | src.modules.posting.executor:_find_element_elegantly:699 | ❌ 无法找到元素: media_input
2025-07-30 05:39:33 | ERROR    | src.modules.posting.executor:_find_element_elegantly:699 | ❌ 无法找到元素: media_input
2025-07-30 05:39:33 | ERROR    | src.modules.posting.executor:execute_post:821 | ❌ 发帖失败: 媒体上传失败: 无法找到媒体输入框
2025-07-30 05:42:17 | ERROR    | src.modules.posting.executor:_elegant_upload_media:1040 | 媒体上传异常: Message: unknown error: path is not absolute: data/media/com_202507171332_5d1f129e677479f74f497a1c68949daa__percentage_cropped.jpg
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0xabba83+63395]
	GetHandleVerifier [0x0xabbac4+63460]
	(No symbol) [0x0x902113]
	(No symbol) [0x0x91101d]
	(No symbol) [0x0x943a53]
	(No symbol) [0x0x96f3ac]
	(No symbol) [0x0x93ffa4]
	(No symbol) [0x0x96f624]
	(No symbol) [0x0x9907ba]
	(No symbol) [0x0x96f1a6]
	(No symbol) [0x0x93e7b2]
	(No symbol) [0x0x93f654]
	GetHandleVerifier [0x0xd38883+2672035]
	GetHandleVerifier [0x0xd33cba+2652634]
	GetHandleVerifier [0x0xae2bca+223466]
	GetHandleVerifier [0x0xad2cb8+158168]
	GetHandleVerifier [0x0xad978d+185517]
	GetHandleVerifier [0x0xac3b78+96408]
	GetHandleVerifier [0x0xac3d02+96802]
	GetHandleVerifier [0x0xaae90a+9770]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-30 05:42:37 | ERROR    | src.modules.posting.executor:_elegant_upload_media:1040 | 媒体上传异常: Message: unknown error: path is not absolute: data/media/com_202507171332_5d1f129e677479f74f497a1c68949daa__percentage_cropped.jpg
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0xabba83+63395]
	GetHandleVerifier [0x0xabbac4+63460]
	(No symbol) [0x0x902113]
	(No symbol) [0x0x91101d]
	(No symbol) [0x0x943a53]
	(No symbol) [0x0x96f3ac]
	(No symbol) [0x0x93ffa4]
	(No symbol) [0x0x96f624]
	(No symbol) [0x0x9907ba]
	(No symbol) [0x0x96f1a6]
	(No symbol) [0x0x93e7b2]
	(No symbol) [0x0x93f654]
	GetHandleVerifier [0x0xd38883+2672035]
	GetHandleVerifier [0x0xd33cba+2652634]
	GetHandleVerifier [0x0xae2bca+223466]
	GetHandleVerifier [0x0xad2cb8+158168]
	GetHandleVerifier [0x0xad978d+185517]
	GetHandleVerifier [0x0xac3b78+96408]
	GetHandleVerifier [0x0xac3d02+96802]
	GetHandleVerifier [0x0xaae90a+9770]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-30 05:42:54 | ERROR    | src.modules.posting.executor:_elegant_upload_media:1040 | 媒体上传异常: Message: unknown error: path is not absolute: data/media/com_202507171332_5d1f129e677479f74f497a1c68949daa__percentage_cropped.jpg
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0xabba83+63395]
	GetHandleVerifier [0x0xabbac4+63460]
	(No symbol) [0x0x902113]
	(No symbol) [0x0x91101d]
	(No symbol) [0x0x943a53]
	(No symbol) [0x0x96f3ac]
	(No symbol) [0x0x93ffa4]
	(No symbol) [0x0x96f624]
	(No symbol) [0x0x9907ba]
	(No symbol) [0x0x96f1a6]
	(No symbol) [0x0x93e7b2]
	(No symbol) [0x0x93f654]
	GetHandleVerifier [0x0xd38883+2672035]
	GetHandleVerifier [0x0xd33cba+2652634]
	GetHandleVerifier [0x0xae2bca+223466]
	GetHandleVerifier [0x0xad2cb8+158168]
	GetHandleVerifier [0x0xad978d+185517]
	GetHandleVerifier [0x0xac3b78+96408]
	GetHandleVerifier [0x0xac3d02+96802]
	GetHandleVerifier [0x0xaae90a+9770]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-30 05:42:54 | ERROR    | src.modules.posting.executor:execute_post:821 | ❌ 发帖失败: 媒体上传失败: 媒体上传异常: Message: unknown error: path is not absolute: data/media/com_202507171332_5d1f129e677479f74f497a1c68949daa__percentage_cropped.jpg
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0xabba83+63395]
	GetHandleVerifier [0x0xabbac4+63460]
	(No symbol) [0x0x902113]
	(No symbol) [0x0x91101d]
	(No symbol) [0x0x943a53]
	(No symbol) [0x0x96f3ac]
	(No symbol) [0x0x93ffa4]
	(No symbol) [0x0x96f624]
	(No symbol) [0x0x9907ba]
	(No symbol) [0x0x96f1a6]
	(No symbol) [0x0x93e7b2]
	(No symbol) [0x0x93f654]
	GetHandleVerifier [0x0xd38883+2672035]
	GetHandleVerifier [0x0xd33cba+2652634]
	GetHandleVerifier [0x0xae2bca+223466]
	GetHandleVerifier [0x0xad2cb8+158168]
	GetHandleVerifier [0x0xad978d+185517]
	GetHandleVerifier [0x0xac3b78+96408]
	GetHandleVerifier [0x0xac3d02+96802]
	GetHandleVerifier [0x0xaae90a+9770]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-30 05:56:36 | ERROR    | src.modules.posting.executor:execute_post:828 | 发帖失败: CharleneCa2057, 'PostingExecutor' object has no attribute '_save_cookies'
2025-07-30 06:08:41 | ERROR    | src.modules.posting.executor:execute_post:828 | 发帖失败: CharleneCa2057, 'PostingExecutor' object has no attribute '_save_cookies'
2025-07-30 06:19:47 | ERROR    | src.modules.posting.executor:execute_post:828 | 发帖失败: CharleneCa2057, 'PostingExecutor' object has no attribute '_save_cookies'
2025-07-30 14:36:58 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:602 | Chrome启动彻底失败: Message: session not created: cannot connect to chrome at 127.0.0.1:20812
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x7ff617dee415+77285]
	GetHandleVerifier [0x0x7ff617dee470+77376]
	(No symbol) [0x0x7ff617bb989c]
	(No symbol) [0x0x7ff617baab71]
	(No symbol) [0x0x7ff617bfd0c2]
	(No symbol) [0x0x7ff617bf1eb9]
	(No symbol) [0x0x7ff617c4602e]
	(No symbol) [0x0x7ff617c457c0]
	(No symbol) [0x0x7ff617c383e3]
	(No symbol) [0x0x7ff617c01521]
	(No symbol) [0x0x7ff617c022b3]
	GetHandleVerifier [0x0x7ff6180d1efd+3107021]
	GetHandleVerifier [0x0x7ff6180cc29d+3083373]
	GetHandleVerifier [0x0x7ff6180ebedd+3213485]
	GetHandleVerifier [0x0x7ff617e0884e+184862]
	GetHandleVerifier [0x0x7ff617e1055f+216879]
	GetHandleVerifier [0x0x7ff617df7084+113236]
	GetHandleVerifier [0x0x7ff617df7239+113673]
	GetHandleVerifier [0x0x7ff617dde298+11368]
	BaseThreadInitThunk [0x0x7ff80818e8d7+23]
	RtlUserThreadStart [0x0x7ff808d1c34c+44]

2025-07-30 14:39:40 | ERROR    | src.modules.posting.executor:_input_text_content:1391 | 输入文本内容失败: Message: element click intercepted: Element <div aria-activedescendant="typeaheadFocus-0.08036443032719642" aria-autocomplete="list" aria-controls="typeaheadDropdownWrapped-1" aria-describedby="placeholder-7olj3" aria-label="Post text" aria-multiline="true" class="notranslate public-DraftEditor-content" contenteditable="true" data-testid="tweetTextarea_0" role="textbox" spellcheck="true" tabindex="0" no-focustrapview-refocus="true" style="outline: none; user-select: text; white-space: pre-wrap; overflow-wrap: break-word;">...</div> is not clickable at point (911, 14). Other element would receive the click: <a href="/home" role="tab" aria-selected="false" tabindex="-1" class="css-175oi2r r-1awozwy r-6koalj r-eqz5dr r-16y2uox r-1h3ijdo r-1777fci r-s8bhmr r-3pj75a r-o7ynqc r-6416eg r-1ny4l3l r-1loqt21">...</a>
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0xf7ba83+63395]
	GetHandleVerifier [0x0xf7bac4+63460]
	(No symbol) [0x0xdc2113]
	(No symbol) [0x0xe10ba0]
	(No symbol) [0x0xe0ef5a]
	(No symbol) [0x0xe0cab7]
	(No symbol) [0x0xe0bd6d]
	(No symbol) [0x0xe00515]
	(No symbol) [0x0xe2f3ac]
	(No symbol) [0x0xdfffa4]
	(No symbol) [0x0xe2f624]
	(No symbol) [0x0xe507ba]
	(No symbol) [0x0xe2f1a6]
	(No symbol) [0x0xdfe7b2]
	(No symbol) [0x0xdff654]
	GetHandleVerifier [0x0x11f8883+2672035]
	GetHandleVerifier [0x0x11f3cba+2652634]
	GetHandleVerifier [0x0xfa2bca+223466]
	GetHandleVerifier [0x0xf92cb8+158168]
	GetHandleVerifier [0x0xf9978d+185517]
	GetHandleVerifier [0x0xf83b78+96408]
	GetHandleVerifier [0x0xf83d02+96802]
	GetHandleVerifier [0x0xf6e90a+9770]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-30 16:00:31 | ERROR    | src.modules.posting.executor:_input_text_content:1391 | 输入文本内容失败: Message: element click intercepted: Element <div aria-activedescendant="typeaheadFocus-0.7917454240364509" aria-autocomplete="list" aria-controls="typeaheadDropdownWrapped-1" aria-describedby="placeholder-688jl" aria-label="Post text" aria-multiline="true" class="notranslate public-DraftEditor-content" contenteditable="true" data-testid="tweetTextarea_0" role="textbox" spellcheck="true" tabindex="0" no-focustrapview-refocus="true" style="outline: none; user-select: text; white-space: pre-wrap; overflow-wrap: break-word;">...</div> is not clickable at point (911, 90). Other element would receive the click: <div class="css-175oi2r r-1p0dtai r-1d2f490 r-1xcajam r-zchlnj r-ipm5af r-1ffj0ar" data-testid="mask"></div>
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x120ba83+63395]
	GetHandleVerifier [0x0x120bac4+63460]
	(No symbol) [0x0x1052113]
	(No symbol) [0x0x10a0ba0]
	(No symbol) [0x0x109ef5a]
	(No symbol) [0x0x109cab7]
	(No symbol) [0x0x109bd6d]
	(No symbol) [0x0x1090515]
	(No symbol) [0x0x10bf3ac]
	(No symbol) [0x0x108ffa4]
	(No symbol) [0x0x10bf624]
	(No symbol) [0x0x10e07ba]
	(No symbol) [0x0x10bf1a6]
	(No symbol) [0x0x108e7b2]
	(No symbol) [0x0x108f654]
	GetHandleVerifier [0x0x1488883+2672035]
	GetHandleVerifier [0x0x1483cba+2652634]
	GetHandleVerifier [0x0x1232bca+223466]
	GetHandleVerifier [0x0x1222cb8+158168]
	GetHandleVerifier [0x0x122978d+185517]
	GetHandleVerifier [0x0x1213b78+96408]
	GetHandleVerifier [0x0x1213d02+96802]
	GetHandleVerifier [0x0x11fe90a+9770]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-30 16:00:49 | ERROR    | src.modules.posting.executor:_input_text_content:1391 | 输入文本内容失败: Message: element click intercepted: Element <div aria-activedescendant="typeaheadFocus-0.8842456527121058" aria-autocomplete="list" aria-controls="typeaheadDropdownWrapped-1" aria-describedby="placeholder-9ppig" aria-label="Post text" aria-multiline="true" class="notranslate public-DraftEditor-content" contenteditable="true" data-testid="tweetTextarea_0" role="textbox" spellcheck="true" tabindex="0" no-focustrapview-refocus="true" style="outline: none; user-select: text; white-space: pre-wrap; overflow-wrap: break-word;">...</div> is not clickable at point (911, 14). Other element would receive the click: <a href="/home" role="tab" aria-selected="false" tabindex="-1" class="css-175oi2r r-1awozwy r-6koalj r-eqz5dr r-16y2uox r-1h3ijdo r-1777fci r-s8bhmr r-3pj75a r-o7ynqc r-6416eg r-1ny4l3l r-1loqt21">...</a>
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x120ba83+63395]
	GetHandleVerifier [0x0x120bac4+63460]
	(No symbol) [0x0x1052113]
	(No symbol) [0x0x10a0ba0]
	(No symbol) [0x0x109ef5a]
	(No symbol) [0x0x109cab7]
	(No symbol) [0x0x109bd6d]
	(No symbol) [0x0x1090515]
	(No symbol) [0x0x10bf3ac]
	(No symbol) [0x0x108ffa4]
	(No symbol) [0x0x10bf624]
	(No symbol) [0x0x10e07ba]
	(No symbol) [0x0x10bf1a6]
	(No symbol) [0x0x108e7b2]
	(No symbol) [0x0x108f654]
	GetHandleVerifier [0x0x1488883+2672035]
	GetHandleVerifier [0x0x1483cba+2652634]
	GetHandleVerifier [0x0x1232bca+223466]
	GetHandleVerifier [0x0x1222cb8+158168]
	GetHandleVerifier [0x0x122978d+185517]
	GetHandleVerifier [0x0x1213b78+96408]
	GetHandleVerifier [0x0x1213d02+96802]
	GetHandleVerifier [0x0x11fe90a+9770]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-30 16:01:12 | ERROR    | src.modules.posting.executor:execute_post:821 | ❌ 发帖失败: 发布按钮不可用
2025-07-30 18:12:02 | ERROR    | src.ui.main_window:create_tabs:144 | 创建标签页失败: name 'QGridLayout' is not defined
2025-07-30 19:44:27 | ERROR    | src.modules.posting.executor:login_account:427 | 登录异常: SebastianS49417 (尝试 1/3), Task <Task pending name='Task-511' coro=<ConcurrentAccountLoginThread.run.<locals>.concurrent_login_accounts.<locals>.login_single_account() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\ui\widgets\account_widget.py:231> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-30 19:44:27 | ERROR    | src.modules.posting.executor:login_account:427 | 登录异常: Remington852245 (尝试 1/3), Task <Task pending name='Task-512' coro=<ConcurrentAccountLoginThread.run.<locals>.concurrent_login_accounts.<locals>.login_single_account() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\ui\widgets\account_widget.py:231> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-30 19:44:27 | ERROR    | src.modules.posting.executor:login_account:427 | 登录异常: AbdullahAl16806 (尝试 1/3), Task <Task pending name='Task-513' coro=<ConcurrentAccountLoginThread.run.<locals>.concurrent_login_accounts.<locals>.login_single_account() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\ui\widgets\account_widget.py:231> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-30 19:44:27 | ERROR    | src.modules.posting.executor:login_account:427 | 登录异常: mp_ser29877 (尝试 1/3), Task <Task pending name='Task-514' coro=<ConcurrentAccountLoginThread.run.<locals>.concurrent_login_accounts.<locals>.login_single_account() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\ui\widgets\account_widget.py:231> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-30 19:44:33 | ERROR    | src.modules.posting.executor:login_account:427 | 登录异常: AbdullahAl16806 (尝试 2/3), Task <Task pending name='Task-513' coro=<ConcurrentAccountLoginThread.run.<locals>.concurrent_login_accounts.<locals>.login_single_account() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\ui\widgets\account_widget.py:231> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-30 19:44:33 | ERROR    | src.modules.posting.executor:login_account:427 | 登录异常: mp_ser29877 (尝试 2/3), Task <Task pending name='Task-514' coro=<ConcurrentAccountLoginThread.run.<locals>.concurrent_login_accounts.<locals>.login_single_account() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\ui\widgets\account_widget.py:231> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-30 19:44:34 | ERROR    | src.modules.posting.executor:login_account:427 | 登录异常: Remington852245 (尝试 2/3), Task <Task pending name='Task-512' coro=<ConcurrentAccountLoginThread.run.<locals>.concurrent_login_accounts.<locals>.login_single_account() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\ui\widgets\account_widget.py:231> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-30 19:44:34 | ERROR    | src.modules.posting.executor:login_account:427 | 登录异常: SebastianS49417 (尝试 2/3), Task <Task pending name='Task-511' coro=<ConcurrentAccountLoginThread.run.<locals>.concurrent_login_accounts.<locals>.login_single_account() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\ui\widgets\account_widget.py:231> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-30 19:44:38 | ERROR    | src.modules.posting.executor:login_account:427 | 登录异常: AbdullahAl16806 (尝试 3/3), Task <Task pending name='Task-513' coro=<ConcurrentAccountLoginThread.run.<locals>.concurrent_login_accounts.<locals>.login_single_account() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\ui\widgets\account_widget.py:231> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-30 19:44:42 | ERROR    | src.modules.posting.executor:login_account:427 | 登录异常: mp_ser29877 (尝试 3/3), Task <Task pending name='Task-514' coro=<ConcurrentAccountLoginThread.run.<locals>.concurrent_login_accounts.<locals>.login_single_account() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\ui\widgets\account_widget.py:231> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-30 19:44:43 | ERROR    | src.modules.posting.executor:login_account:427 | 登录异常: Remington852245 (尝试 3/3), Task <Task pending name='Task-512' coro=<ConcurrentAccountLoginThread.run.<locals>.concurrent_login_accounts.<locals>.login_single_account() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\ui\widgets\account_widget.py:231> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-30 19:44:44 | ERROR    | src.modules.posting.executor:login_account:427 | 登录异常: SebastianS49417 (尝试 3/3), Task <Task pending name='Task-511' coro=<ConcurrentAccountLoginThread.run.<locals>.concurrent_login_accounts.<locals>.login_single_account() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\ui\widgets\account_widget.py:231> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-30 19:53:30 | ERROR    | src.modules.posting.executor:login_account:427 | 登录异常: Remington852245 (尝试 1/3), Task <Task pending name='Task-3' coro=<ConcurrentAccountLoginThread.run.<locals>.concurrent_login_accounts.<locals>.login_single_account() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\ui\widgets\account_widget.py:241> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-30 19:53:40 | ERROR    | src.modules.posting.executor:login_account:427 | 登录异常: Remington852245 (尝试 2/3), Task <Task pending name='Task-3' coro=<ConcurrentAccountLoginThread.run.<locals>.concurrent_login_accounts.<locals>.login_single_account() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\ui\widgets\account_widget.py:241> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-30 19:53:46 | ERROR    | src.modules.posting.executor:login_account:427 | 登录异常: Remington852245 (尝试 3/3), Task <Task pending name='Task-3' coro=<ConcurrentAccountLoginThread.run.<locals>.concurrent_login_accounts.<locals>.login_single_account() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\ui\widgets\account_widget.py:241> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-30 19:54:47 | ERROR    | src.modules.posting.executor:login_account:427 | 登录异常: SebastianS49417 (尝试 1/3), 无法找到用户名输入框
2025-07-30 19:56:47 | ERROR    | src.modules.posting.executor:login_account:427 | 登录异常: SebastianS49417 (尝试 2/3), 无法找到密码输入框
2025-07-30 20:08:40 | ERROR    | src.modules.posting.executor:login_account:427 | 登录异常: Remington852245 (尝试 1/3), 无法找到用户名输入框
2025-07-30 21:08:52 | ERROR    | src.ui.widgets.account_widget:concurrent_login_accounts:1525 | 并发登录账号失败: 'AccountWidget' object has no attribute 'login_mode_combo'
2025-07-30 21:09:15 | ERROR    | src.ui.widgets.account_widget:concurrent_login_accounts:1525 | 并发登录账号失败: 'AccountWidget' object has no attribute 'login_mode_combo'
2025-07-30 21:20:46 | ERROR    | src.core.token_session_manager:_inject_token_to_browser:162 | 注入Token失败: Message: tab crashed
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x59ba83+63395]
	GetHandleVerifier [0x0x59bac4+63460]
	(No symbol) [0x0x3e1f70]
	(No symbol) [0x0x3d3eaa]
	(No symbol) [0x0x3d3318]
	(No symbol) [0x0x3d2e93]
	(No symbol) [0x0x3d2ba1]
	(No symbol) [0x0x3d0b44]
	(No symbol) [0x0x3d160d]
	(No symbol) [0x0x3ddda9]
	(No symbol) [0x0x3ef2b5]
	(No symbol) [0x0x3f4cb6]
	(No symbol) [0x0x3d1c4d]
	(No symbol) [0x0x3eeb43]
	(No symbol) [0x0x470a9c]
	(No symbol) [0x0x44f1a6]
	(No symbol) [0x0x41e7b2]
	(No symbol) [0x0x41f654]
	GetHandleVerifier [0x0x818883+2672035]
	GetHandleVerifier [0x0x813cba+2652634]
	GetHandleVerifier [0x0x5c2bca+223466]
	GetHandleVerifier [0x0x5b2cb8+158168]
	GetHandleVerifier [0x0x5b978d+185517]
	GetHandleVerifier [0x0x5a3b78+96408]
	GetHandleVerifier [0x0x5a3d02+96802]
	GetHandleVerifier [0x0x58e90a+9770]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-30 21:20:46 | ERROR    | src.core.token_session_manager:_verify_token_login:122 | 验证Token登录失败: Message: tab crashed
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x59ba83+63395]
	GetHandleVerifier [0x0x59bac4+63460]
	(No symbol) [0x0x3e1f70]
	(No symbol) [0x0x3d3eaa]
	(No symbol) [0x0x3d3318]
	(No symbol) [0x0x3d2e93]
	(No symbol) [0x0x3d2ba1]
	(No symbol) [0x0x3d0b44]
	(No symbol) [0x0x3d160d]
	(No symbol) [0x0x3ddda9]
	(No symbol) [0x0x3ef2b5]
	(No symbol) [0x0x3f4cb6]
	(No symbol) [0x0x3d1c4d]
	(No symbol) [0x0x3eeb43]
	(No symbol) [0x0x470a9c]
	(No symbol) [0x0x44f1a6]
	(No symbol) [0x0x41e7b2]
	(No symbol) [0x0x41f654]
	GetHandleVerifier [0x0x818883+2672035]
	GetHandleVerifier [0x0x813cba+2652634]
	GetHandleVerifier [0x0x5c2bca+223466]
	GetHandleVerifier [0x0x5b2cb8+158168]
	GetHandleVerifier [0x0x5b978d+185517]
	GetHandleVerifier [0x0x5a3b78+96408]
	GetHandleVerifier [0x0x5a3d02+96802]
	GetHandleVerifier [0x0x58e90a+9770]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-30 21:20:46 | ERROR    | src.core.token_session_manager:_inject_token_to_browser:162 | 注入Token失败: Message: Driver会话已断开

2025-07-30 21:20:46 | ERROR    | src.core.token_session_manager:_verify_token_login:122 | 验证Token登录失败: Message: Driver会话已断开

2025-07-30 21:20:56 | ERROR    | src.modules.posting.executor:login_account:443 | 登录异常: mp_ser29877 (尝试 1/3), Message: Driver已关闭

2025-07-30 21:21:12 | ERROR    | src.modules.posting.executor:login_account:443 | 登录异常: Remington852245 (尝试 1/3), HTTPConnectionPool(host='localhost', port=39403): Max retries exceeded with url: /session/db547ca8e300b78cc857e1a4fb7fab3c/url (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001FF2D8BB370>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-30 21:36:43 | ERROR    | src.core.token_session_manager:_inject_token_to_browser:188 | 注入Token失败: Message: timeout: Timed out receiving message from renderer: 298.756
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0xaeba83+63395]
	GetHandleVerifier [0x0xaebac4+63460]
	(No symbol) [0x0x932113]
	(No symbol) [0x0x922e6b]
	(No symbol) [0x0x922ba1]
	(No symbol) [0x0x920b44]
	(No symbol) [0x0x92160d]
	(No symbol) [0x0x92dda9]
	(No symbol) [0x0x93f2b5]
	(No symbol) [0x0x944cb6]
	(No symbol) [0x0x921c4d]
	(No symbol) [0x0x93f019]
	(No symbol) [0x0x9c0a9c]
	(No symbol) [0x0x99f1a6]
	(No symbol) [0x0x96e7b2]
	(No symbol) [0x0x96f654]
	GetHandleVerifier [0x0xd68883+2672035]
	GetHandleVerifier [0x0xd63cba+2652634]
	GetHandleVerifier [0x0xb12bca+223466]
	GetHandleVerifier [0x0xb02cb8+158168]
	GetHandleVerifier [0x0xb0978d+185517]
	GetHandleVerifier [0x0xaf3b78+96408]
	GetHandleVerifier [0x0xaf3d02+96802]
	GetHandleVerifier [0x0xade90a+9770]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-30 21:36:43 | ERROR    | src.core.token_session_manager:_verify_token_login:148 | 验证Token登录失败: Message: timeout: Timed out receiving message from renderer: 298.756
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0xaeba83+63395]
	GetHandleVerifier [0x0xaebac4+63460]
	(No symbol) [0x0x932113]
	(No symbol) [0x0x922e6b]
	(No symbol) [0x0x922ba1]
	(No symbol) [0x0x920b44]
	(No symbol) [0x0x92160d]
	(No symbol) [0x0x92dda9]
	(No symbol) [0x0x93f2b5]
	(No symbol) [0x0x944cb6]
	(No symbol) [0x0x921c4d]
	(No symbol) [0x0x93f019]
	(No symbol) [0x0x9c0a9c]
	(No symbol) [0x0x99f1a6]
	(No symbol) [0x0x96e7b2]
	(No symbol) [0x0x96f654]
	GetHandleVerifier [0x0xd68883+2672035]
	GetHandleVerifier [0x0xd63cba+2652634]
	GetHandleVerifier [0x0xb12bca+223466]
	GetHandleVerifier [0x0xb02cb8+158168]
	GetHandleVerifier [0x0xb0978d+185517]
	GetHandleVerifier [0x0xaf3b78+96408]
	GetHandleVerifier [0x0xaf3d02+96802]
	GetHandleVerifier [0x0xade90a+9770]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-30 21:39:56 | ERROR    | src.modules.posting.executor:login_account:455 | 登录异常: AhmedovD68802 (尝试 1/3), Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0xaeba83+63395]
	GetHandleVerifier [0x0xaebac4+63460]
	(No symbol) [0x0x932113]
	(No symbol) [0x0x910fb9]
	(No symbol) [0x0x9a5ace]
	(No symbol) [0x0x9c0099]
	(No symbol) [0x0x99f1a6]
	(No symbol) [0x0x96e7b2]
	(No symbol) [0x0x96f654]
	GetHandleVerifier [0x0xd68883+2672035]
	GetHandleVerifier [0x0xd63cba+2652634]
	GetHandleVerifier [0x0xb12bca+223466]
	GetHandleVerifier [0x0xb02cb8+158168]
	GetHandleVerifier [0x0xb0978d+185517]
	GetHandleVerifier [0x0xaf3b78+96408]
	GetHandleVerifier [0x0xaf3d02+96802]
	GetHandleVerifier [0x0xade90a+9770]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-30 21:52:52 | ERROR    | src.core.token_session_manager:_inject_token_to_browser:272 | 注入Token失败: name 'WebDriverWait' is not defined
2025-07-30 21:52:52 | ERROR    | src.core.token_session_manager:_verify_token_login:176 | 验证Token登录失败: name 'WebDriverWait' is not defined
2025-07-30 21:53:08 | ERROR    | src.core.token_session_manager:_inject_token_to_browser:272 | 注入Token失败: name 'WebDriverWait' is not defined
2025-07-30 21:53:08 | ERROR    | src.core.token_session_manager:_verify_token_login:176 | 验证Token登录失败: name 'WebDriverWait' is not defined
2025-07-30 21:53:12 | ERROR    | src.core.token_session_manager:_inject_token_to_browser:272 | 注入Token失败: name 'WebDriverWait' is not defined
2025-07-30 21:53:12 | ERROR    | src.core.token_session_manager:_verify_token_login:176 | 验证Token登录失败: name 'WebDriverWait' is not defined
2025-07-30 21:53:42 | ERROR    | src.modules.posting.executor:login_account:475 | 登录异常: LabsNopal29409 (尝试 1/3), 无法找到用户名输入框
2025-07-30 21:53:52 | ERROR    | src.core.token_session_manager:_inject_token_to_browser:272 | 注入Token失败: name 'WebDriverWait' is not defined
2025-07-30 21:53:52 | ERROR    | src.core.token_session_manager:_verify_token_login:176 | 验证Token登录失败: name 'WebDriverWait' is not defined
2025-07-30 21:54:27 | ERROR    | src.modules.posting.executor:login_account:475 | 登录异常: LabsNopal29409 (尝试 2/3), 无法找到用户名输入框
2025-07-30 21:54:37 | ERROR    | src.core.token_session_manager:_inject_token_to_browser:272 | 注入Token失败: name 'WebDriverWait' is not defined
2025-07-30 21:54:37 | ERROR    | src.core.token_session_manager:_verify_token_login:176 | 验证Token登录失败: name 'WebDriverWait' is not defined
2025-07-30 21:54:50 | ERROR    | src.core.token_session_manager:_inject_token_to_browser:272 | 注入Token失败: name 'WebDriverWait' is not defined
2025-07-30 21:54:50 | ERROR    | src.core.token_session_manager:_verify_token_login:176 | 验证Token登录失败: name 'WebDriverWait' is not defined
2025-07-30 21:55:04 | ERROR    | src.modules.posting.executor:login_account:475 | 登录异常: loves_lul49315 (尝试 1/3), 无法找到密码输入框
2025-07-30 21:55:15 | ERROR    | src.core.token_session_manager:_inject_token_to_browser:272 | 注入Token失败: name 'WebDriverWait' is not defined
2025-07-30 21:55:15 | ERROR    | src.core.token_session_manager:_verify_token_login:176 | 验证Token登录失败: name 'WebDriverWait' is not defined
2025-07-30 21:55:29 | ERROR    | src.modules.posting.executor:login_account:297 | 用户名输入失败: Message: Driver已关闭

2025-07-30 21:55:29 | ERROR    | src.modules.posting.executor:login_account:475 | 登录异常: LabsNopal29409 (尝试 3/3), 无法找到用户名输入框
2025-07-30 21:55:35 | ERROR    | src.modules.posting.executor:login_account:297 | 用户名输入失败: Message: Driver已关闭

2025-07-30 21:55:35 | ERROR    | src.modules.posting.executor:login_account:475 | 登录异常: AhmedovD68802 (尝试 2/3), 无法找到用户名输入框
2025-07-30 21:55:44 | ERROR    | src.modules.posting.executor:login_account:475 | 登录异常: loves_lul49315 (尝试 2/3), Message: Driver已关闭

2025-07-30 21:55:59 | ERROR    | src.core.token_session_manager:_inject_token_to_browser:272 | 注入Token失败: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-30 21:55:59 | ERROR    | src.core.token_session_manager:_verify_token_login:176 | 验证Token登录失败: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-30 21:56:00 | ERROR    | src.core.token_session_manager:_inject_token_to_browser:272 | 注入Token失败: name 'WebDriverWait' is not defined
2025-07-30 21:56:00 | ERROR    | src.core.token_session_manager:_verify_token_login:176 | 验证Token登录失败: name 'WebDriverWait' is not defined
2025-07-30 21:56:10 | ERROR    | src.modules.posting.executor:login_account:475 | 登录异常: loves_lul49315 (尝试 3/3), Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x74ba83+63395]
	GetHandleVerifier [0x0x74bac4+63460]
	(No symbol) [0x0x592113]
	(No symbol) [0x0x570fb9]
	(No symbol) [0x0x605ace]
	(No symbol) [0x0x620099]
	(No symbol) [0x0x5ff1a6]
	(No symbol) [0x0x5ce7b2]
	(No symbol) [0x0x5cf654]
	GetHandleVerifier [0x0x9c8883+2672035]
	GetHandleVerifier [0x0x9c3cba+2652634]
	GetHandleVerifier [0x0x772bca+223466]
	GetHandleVerifier [0x0x762cb8+158168]
	GetHandleVerifier [0x0x76978d+185517]
	GetHandleVerifier [0x0x753b78+96408]
	GetHandleVerifier [0x0x753d02+96802]
	GetHandleVerifier [0x0x73e90a+9770]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-30 21:56:49 | ERROR    | src.modules.posting.executor:login_account:475 | 登录异常: AhmedovD68802 (尝试 3/3), HTTPConnectionPool(host='localhost', port=29025): Max retries exceeded with url: /session/c22c00e98a5d865bcbcc5c3329fa6f11/url (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001F105015AF0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))
2025-07-30 22:10:25 | ERROR    | src.core.token_session_manager:_inject_token_to_browser:274 | Token注入超时: JombolioE57643
2025-07-30 22:10:25 | ERROR    | src.core.token_session_manager:_verify_token_login:181 | 验证Token登录失败: Token注入超时: 
2025-07-30 22:10:32 | ERROR    | src.core.token_session_manager:_inject_token_to_browser:274 | Token注入超时: EthHvala50954
2025-07-30 22:10:32 | ERROR    | src.core.token_session_manager:_verify_token_login:181 | 验证Token登录失败: Token注入超时: 
2025-07-30 22:10:39 | ERROR    | src.core.token_session_manager:_inject_token_to_browser:274 | Token注入超时: Knownothin59223
2025-07-30 22:10:39 | ERROR    | src.core.token_session_manager:_verify_token_login:181 | 验证Token登录失败: Token注入超时: 
2025-07-30 22:10:45 | ERROR    | src.core.token_session_manager:_inject_token_to_browser:274 | Token注入超时: PetqDankov7382
2025-07-30 22:10:45 | ERROR    | src.core.token_session_manager:_verify_token_login:181 | 验证Token登录失败: Token注入超时: 
2025-07-30 22:10:52 | ERROR    | src.core.token_session_manager:_inject_token_to_browser:274 | Token注入超时: Gumball624003
2025-07-30 22:10:52 | ERROR    | src.core.token_session_manager:_verify_token_login:181 | 验证Token登录失败: Token注入超时: 
2025-07-30 22:11:10 | ERROR    | src.modules.posting.executor:login_account:297 | 用户名输入失败: Message: Driver已关闭

2025-07-30 22:11:10 | ERROR    | src.modules.posting.executor:login_account:475 | 登录异常: EthHvala50954 (尝试 1/3), 无法找到用户名输入框
2025-07-30 22:11:14 | ERROR    | src.modules.posting.executor:login_account:297 | 用户名输入失败: Message: Driver已关闭

2025-07-30 22:11:14 | ERROR    | src.modules.posting.executor:login_account:475 | 登录异常: Knownothin59223 (尝试 1/3), 无法找到用户名输入框
2025-07-30 22:11:17 | ERROR    | src.modules.posting.executor:login_account:475 | 登录异常: Gumball624003 (尝试 1/3), Message: Driver已关闭

2025-07-30 22:16:20 | ERROR    | src.core.token_session_manager:_inject_token_to_browser:274 | Token注入超时: wildebeest3796
2025-07-30 22:16:20 | ERROR    | src.core.token_session_manager:_verify_token_login:181 | 验证Token登录失败: Token注入超时: 
2025-07-30 22:16:29 | ERROR    | src.core.token_session_manager:_inject_token_to_browser:274 | Token注入超时: MattikNeo21759
2025-07-30 22:16:29 | ERROR    | src.core.token_session_manager:_verify_token_login:181 | 验证Token登录失败: Token注入超时: 
2025-07-30 22:17:11 | ERROR    | src.modules.posting.executor:login_account:475 | 登录异常: MattikNeo21759 (尝试 1/3), 无法找到用户名输入框
2025-07-30 22:17:19 | ERROR    | src.modules.posting.executor:login_account:475 | 登录异常: wildebeest3796 (尝试 1/3), 无法找到用户名输入框
2025-07-30 22:20:18 | ERROR    | src.core.token_session_manager:_inject_token_to_browser:274 | Token注入超时: DdsBentley47711
2025-07-30 22:20:18 | ERROR    | src.core.token_session_manager:_verify_token_login:181 | 验证Token登录失败: Token注入超时: 
2025-07-30 22:21:17 | ERROR    | src.modules.posting.executor:login_account:475 | 登录异常: DdsBentley47711 (尝试 1/3), 无法找到用户名输入框
2025-07-30 22:30:36 | ERROR    | src.modules.interaction.executor:execute_interaction:152 | 互动异常: SebastianS49417, like, Task <Task pending name='Task-9' coro=<InteractionExecutor._execute_concurrent_interactions.<locals>.execute_single_interaction_with_semaphore() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\executor.py:385> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-30 22:30:36 | ERROR    | src.modules.interaction.executor:execute_interaction:152 | 互动异常: LabsNopal29409, retweet, Task <Task pending name='Task-10' coro=<InteractionExecutor._execute_concurrent_interactions.<locals>.execute_single_interaction_with_semaphore() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\executor.py:385> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-30 22:30:37 | ERROR    | src.modules.interaction.executor:execute_interaction:152 | 互动异常: wildebeest3796, comment, Task <Task pending name='Task-12' coro=<InteractionExecutor._execute_concurrent_interactions.<locals>.execute_single_interaction_with_semaphore() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\executor.py:385> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-30 22:30:37 | ERROR    | src.modules.interaction.executor:execute_interaction:152 | 互动异常: EthHvala50954, like, Task <Task pending name='Task-8' coro=<InteractionExecutor._execute_concurrent_interactions.<locals>.execute_single_interaction_with_semaphore() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\executor.py:385> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-30 22:30:38 | ERROR    | src.modules.interaction.executor:execute_interaction:152 | 互动异常: EliteSylph26626, comment, Task <Task pending name='Task-13' coro=<InteractionExecutor._execute_concurrent_interactions.<locals>.execute_single_interaction_with_semaphore() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\executor.py:385> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-31 00:48:42 | ERROR    | src.modules.interaction.executor:_create_interaction_task:872 | 创建互动任务失败: 互动类型必须是: like, comment, retweet
2025-07-31 00:48:42 | ERROR    | src.modules.interaction.executor:_create_interaction_task:872 | 创建互动任务失败: 互动类型必须是: like, comment, retweet
2025-07-31 00:48:42 | ERROR    | src.modules.interaction.executor:_create_interaction_task:872 | 创建互动任务失败: 互动类型必须是: like, comment, retweet
2025-07-31 00:48:42 | ERROR    | src.modules.interaction.executor:_create_interaction_task:872 | 创建互动任务失败: 互动类型必须是: like, comment, retweet
2025-07-31 00:48:42 | ERROR    | src.modules.interaction.executor:_create_interaction_task:872 | 创建互动任务失败: 互动类型必须是: like, comment, retweet
2025-07-31 00:48:42 | ERROR    | src.modules.interaction.executor:_create_interaction_task:872 | 创建互动任务失败: 互动类型必须是: like, comment, retweet
2025-07-31 00:48:42 | ERROR    | src.modules.interaction.executor:_create_interaction_task:872 | 创建互动任务失败: 互动类型必须是: like, comment, retweet
2025-07-31 00:48:42 | ERROR    | src.modules.interaction.executor:_create_interaction_task:872 | 创建互动任务失败: 互动类型必须是: like, comment, retweet
2025-07-31 00:48:42 | ERROR    | src.modules.interaction.executor:_create_interaction_task:872 | 创建互动任务失败: 互动类型必须是: like, comment, retweet
2025-07-31 00:55:48 | ERROR    | src.modules.interaction.executor:_create_interaction_task:872 | 创建互动任务失败: 互动类型必须是: like, comment, retweet
2025-07-31 00:55:48 | ERROR    | src.modules.interaction.executor:_create_interaction_task:872 | 创建互动任务失败: 互动类型必须是: like, comment, retweet
2025-07-31 00:55:48 | ERROR    | src.modules.interaction.executor:_create_interaction_task:872 | 创建互动任务失败: 互动类型必须是: like, comment, retweet
2025-07-31 00:55:48 | ERROR    | src.modules.interaction.executor:_create_interaction_task:872 | 创建互动任务失败: 互动类型必须是: like, comment, retweet
2025-07-31 00:55:48 | ERROR    | src.modules.interaction.executor:_create_interaction_task:872 | 创建互动任务失败: 互动类型必须是: like, comment, retweet
2025-07-31 00:55:48 | ERROR    | src.modules.interaction.executor:_create_interaction_task:872 | 创建互动任务失败: 互动类型必须是: like, comment, retweet
2025-07-31 00:55:48 | ERROR    | src.modules.interaction.executor:_create_interaction_task:872 | 创建互动任务失败: 互动类型必须是: like, comment, retweet
2025-07-31 00:55:48 | ERROR    | src.modules.interaction.executor:_create_interaction_task:872 | 创建互动任务失败: 互动类型必须是: like, comment, retweet
2025-07-31 00:55:48 | ERROR    | src.modules.interaction.executor:_create_interaction_task:872 | 创建互动任务失败: 互动类型必须是: like, comment, retweet
2025-07-31 00:55:48 | ERROR    | src.modules.interaction.executor:_create_interaction_task:872 | 创建互动任务失败: 互动类型必须是: like, comment, retweet
2025-07-31 00:55:48 | ERROR    | src.modules.interaction.executor:_create_interaction_task:872 | 创建互动任务失败: 互动类型必须是: like, comment, retweet
2025-07-31 00:55:48 | ERROR    | src.modules.interaction.executor:_create_interaction_task:872 | 创建互动任务失败: 互动类型必须是: like, comment, retweet
2025-07-31 01:03:20 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:191 | 互动异常: loves_lul49315, like, Task <Task pending name='Task-10' coro=<ConcurrentInteractionExecutor.execute_concurrent_interactions.<locals>.execute_single_task() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:73> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-31 01:03:21 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:191 | 互动异常: SebastianS49417, retweet, Task <Task pending name='Task-12' coro=<ConcurrentInteractionExecutor.execute_concurrent_interactions.<locals>.execute_single_task() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:73> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-31 01:03:21 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:191 | 互动异常: CooL41012679331, retweet, Task <Task pending name='Task-9' coro=<ConcurrentInteractionExecutor.execute_concurrent_interactions.<locals>.execute_single_task() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:73> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-31 01:03:21 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:191 | 互动异常: loves_lul49315, comment, Task <Task pending name='Task-11' coro=<ConcurrentInteractionExecutor.execute_concurrent_interactions.<locals>.execute_single_task() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:73> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-31 01:03:22 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:191 | 互动异常: AmcaCash14080, retweet, Task <Task pending name='Task-15' coro=<ConcurrentInteractionExecutor.execute_concurrent_interactions.<locals>.execute_single_task() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:73> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-31 01:03:22 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:191 | 互动异常: AmcaCash14080, like, Task <Task pending name='Task-14' coro=<ConcurrentInteractionExecutor.execute_concurrent_interactions.<locals>.execute_single_task() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:73> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-31 01:03:22 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:191 | 互动异常: SebastianS49417, comment, Task <Task pending name='Task-13' coro=<ConcurrentInteractionExecutor.execute_concurrent_interactions.<locals>.execute_single_task() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:73> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-31 01:03:22 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:191 | 互动异常: Gumball624003, like, Task <Task pending name='Task-16' coro=<ConcurrentInteractionExecutor.execute_concurrent_interactions.<locals>.execute_single_task() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:73> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-31 01:03:24 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:191 | 互动异常: mp_ser29877, retweet, Task <Task pending name='Task-18' coro=<ConcurrentInteractionExecutor.execute_concurrent_interactions.<locals>.execute_single_task() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:73> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-31 01:03:24 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:191 | 互动异常: mp_ser29877, comment, Task <Task pending name='Task-19' coro=<ConcurrentInteractionExecutor.execute_concurrent_interactions.<locals>.execute_single_task() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:73> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-31 01:03:24 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:191 | 互动异常: Gumball624003, comment, Task <Task pending name='Task-17' coro=<ConcurrentInteractionExecutor.execute_concurrent_interactions.<locals>.execute_single_task() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:73> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-31 01:13:47 | ERROR    | src.modules.interaction.executor:execute_interaction:464 | 互动异常: wildebeest3796, retweet, Task <Task pending name='Task-9' coro=<ConcurrentInteractionExecutor.execute_concurrent_interactions.<locals>.execute_single_task() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:73> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-31 01:13:47 | ERROR    | src.modules.interaction.executor:execute_interaction:464 | 互动异常: loves_lul49315, retweet, Task <Task pending name='Task-12' coro=<ConcurrentInteractionExecutor.execute_concurrent_interactions.<locals>.execute_single_task() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:73> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-31 01:13:47 | ERROR    | src.modules.interaction.executor:execute_interaction:464 | 互动异常: wildebeest3796, like, Task <Task pending name='Task-8' coro=<ConcurrentInteractionExecutor.execute_concurrent_interactions.<locals>.execute_single_task() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:73> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-31 01:13:47 | ERROR    | src.modules.interaction.executor:execute_interaction:464 | 互动异常: EthHvala50954, comment, Task <Task pending name='Task-11' coro=<ConcurrentInteractionExecutor.execute_concurrent_interactions.<locals>.execute_single_task() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:73> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-31 01:13:48 | ERROR    | src.modules.interaction.executor:execute_interaction:464 | 互动异常: EliteSylph26626, like, Task <Task pending name='Task-16' coro=<ConcurrentInteractionExecutor.execute_concurrent_interactions.<locals>.execute_single_task() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:73> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-31 01:13:48 | ERROR    | src.modules.interaction.executor:execute_interaction:464 | 互动异常: loves_lul49315, comment, Task <Task pending name='Task-13' coro=<ConcurrentInteractionExecutor.execute_concurrent_interactions.<locals>.execute_single_task() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:73> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-31 01:13:48 | ERROR    | src.modules.interaction.executor:execute_interaction:464 | 互动异常: AbdullahAl16806, retweet, Task <Task pending name='Task-15' coro=<ConcurrentInteractionExecutor.execute_concurrent_interactions.<locals>.execute_single_task() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:73> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-31 01:13:48 | ERROR    | src.modules.interaction.executor:execute_interaction:464 | 互动异常: AbdullahAl16806, like, Task <Task pending name='Task-14' coro=<ConcurrentInteractionExecutor.execute_concurrent_interactions.<locals>.execute_single_task() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:73> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-31 01:13:50 | ERROR    | src.modules.interaction.executor:execute_interaction:464 | 互动异常: LabsNopal29409, retweet, Task <Task pending name='Task-18' coro=<ConcurrentInteractionExecutor.execute_concurrent_interactions.<locals>.execute_single_task() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:73> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-31 01:13:50 | ERROR    | src.modules.interaction.executor:execute_interaction:464 | 互动异常: EliteSylph26626, comment, Task <Task pending name='Task-17' coro=<ConcurrentInteractionExecutor.execute_concurrent_interactions.<locals>.execute_single_task() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:73> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-31 01:13:50 | ERROR    | src.modules.interaction.executor:execute_interaction:464 | 互动异常: LabsNopal29409, comment, Task <Task pending name='Task-19' coro=<ConcurrentInteractionExecutor.execute_concurrent_interactions.<locals>.execute_single_task() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:73> cb=[_gather.<locals>._done_callback() at D:\python39\lib\asyncio\tasks.py:767]> got Future <Future pending> attached to a different loop
2025-07-31 01:25:37 | ERROR    | src.modules.posting.executor:_input_text_content:1626 | 输入文本内容失败: Message: element click intercepted: Element <div aria-activedescendant="typeaheadFocus-0.9740312895334376" aria-autocomplete="list" aria-controls="typeaheadDropdownWrapped-1" aria-describedby="placeholder-r0n4" aria-label="Post text" aria-multiline="true" class="notranslate public-DraftEditor-content" contenteditable="true" data-testid="tweetTextarea_0" role="textbox" spellcheck="true" tabindex="0" no-focustrapview-refocus="true" style="outline: none; user-select: text; white-space: pre-wrap; overflow-wrap: break-word;">...</div> is not clickable at point (911, 14). Other element would receive the click: <a href="/home" role="tab" aria-selected="false" tabindex="-1" class="css-175oi2r r-1awozwy r-6koalj r-eqz5dr r-16y2uox r-1h3ijdo r-1777fci r-s8bhmr r-3pj75a r-o7ynqc r-6416eg r-1ny4l3l r-1loqt21">...</a>
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x40ba83+63395]
	GetHandleVerifier [0x0x40bac4+63460]
	(No symbol) [0x0x252113]
	(No symbol) [0x0x2a0ba0]
	(No symbol) [0x0x29ef5a]
	(No symbol) [0x0x29cab7]
	(No symbol) [0x0x29bd6d]
	(No symbol) [0x0x290515]
	(No symbol) [0x0x2bf3ac]
	(No symbol) [0x0x28ffa4]
	(No symbol) [0x0x2bf624]
	(No symbol) [0x0x2e07ba]
	(No symbol) [0x0x2bf1a6]
	(No symbol) [0x0x28e7b2]
	(No symbol) [0x0x28f654]
	GetHandleVerifier [0x0x688883+2672035]
	GetHandleVerifier [0x0x683cba+2652634]
	GetHandleVerifier [0x0x432bca+223466]
	GetHandleVerifier [0x0x422cb8+158168]
	GetHandleVerifier [0x0x42978d+185517]
	GetHandleVerifier [0x0x413b78+96408]
	GetHandleVerifier [0x0x413d02+96802]
	GetHandleVerifier [0x0x3fe90a+9770]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-31 01:25:53 | ERROR    | src.modules.posting.executor:_input_text_content:1626 | 输入文本内容失败: Message: element click intercepted: Element <div aria-activedescendant="typeaheadFocus-0.9833561496272093" aria-autocomplete="list" aria-controls="typeaheadDropdownWrapped-1" aria-describedby="placeholder-7jhrn" aria-label="Post text" aria-multiline="true" class="notranslate public-DraftEditor-content" contenteditable="true" data-testid="tweetTextarea_0" role="textbox" spellcheck="true" tabindex="0" no-focustrapview-refocus="true" style="outline: none; user-select: text; white-space: pre-wrap; overflow-wrap: break-word;">...</div> is not clickable at point (911, 90). Other element would receive the click: <div class="css-175oi2r r-1p0dtai r-1d2f490 r-1xcajam r-zchlnj r-ipm5af r-1ffj0ar" data-testid="mask"></div>
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x40ba83+63395]
	GetHandleVerifier [0x0x40bac4+63460]
	(No symbol) [0x0x252113]
	(No symbol) [0x0x2a0ba0]
	(No symbol) [0x0x29ef5a]
	(No symbol) [0x0x29cab7]
	(No symbol) [0x0x29bd6d]
	(No symbol) [0x0x290515]
	(No symbol) [0x0x2bf3ac]
	(No symbol) [0x0x28ffa4]
	(No symbol) [0x0x2bf624]
	(No symbol) [0x0x2e07ba]
	(No symbol) [0x0x2bf1a6]
	(No symbol) [0x0x28e7b2]
	(No symbol) [0x0x28f654]
	GetHandleVerifier [0x0x688883+2672035]
	GetHandleVerifier [0x0x683cba+2652634]
	GetHandleVerifier [0x0x432bca+223466]
	GetHandleVerifier [0x0x422cb8+158168]
	GetHandleVerifier [0x0x42978d+185517]
	GetHandleVerifier [0x0x413b78+96408]
	GetHandleVerifier [0x0x413d02+96802]
	GetHandleVerifier [0x0x3fe90a+9770]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-31 01:26:20 | ERROR    | src.modules.posting.executor:execute_post:909 | ❌ 发帖失败: 媒体上传失败: 媒体上传验证失败
2025-07-31 01:27:05 | ERROR    | src.modules.posting.executor:_input_text_content:1626 | 输入文本内容失败: Message: element click intercepted: Element <div aria-activedescendant="typeaheadFocus-0.6065319112532495" aria-autocomplete="list" aria-controls="typeaheadDropdownWrapped-3" aria-describedby="placeholder-5q62n" aria-label="Post text" aria-multiline="true" class="notranslate public-DraftEditor-content" contenteditable="true" data-testid="tweetTextarea_0" role="textbox" spellcheck="true" tabindex="0" no-focustrapview-refocus="true" style="outline: none; user-select: text; white-space: pre-wrap; overflow-wrap: break-word;">...</div> is not clickable at point (911, 14). Other element would receive the click: <a href="/home" role="tab" aria-selected="false" tabindex="-1" class="css-175oi2r r-1awozwy r-6koalj r-eqz5dr r-16y2uox r-1h3ijdo r-1777fci r-s8bhmr r-3pj75a r-o7ynqc r-6416eg r-1ny4l3l r-1loqt21">...</a>
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x40ba83+63395]
	GetHandleVerifier [0x0x40bac4+63460]
	(No symbol) [0x0x252113]
	(No symbol) [0x0x2a0ba0]
	(No symbol) [0x0x29ef5a]
	(No symbol) [0x0x29cab7]
	(No symbol) [0x0x29bd6d]
	(No symbol) [0x0x290515]
	(No symbol) [0x0x2bf3ac]
	(No symbol) [0x0x28ffa4]
	(No symbol) [0x0x2bf624]
	(No symbol) [0x0x2e07ba]
	(No symbol) [0x0x2bf1a6]
	(No symbol) [0x0x28e7b2]
	(No symbol) [0x0x28f654]
	GetHandleVerifier [0x0x688883+2672035]
	GetHandleVerifier [0x0x683cba+2652634]
	GetHandleVerifier [0x0x432bca+223466]
	GetHandleVerifier [0x0x422cb8+158168]
	GetHandleVerifier [0x0x42978d+185517]
	GetHandleVerifier [0x0x413b78+96408]
	GetHandleVerifier [0x0x413d02+96802]
	GetHandleVerifier [0x0x3fe90a+9770]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-31 01:27:48 | ERROR    | src.modules.posting.executor:_input_text_content:1626 | 输入文本内容失败: Message: element click intercepted: Element <div aria-activedescendant="typeaheadFocus-0.6071348947584942" aria-autocomplete="list" aria-controls="typeaheadDropdownWrapped-8" aria-describedby="placeholder-1sshp" aria-label="Post text" aria-multiline="true" class="notranslate public-DraftEditor-content" contenteditable="true" data-testid="tweetTextarea_0" role="textbox" spellcheck="true" tabindex="0" no-focustrapview-refocus="true" style="outline: none; user-select: text; white-space: pre-wrap; overflow-wrap: break-word;">...</div> is not clickable at point (911, 90). Other element would receive the click: <div class="css-175oi2r r-1p0dtai r-1d2f490 r-1xcajam r-zchlnj r-ipm5af r-1ffj0ar" data-testid="mask"></div>
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x40ba83+63395]
	GetHandleVerifier [0x0x40bac4+63460]
	(No symbol) [0x0x252113]
	(No symbol) [0x0x2a0ba0]
	(No symbol) [0x0x29ef5a]
	(No symbol) [0x0x29cab7]
	(No symbol) [0x0x29bd6d]
	(No symbol) [0x0x290515]
	(No symbol) [0x0x2bf3ac]
	(No symbol) [0x0x28ffa4]
	(No symbol) [0x0x2bf624]
	(No symbol) [0x0x2e07ba]
	(No symbol) [0x0x2bf1a6]
	(No symbol) [0x0x28e7b2]
	(No symbol) [0x0x28f654]
	GetHandleVerifier [0x0x688883+2672035]
	GetHandleVerifier [0x0x683cba+2652634]
	GetHandleVerifier [0x0x432bca+223466]
	GetHandleVerifier [0x0x422cb8+158168]
	GetHandleVerifier [0x0x42978d+185517]
	GetHandleVerifier [0x0x413b78+96408]
	GetHandleVerifier [0x0x413d02+96802]
	GetHandleVerifier [0x0x3fe90a+9770]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-31 01:27:48 | ERROR    | src.modules.posting.executor:execute_post:909 | ❌ 发帖失败: 内容输入失败: Message: element click intercepted: Element <div aria-activedescendant="typeaheadFocus-0.6071348947584942" aria-autocomplete="list" aria-controls="typeaheadDropdownWrapped-8" aria-describedby="placeholder-1sshp" aria-label="Post text" aria-multiline="true" class="notranslate public-DraftEditor-content" contenteditable="true" data-testid="tweetTextarea_0" role="textbox" spellcheck="true" tabindex="0" no-focustrapview-refocus="true" style="outline: none; user-select: text; white-space: pre-wrap; overflow-wrap: break-word;">...</div> is not clickable at point (911, 90). Other element would receive the click: <div class="css-175oi2r r-1p0dtai r-1d2f490 r-1xcajam r-zchlnj r-ipm5af r-1ffj0ar" data-testid="mask"></div>
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x40ba83+63395]
	GetHandleVerifier [0x0x40bac4+63460]
	(No symbol) [0x0x252113]
	(No symbol) [0x0x2a0ba0]
	(No symbol) [0x0x29ef5a]
	(No symbol) [0x0x29cab7]
	(No symbol) [0x0x29bd6d]
	(No symbol) [0x0x290515]
	(No symbol) [0x0x2bf3ac]
	(No symbol) [0x0x28ffa4]
	(No symbol) [0x0x2bf624]
	(No symbol) [0x0x2e07ba]
	(No symbol) [0x0x2bf1a6]
	(No symbol) [0x0x28e7b2]
	(No symbol) [0x0x28f654]
	GetHandleVerifier [0x0x688883+2672035]
	GetHandleVerifier [0x0x683cba+2652634]
	GetHandleVerifier [0x0x432bca+223466]
	GetHandleVerifier [0x0x422cb8+158168]
	GetHandleVerifier [0x0x42978d+185517]
	GetHandleVerifier [0x0x413b78+96408]
	GetHandleVerifier [0x0x413d02+96802]
	GetHandleVerifier [0x0x3fe90a+9770]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-31 01:30:11 | ERROR    | src.modules.posting.executor:_input_text_content:1626 | 输入文本内容失败: Message: element click intercepted: Element <div aria-activedescendant="typeaheadFocus-0.49657049766022354" aria-autocomplete="list" aria-controls="typeaheadDropdownWrapped-4" aria-describedby="placeholder-fbrn9" aria-label="Post text" aria-multiline="true" class="notranslate public-DraftEditor-content" contenteditable="true" data-testid="tweetTextarea_0" role="textbox" spellcheck="true" tabindex="0" no-focustrapview-refocus="true" style="outline: none; user-select: text; white-space: pre-wrap; overflow-wrap: break-word;">...</div> is not clickable at point (911, 90). Other element would receive the click: <div class="css-175oi2r r-1p0dtai r-1d2f490 r-1xcajam r-zchlnj r-ipm5af r-1ffj0ar" data-testid="mask"></div>
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x40ba83+63395]
	GetHandleVerifier [0x0x40bac4+63460]
	(No symbol) [0x0x252113]
	(No symbol) [0x0x2a0ba0]
	(No symbol) [0x0x29ef5a]
	(No symbol) [0x0x29cab7]
	(No symbol) [0x0x29bd6d]
	(No symbol) [0x0x290515]
	(No symbol) [0x0x2bf3ac]
	(No symbol) [0x0x28ffa4]
	(No symbol) [0x0x2bf624]
	(No symbol) [0x0x2e07ba]
	(No symbol) [0x0x2bf1a6]
	(No symbol) [0x0x28e7b2]
	(No symbol) [0x0x28f654]
	GetHandleVerifier [0x0x688883+2672035]
	GetHandleVerifier [0x0x683cba+2652634]
	GetHandleVerifier [0x0x432bca+223466]
	GetHandleVerifier [0x0x422cb8+158168]
	GetHandleVerifier [0x0x42978d+185517]
	GetHandleVerifier [0x0x413b78+96408]
	GetHandleVerifier [0x0x413d02+96802]
	GetHandleVerifier [0x0x3fe90a+9770]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-31 01:30:26 | ERROR    | src.modules.posting.executor:_input_text_content:1626 | 输入文本内容失败: Message: element click intercepted: Element <div aria-activedescendant="typeaheadFocus-0.9478149667166249" aria-autocomplete="list" aria-controls="typeaheadDropdownWrapped-1" aria-describedby="placeholder-fk443" aria-label="Post text" aria-multiline="true" class="notranslate public-DraftEditor-content" contenteditable="true" data-testid="tweetTextarea_0" role="textbox" spellcheck="true" tabindex="0" no-focustrapview-refocus="true" style="outline: none; user-select: text; white-space: pre-wrap; overflow-wrap: break-word;">...</div> is not clickable at point (911, 90). Other element would receive the click: <div class="css-175oi2r r-1p0dtai r-1d2f490 r-1xcajam r-zchlnj r-ipm5af r-1ffj0ar" data-testid="mask"></div>
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x40ba83+63395]
	GetHandleVerifier [0x0x40bac4+63460]
	(No symbol) [0x0x252113]
	(No symbol) [0x0x2a0ba0]
	(No symbol) [0x0x29ef5a]
	(No symbol) [0x0x29cab7]
	(No symbol) [0x0x29bd6d]
	(No symbol) [0x0x290515]
	(No symbol) [0x0x2bf3ac]
	(No symbol) [0x0x28ffa4]
	(No symbol) [0x0x2bf624]
	(No symbol) [0x0x2e07ba]
	(No symbol) [0x0x2bf1a6]
	(No symbol) [0x0x28e7b2]
	(No symbol) [0x0x28f654]
	GetHandleVerifier [0x0x688883+2672035]
	GetHandleVerifier [0x0x683cba+2652634]
	GetHandleVerifier [0x0x432bca+223466]
	GetHandleVerifier [0x0x422cb8+158168]
	GetHandleVerifier [0x0x42978d+185517]
	GetHandleVerifier [0x0x413b78+96408]
	GetHandleVerifier [0x0x413d02+96802]
	GetHandleVerifier [0x0x3fe90a+9770]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-31 01:58:19 | ERROR    | src.modules.posting.executor:_input_text_content:1626 | 输入文本内容失败: Message: element click intercepted: Element <div aria-activedescendant="typeaheadFocus-0.5721846602343191" aria-autocomplete="list" aria-controls="typeaheadDropdownWrapped-2" aria-describedby="placeholder-1de4f" aria-label="Post text" aria-multiline="true" class="notranslate public-DraftEditor-content" contenteditable="true" data-testid="tweetTextarea_0" role="textbox" spellcheck="true" tabindex="0" no-focustrapview-refocus="true" style="outline: none; user-select: text; white-space: pre-wrap; overflow-wrap: break-word;">...</div> is not clickable at point (911, 90). Other element would receive the click: <div class="css-175oi2r r-1p0dtai r-1d2f490 r-1xcajam r-zchlnj r-ipm5af r-1ffj0ar" data-testid="mask"></div>
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x99ba83+63395]
	GetHandleVerifier [0x0x99bac4+63460]
	(No symbol) [0x0x7e2113]
	(No symbol) [0x0x830ba0]
	(No symbol) [0x0x82ef5a]
	(No symbol) [0x0x82cab7]
	(No symbol) [0x0x82bd6d]
	(No symbol) [0x0x820515]
	(No symbol) [0x0x84f3ac]
	(No symbol) [0x0x81ffa4]
	(No symbol) [0x0x84f624]
	(No symbol) [0x0x8707ba]
	(No symbol) [0x0x84f1a6]
	(No symbol) [0x0x81e7b2]
	(No symbol) [0x0x81f654]
	GetHandleVerifier [0x0xc18883+2672035]
	GetHandleVerifier [0x0xc13cba+2652634]
	GetHandleVerifier [0x0x9c2bca+223466]
	GetHandleVerifier [0x0x9b2cb8+158168]
	GetHandleVerifier [0x0x9b978d+185517]
	GetHandleVerifier [0x0x9a3b78+96408]
	GetHandleVerifier [0x0x9a3d02+96802]
	GetHandleVerifier [0x0x98e90a+9770]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-31 01:58:36 | ERROR    | src.modules.posting.executor:_input_text_content:1626 | 输入文本内容失败: Message: element click intercepted: Element <div aria-activedescendant="typeaheadFocus-0.3788859867505662" aria-autocomplete="list" aria-controls="typeaheadDropdownWrapped-1" aria-describedby="placeholder-bqfa6" aria-label="Post text" aria-multiline="true" class="notranslate public-DraftEditor-content" contenteditable="true" data-testid="tweetTextarea_0" role="textbox" spellcheck="true" tabindex="0" no-focustrapview-refocus="true" style="outline: none; user-select: text; white-space: pre-wrap; overflow-wrap: break-word;">...</div> is not clickable at point (911, 14). Other element would receive the click: <a href="/home" role="tab" aria-selected="false" tabindex="-1" class="css-175oi2r r-1awozwy r-6koalj r-eqz5dr r-16y2uox r-1h3ijdo r-1777fci r-s8bhmr r-3pj75a r-o7ynqc r-6416eg r-1ny4l3l r-1loqt21">...</a>
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x99ba83+63395]
	GetHandleVerifier [0x0x99bac4+63460]
	(No symbol) [0x0x7e2113]
	(No symbol) [0x0x830ba0]
	(No symbol) [0x0x82ef5a]
	(No symbol) [0x0x82cab7]
	(No symbol) [0x0x82bd6d]
	(No symbol) [0x0x820515]
	(No symbol) [0x0x84f3ac]
	(No symbol) [0x0x81ffa4]
	(No symbol) [0x0x84f624]
	(No symbol) [0x0x8707ba]
	(No symbol) [0x0x84f1a6]
	(No symbol) [0x0x81e7b2]
	(No symbol) [0x0x81f654]
	GetHandleVerifier [0x0xc18883+2672035]
	GetHandleVerifier [0x0xc13cba+2652634]
	GetHandleVerifier [0x0x9c2bca+223466]
	GetHandleVerifier [0x0x9b2cb8+158168]
	GetHandleVerifier [0x0x9b978d+185517]
	GetHandleVerifier [0x0x9a3b78+96408]
	GetHandleVerifier [0x0x9a3d02+96802]
	GetHandleVerifier [0x0x98e90a+9770]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-31 01:58:52 | ERROR    | src.modules.posting.executor:_input_text_content:1626 | 输入文本内容失败: Message: element click intercepted: Element <div aria-activedescendant="typeaheadFocus-0.9444530112523873" aria-autocomplete="list" aria-controls="typeaheadDropdownWrapped-1" aria-describedby="placeholder-e7ski" aria-label="Post text" aria-multiline="true" class="notranslate public-DraftEditor-content" contenteditable="true" data-testid="tweetTextarea_0" role="textbox" spellcheck="true" tabindex="0" no-focustrapview-refocus="true" style="outline: none; user-select: text; white-space: pre-wrap; overflow-wrap: break-word;">...</div> is not clickable at point (911, 90). Other element would receive the click: <div class="css-175oi2r r-1p0dtai r-1d2f490 r-1xcajam r-zchlnj r-ipm5af r-1ffj0ar" data-testid="mask"></div>
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x99ba83+63395]
	GetHandleVerifier [0x0x99bac4+63460]
	(No symbol) [0x0x7e2113]
	(No symbol) [0x0x830ba0]
	(No symbol) [0x0x82ef5a]
	(No symbol) [0x0x82cab7]
	(No symbol) [0x0x82bd6d]
	(No symbol) [0x0x820515]
	(No symbol) [0x0x84f3ac]
	(No symbol) [0x0x81ffa4]
	(No symbol) [0x0x84f624]
	(No symbol) [0x0x8707ba]
	(No symbol) [0x0x84f1a6]
	(No symbol) [0x0x81e7b2]
	(No symbol) [0x0x81f654]
	GetHandleVerifier [0x0xc18883+2672035]
	GetHandleVerifier [0x0xc13cba+2652634]
	GetHandleVerifier [0x0x9c2bca+223466]
	GetHandleVerifier [0x0x9b2cb8+158168]
	GetHandleVerifier [0x0x9b978d+185517]
	GetHandleVerifier [0x0x9a3b78+96408]
	GetHandleVerifier [0x0x9a3d02+96802]
	GetHandleVerifier [0x0x98e90a+9770]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-31 01:58:52 | ERROR    | src.modules.posting.executor:execute_post:909 | ❌ 发帖失败: 内容输入失败: Message: element click intercepted: Element <div aria-activedescendant="typeaheadFocus-0.9444530112523873" aria-autocomplete="list" aria-controls="typeaheadDropdownWrapped-1" aria-describedby="placeholder-e7ski" aria-label="Post text" aria-multiline="true" class="notranslate public-DraftEditor-content" contenteditable="true" data-testid="tweetTextarea_0" role="textbox" spellcheck="true" tabindex="0" no-focustrapview-refocus="true" style="outline: none; user-select: text; white-space: pre-wrap; overflow-wrap: break-word;">...</div> is not clickable at point (911, 90). Other element would receive the click: <div class="css-175oi2r r-1p0dtai r-1d2f490 r-1xcajam r-zchlnj r-ipm5af r-1ffj0ar" data-testid="mask"></div>
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x99ba83+63395]
	GetHandleVerifier [0x0x99bac4+63460]
	(No symbol) [0x0x7e2113]
	(No symbol) [0x0x830ba0]
	(No symbol) [0x0x82ef5a]
	(No symbol) [0x0x82cab7]
	(No symbol) [0x0x82bd6d]
	(No symbol) [0x0x820515]
	(No symbol) [0x0x84f3ac]
	(No symbol) [0x0x81ffa4]
	(No symbol) [0x0x84f624]
	(No symbol) [0x0x8707ba]
	(No symbol) [0x0x84f1a6]
	(No symbol) [0x0x81e7b2]
	(No symbol) [0x0x81f654]
	GetHandleVerifier [0x0xc18883+2672035]
	GetHandleVerifier [0x0xc13cba+2652634]
	GetHandleVerifier [0x0x9c2bca+223466]
	GetHandleVerifier [0x0x9b2cb8+158168]
	GetHandleVerifier [0x0x9b978d+185517]
	GetHandleVerifier [0x0x9a3b78+96408]
	GetHandleVerifier [0x0x9a3d02+96802]
	GetHandleVerifier [0x0x98e90a+9770]
	BaseThreadInitThunk [0x0x76185d49+25]
	RtlInitializeExceptionChain [0x0x7714d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x7714d131+561]

2025-07-31 02:02:18 | ERROR    | src.modules.posting.executor:_verify_media_upload_elegantly:1413 | 媒体验证异常: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-31 02:07:54 | ERROR    | src.modules.posting.executor:_input_text_content:1626 | 输入文本内容失败: Message: element click intercepted: Element <div aria-activedescendant="typeaheadFocus-0.1628223157550136" aria-autocomplete="list" aria-controls="typeaheadDropdownWrapped-1" aria-describedby="placeholder-96muc" aria-label="Post text" aria-multiline="true" class="notranslate public-DraftEditor-content" contenteditable="true" data-testid="tweetTextarea_0" role="textbox" spellcheck="true" tabindex="0" no-focustrapview-refocus="true" style="outline: none; user-select: text; white-space: pre-wrap; overflow-wrap: break-word;">...</div> is not clickable at point (911, 90). Other element would receive the click: <div class="css-175oi2r r-1p0dtai r-1d2f490 r-1xcajam r-zchlnj r-ipm5af r-1ffj0ar" data-testid="mask"></div>
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x38ba83+63395]
	GetHandleVerifier [0x0x38bac4+63460]
	(No symbol) [0x0x1d2113]
	(No symbol) [0x0x220ba0]
	(No symbol) [0x0x21ef5a]
	(No symbol) [0x0x21cab7]
	(No symbol) [0x0x21bd6d]
	(No symbol) [0x0x210515]
	(No symbol) [0x0x23f3ac]
	(No symbol) [0x0x20ffa4]
	(No symbol) [0x0x23f624]
	(No symbol) [0x0x2607ba]
	(No symbol) [0x0x23f1a6]
	(No symbol) [0x0x20e7b2]
	(No symbol) [0x0x20f654]
	GetHandleVerifier [0x0x608883+2672035]
	GetHandleVerifier [0x0x603cba+2652634]
	GetHandleVerifier [0x0x3b2bca+223466]
	GetHandleVerifier [0x0x3a2cb8+158168]
	GetHandleVerifier [0x0x3a978d+185517]
	GetHandleVerifier [0x0x393b78+96408]
	GetHandleVerifier [0x0x393d02+96802]
	GetHandleVerifier [0x0x37e90a+9770]
	BaseThreadInitThunk [0x0x76155d49+25]
	RtlInitializeExceptionChain [0x0x77d1d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x77d1d131+561]

2025-07-31 02:08:25 | ERROR    | src.modules.posting.executor:_find_element_elegantly:787 | ❌ 无法找到元素: tweet_textbox
2025-07-31 02:08:25 | ERROR    | src.modules.posting.executor:execute_post:909 | ❌ 发帖失败: 无法找到推文输入框
2025-07-31 02:08:58 | ERROR    | src.modules.posting.executor:_input_text_content:1626 | 输入文本内容失败: Message: element click intercepted: Element <div aria-activedescendant="typeaheadFocus-0.6753827984791735" aria-autocomplete="list" aria-controls="typeaheadDropdownWrapped-1" aria-describedby="placeholder-e515c" aria-label="Post text" aria-multiline="true" class="notranslate public-DraftEditor-content" contenteditable="true" data-testid="tweetTextarea_0" role="textbox" spellcheck="true" tabindex="0" no-focustrapview-refocus="true" style="outline: none; user-select: text; white-space: pre-wrap; overflow-wrap: break-word;">...</div> is not clickable at point (911, 90). Other element would receive the click: <div class="css-175oi2r r-1p0dtai r-1d2f490 r-1xcajam r-zchlnj r-ipm5af r-1ffj0ar" data-testid="mask"></div>
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x38ba83+63395]
	GetHandleVerifier [0x0x38bac4+63460]
	(No symbol) [0x0x1d2113]
	(No symbol) [0x0x220ba0]
	(No symbol) [0x0x21ef5a]
	(No symbol) [0x0x21cab7]
	(No symbol) [0x0x21bd6d]
	(No symbol) [0x0x210515]
	(No symbol) [0x0x23f3ac]
	(No symbol) [0x0x20ffa4]
	(No symbol) [0x0x23f624]
	(No symbol) [0x0x2607ba]
	(No symbol) [0x0x23f1a6]
	(No symbol) [0x0x20e7b2]
	(No symbol) [0x0x20f654]
	GetHandleVerifier [0x0x608883+2672035]
	GetHandleVerifier [0x0x603cba+2652634]
	GetHandleVerifier [0x0x3b2bca+223466]
	GetHandleVerifier [0x0x3a2cb8+158168]
	GetHandleVerifier [0x0x3a978d+185517]
	GetHandleVerifier [0x0x393b78+96408]
	GetHandleVerifier [0x0x393d02+96802]
	GetHandleVerifier [0x0x37e90a+9770]
	BaseThreadInitThunk [0x0x76155d49+25]
	RtlInitializeExceptionChain [0x0x77d1d1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x77d1d131+561]

2025-07-31 02:09:34 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:387 | 互动异常: loves_lul49315, comment, Task <Task pending name='Task-27' coro=<ConcurrentInteractionExecutor._execute_single_interaction() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:344> cb=[_release_waiter(<Future pendi...EB47F2BB0>()]>)() at D:\python39\lib\asyncio\tasks.py:416]> got Future <Future pending> attached to a different loop
2025-07-31 02:09:34 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:387 | 互动异常: SebastianS49417, retweet, Task <Task pending name='Task-28' coro=<ConcurrentInteractionExecutor._execute_single_interaction() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:344> cb=[_release_waiter(<Future pendi...EB47F2DF0>()]>)() at D:\python39\lib\asyncio\tasks.py:416]> got Future <Future pending> attached to a different loop
2025-07-31 02:09:34 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:387 | 互动异常: loves_lul49315, like, Task <Task pending name='Task-29' coro=<ConcurrentInteractionExecutor._execute_single_interaction() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:344> cb=[_release_waiter(<Future pendi...EB47F2DC0>()]>)() at D:\python39\lib\asyncio\tasks.py:416]> got Future <Future pending> attached to a different loop
2025-07-31 02:09:34 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:387 | 互动异常: Remington852245, retweet, Task <Task pending name='Task-30' coro=<ConcurrentInteractionExecutor._execute_single_interaction() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:344> cb=[_release_waiter(<Future pendi...EB47F2490>()]>)() at D:\python39\lib\asyncio\tasks.py:416]> got Future <Future pending> attached to a different loop
2025-07-31 02:09:36 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:387 | 互动异常: loves_lul49315, comment, Task <Task pending name='Task-31' coro=<ConcurrentInteractionExecutor._execute_single_interaction() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:344> cb=[_release_waiter(<Future pendi...EB4672370>()]>)() at D:\python39\lib\asyncio\tasks.py:416]> got Future <Future pending> attached to a different loop
2025-07-31 02:09:36 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:387 | 互动异常: SebastianS49417, retweet, Task <Task pending name='Task-32' coro=<ConcurrentInteractionExecutor._execute_single_interaction() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:344> cb=[_release_waiter(<Future pendi...EB46727F0>()]>)() at D:\python39\lib\asyncio\tasks.py:416]> got Future <Future pending> attached to a different loop
2025-07-31 02:09:36 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:387 | 互动异常: loves_lul49315, like, Task <Task pending name='Task-33' coro=<ConcurrentInteractionExecutor._execute_single_interaction() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:344> cb=[_release_waiter(<Future pendi...EB48004C0>()]>)() at D:\python39\lib\asyncio\tasks.py:416]> got Future <Future pending> attached to a different loop
2025-07-31 02:09:36 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:387 | 互动异常: Remington852245, retweet, Task <Task pending name='Task-34' coro=<ConcurrentInteractionExecutor._execute_single_interaction() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:344> cb=[_release_waiter(<Future pendi...EB4800A60>()]>)() at D:\python39\lib\asyncio\tasks.py:416]> got Future <Future pending> attached to a different loop
2025-07-31 02:09:41 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:387 | 互动异常: SebastianS49417, retweet, Task <Task pending name='Task-35' coro=<ConcurrentInteractionExecutor._execute_single_interaction() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:344> cb=[_release_waiter(<Future pendi...EB47CCA90>()]>)() at D:\python39\lib\asyncio\tasks.py:416]> got Future <Future pending> attached to a different loop
2025-07-31 02:09:41 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:387 | 互动异常: Remington852245, retweet, Task <Task pending name='Task-36' coro=<ConcurrentInteractionExecutor._execute_single_interaction() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:344> cb=[_release_waiter(<Future pendi...EB47CC9A0>()]>)() at D:\python39\lib\asyncio\tasks.py:416]> got Future <Future pending> attached to a different loop
2025-07-31 02:09:41 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:387 | 互动异常: loves_lul49315, like, Task <Task pending name='Task-37' coro=<ConcurrentInteractionExecutor._execute_single_interaction() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:344> cb=[_release_waiter(<Future pendi...EB47CC7F0>()]>)() at D:\python39\lib\asyncio\tasks.py:416]> got Future <Future pending> attached to a different loop
2025-07-31 02:09:41 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:387 | 互动异常: loves_lul49315, comment, Task <Task pending name='Task-38' coro=<ConcurrentInteractionExecutor._execute_single_interaction() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:344> cb=[_release_waiter(<Future pendi...EB47CC9D0>()]>)() at D:\python39\lib\asyncio\tasks.py:416]> got Future <Future pending> attached to a different loop
2025-07-31 02:09:51 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:387 | 互动异常: Remington852245, retweet, Task <Task pending name='Task-47' coro=<ConcurrentInteractionExecutor._execute_single_interaction() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:344> cb=[_release_waiter(<Future pendi...EB47CC2E0>()]>)() at D:\python39\lib\asyncio\tasks.py:416]> got Future <Future pending> attached to a different loop
2025-07-31 02:09:51 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:387 | 互动异常: loves_lul49315, like, Task <Task pending name='Task-48' coro=<ConcurrentInteractionExecutor._execute_single_interaction() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:344> cb=[_release_waiter(<Future pendi...EB47CC5E0>()]>)() at D:\python39\lib\asyncio\tasks.py:416]> got Future <Future pending> attached to a different loop
2025-07-31 02:09:51 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:387 | 互动异常: loves_lul49315, comment, Task <Task pending name='Task-49' coro=<ConcurrentInteractionExecutor._execute_single_interaction() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:344> cb=[_release_waiter(<Future pendi...EB47CCE50>()]>)() at D:\python39\lib\asyncio\tasks.py:416]> got Future <Future pending> attached to a different loop
2025-07-31 02:09:51 | ERROR    | src.modules.interaction.concurrent_executor:_execute_task_with_recovery:261 | 💥 互动最终失败: Remington852245 - retweet (重试 3 次)
2025-07-31 02:09:51 | ERROR    | src.modules.interaction.concurrent_executor:_execute_task_with_recovery:261 | 💥 互动最终失败: loves_lul49315 - like (重试 3 次)
2025-07-31 02:09:51 | ERROR    | src.modules.interaction.concurrent_executor:_execute_task_with_recovery:261 | 💥 互动最终失败: loves_lul49315 - comment (重试 3 次)
2025-07-31 02:09:52 | ERROR    | src.modules.posting.executor:login_account:475 | 登录异常: Remington852245 (尝试 1/3), Task <Task pending name='Task-45' coro=<ConcurrentInteractionExecutor._execute_single_interaction() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:350> cb=[_release_waiter(<Future pendi...EB25E96D0>()]>)() at D:\python39\lib\asyncio\tasks.py:416]> got Future <Future pending> attached to a different loop
2025-07-31 02:09:53 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:387 | 互动异常: SebastianS49417, comment, Task <Task pending name='Task-50' coro=<ConcurrentInteractionExecutor._execute_single_interaction() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:344> cb=[_release_waiter(<Future pendi...ECFB81910>()]>)() at D:\python39\lib\asyncio\tasks.py:416]> got Future <Future pending> attached to a different loop
2025-07-31 02:09:53 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:387 | 互动异常: EthPrice61351, like, Task <Task pending name='Task-51' coro=<ConcurrentInteractionExecutor._execute_single_interaction() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:344> cb=[_release_waiter(<Future pendi...ECFB81C70>()]>)() at D:\python39\lib\asyncio\tasks.py:416]> got Future <Future pending> attached to a different loop
2025-07-31 02:09:53 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:387 | 互动异常: EthPrice61351, retweet, Task <Task pending name='Task-52' coro=<ConcurrentInteractionExecutor._execute_single_interaction() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:344> cb=[_release_waiter(<Future pendi...ECFB81880>()]>)() at D:\python39\lib\asyncio\tasks.py:416]> got Future <Future pending> attached to a different loop
2025-07-31 02:09:55 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:387 | 互动异常: SebastianS49417, comment, Task <Task pending name='Task-53' coro=<ConcurrentInteractionExecutor._execute_single_interaction() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:344> cb=[_release_waiter(<Future pendi...ECF6233D0>()]>)() at D:\python39\lib\asyncio\tasks.py:416]> got Future <Future pending> attached to a different loop
2025-07-31 02:09:55 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:387 | 互动异常: EthPrice61351, like, Task <Task pending name='Task-54' coro=<ConcurrentInteractionExecutor._execute_single_interaction() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:344> cb=[_release_waiter(<Future pendi...EADE11D30>()]>)() at D:\python39\lib\asyncio\tasks.py:416]> got Future <Future pending> attached to a different loop
2025-07-31 02:09:55 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:387 | 互动异常: EthPrice61351, retweet, Task <Task pending name='Task-55' coro=<ConcurrentInteractionExecutor._execute_single_interaction() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:344> cb=[_release_waiter(<Future pendi...EADE11D00>()]>)() at D:\python39\lib\asyncio\tasks.py:416]> got Future <Future pending> attached to a different loop
2025-07-31 02:09:58 | ERROR    | src.modules.posting.executor:login_account:475 | 登录异常: Remington852245 (尝试 2/3), Task <Task pending name='Task-45' coro=<ConcurrentInteractionExecutor._execute_single_interaction() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:350> cb=[_release_waiter(<Future pendi...EB25E96D0>()]>)() at D:\python39\lib\asyncio\tasks.py:416]> got Future <Future pending> attached to a different loop
2025-07-31 02:10:00 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:387 | 互动异常: SebastianS49417, comment, Task <Task pending name='Task-56' coro=<ConcurrentInteractionExecutor._execute_single_interaction() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:344> cb=[_release_waiter(<Future pendi...ECF623D30>()]>)() at D:\python39\lib\asyncio\tasks.py:416]> got Future <Future pending> attached to a different loop
2025-07-31 02:10:00 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:387 | 互动异常: EthPrice61351, retweet, Task <Task pending name='Task-64' coro=<ConcurrentInteractionExecutor._execute_single_interaction() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:344> cb=[_release_waiter(<Future pendi...EB4800910>()]>)() at D:\python39\lib\asyncio\tasks.py:416]> got Future <Future pending> attached to a different loop
2025-07-31 02:10:04 | ERROR    | src.modules.posting.executor:login_account:475 | 登录异常: Remington852245 (尝试 3/3), Task <Task pending name='Task-45' coro=<ConcurrentInteractionExecutor._execute_single_interaction() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:350> cb=[_release_waiter(<Future pendi...EB25E96D0>()]>)() at D:\python39\lib\asyncio\tasks.py:416]> got Future <Future pending> attached to a different loop
2025-07-31 02:10:04 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:387 | 互动异常: Remington852245, like, 账号登录失败
2025-07-31 02:10:11 | ERROR    | src.modules.interaction.concurrent_executor:_recover_browser_session:323 | 浏览器会话恢复异常: SebastianS49417, 'BrowserPool' object has no attribute 'release_driver'
2025-07-31 02:10:11 | ERROR    | src.modules.interaction.concurrent_executor:_execute_task_with_recovery:261 | 💥 互动最终失败: SebastianS49417 - retweet (重试 3 次)
2025-07-31 02:10:16 | ERROR    | src.modules.interaction.concurrent_executor:_recover_browser_session:323 | 浏览器会话恢复异常: EthPrice61351, 'BrowserPool' object has no attribute 'release_driver'
2025-07-31 02:10:24 | ERROR    | src.modules.interaction.concurrent_executor:_recover_browser_session:323 | 浏览器会话恢复异常: Remington852245, 'BrowserPool' object has no attribute 'release_driver'
2025-07-31 02:10:30 | ERROR    | src.modules.interaction.concurrent_executor:_recover_browser_session:323 | 浏览器会话恢复异常: EthPrice61351, 'BrowserPool' object has no attribute 'release_driver'
2025-07-31 02:10:30 | ERROR    | src.modules.interaction.concurrent_executor:_execute_task_with_recovery:261 | 💥 互动最终失败: EthPrice61351 - retweet (重试 3 次)
2025-07-31 02:10:31 | ERROR    | src.modules.posting.executor:login_account:475 | 登录异常: JombolioE57643 (尝试 1/3), Task <Task pending name='Task-100' coro=<ConcurrentInteractionExecutor._execute_single_interaction() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:350> cb=[_release_waiter(<Future pendi...ECF82A7F0>()]>)() at D:\python39\lib\asyncio\tasks.py:416]> got Future <Future pending> attached to a different loop
2025-07-31 02:10:31 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:387 | 互动异常: JombolioE57643, comment, Task <Task pending name='Task-101' coro=<ConcurrentInteractionExecutor._execute_single_interaction() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:344> cb=[_release_waiter(<Future pendi...ECF82A460>()]>)() at D:\python39\lib\asyncio\tasks.py:416]> got Future <Future pending> attached to a different loop
2025-07-31 02:10:33 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:387 | 互动异常: JombolioE57643, comment, Task <Task pending name='Task-102' coro=<ConcurrentInteractionExecutor._execute_single_interaction() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:344> cb=[_release_waiter(<Future pendi...ECFBA8700>()]>)() at D:\python39\lib\asyncio\tasks.py:416]> got Future <Future pending> attached to a different loop
2025-07-31 02:10:40 | ERROR    | src.modules.interaction.concurrent_executor:_recover_browser_session:323 | 浏览器会话恢复异常: SebastianS49417, 'BrowserPool' object has no attribute 'release_driver'
2025-07-31 02:10:40 | ERROR    | src.modules.interaction.concurrent_executor:_execute_task_with_recovery:261 | 💥 互动最终失败: SebastianS49417 - comment (重试 3 次)
2025-07-31 02:10:41 | ERROR    | src.modules.interaction.concurrent_executor:_recover_browser_session:323 | 浏览器会话恢复异常: EthPrice61351, 'BrowserPool' object has no attribute 'release_driver'
2025-07-31 02:10:41 | ERROR    | src.modules.interaction.concurrent_executor:_execute_task_with_recovery:261 | 💥 互动最终失败: EthPrice61351 - like (重试 3 次)
2025-07-31 02:10:42 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:387 | 互动异常: mp_ser29877, comment, Task <Task pending name='Task-130' coro=<ConcurrentInteractionExecutor._execute_single_interaction() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:344> cb=[_release_waiter(<Future pendi...EB46ED1C0>()]>)() at D:\python39\lib\asyncio\tasks.py:416]> got Future <Future pending> attached to a different loop
2025-07-31 02:10:45 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:387 | 互动异常: mp_ser29877, comment, Task <Task pending name='Task-131' coro=<ConcurrentInteractionExecutor._execute_single_interaction() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:344> cb=[_release_waiter(<Future pendi...EB46BF7F0>()]>)() at D:\python39\lib\asyncio\tasks.py:416]> got Future <Future pending> attached to a different loop
2025-07-31 02:10:49 | ERROR    | src.modules.interaction.concurrent_executor:_recover_browser_session:323 | 浏览器会话恢复异常: Remington852245, 'BrowserPool' object has no attribute 'release_driver'
2025-07-31 02:10:49 | ERROR    | src.modules.interaction.concurrent_executor:_execute_task_with_recovery:261 | 💥 互动最终失败: Remington852245 - like (重试 3 次)
2025-07-31 02:10:50 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:387 | 互动异常: mp_ser29877, comment, Task <Task pending name='Task-133' coro=<ConcurrentInteractionExecutor._execute_single_interaction() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:344> cb=[_release_waiter(<Future pendi...EB46C1970>()]>)() at D:\python39\lib\asyncio\tasks.py:416]> got Future <Future pending> attached to a different loop
2025-07-31 02:11:00 | ERROR    | src.modules.interaction.concurrent_executor:_execute_single_interaction:387 | 互动异常: mp_ser29877, comment, Task <Task pending name='Task-140' coro=<ConcurrentInteractionExecutor._execute_single_interaction() running at D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (10) - 副本 - 副本\src\modules\interaction\concurrent_executor.py:344> cb=[_release_waiter(<Future pendi...EB472FE50>()]>)() at D:\python39\lib\asyncio\tasks.py:416]> got Future <Future pending> attached to a different loop
2025-07-31 02:11:00 | ERROR    | src.modules.interaction.concurrent_executor:_execute_task_with_recovery:261 | 💥 互动最终失败: mp_ser29877 - comment (重试 3 次)
2025-07-31 02:11:05 | ERROR    | src.modules.interaction.concurrent_executor:_recover_browser_session:323 | 浏览器会话恢复异常: JombolioE57643, 'BrowserPool' object has no attribute 'release_driver'
2025-07-31 02:11:08 | ERROR    | src.modules.interaction.concurrent_executor:_recover_browser_session:323 | 浏览器会话恢复异常: JombolioE57643, 'BrowserPool' object has no attribute 'release_driver'
2025-07-31 02:11:30 | ERROR    | src.modules.interaction.concurrent_executor:_recover_browser_session:323 | 浏览器会话恢复异常: JombolioE57643, 'BrowserPool' object has no attribute 'release_driver'
2025-07-31 02:11:30 | ERROR    | src.modules.interaction.concurrent_executor:_execute_task_with_recovery:261 | 💥 互动最终失败: JombolioE57643 - like (重试 3 次)
2025-07-31 02:11:48 | ERROR    | src.modules.interaction.concurrent_executor:_recover_browser_session:323 | 浏览器会话恢复异常: mp_ser29877, 'BrowserPool' object has no attribute 'release_driver'
2025-07-31 02:11:48 | ERROR    | src.modules.interaction.concurrent_executor:_recover_browser_session:323 | 浏览器会话恢复异常: JombolioE57643, 'BrowserPool' object has no attribute 'release_driver'
2025-07-31 02:11:48 | ERROR    | src.modules.interaction.concurrent_executor:_execute_task_with_recovery:261 | 💥 互动最终失败: JombolioE57643 - comment (重试 3 次)
2025-07-31 02:12:18 | ERROR    | src.modules.interaction.concurrent_executor:_recover_browser_session:323 | 浏览器会话恢复异常: mp_ser29877, 'BrowserPool' object has no attribute 'release_driver'
2025-07-31 02:12:18 | ERROR    | src.modules.interaction.concurrent_executor:_execute_task_with_recovery:261 | 💥 互动最终失败: mp_ser29877 - retweet (重试 3 次)
2025-07-31 02:45:25 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:625 | Chrome启动彻底失败: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-31 16:51:02 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:701 | 所有Chrome创建方式都失败: Message: session not created: cannot connect to chrome at 127.0.0.1:13874
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x95ba83+63395]
	GetHandleVerifier [0x0x95bac4+63460]
	(No symbol) [0x0x7a1f70]
	(No symbol) [0x0x795f1a]
	(No symbol) [0x0x7dadd6]
	(No symbol) [0x0x7d128f]
	(No symbol) [0x0x7d10c6]
	(No symbol) [0x0x81ae57]
	(No symbol) [0x0x81a74a]
	(No symbol) [0x0x80f1a6]
	(No symbol) [0x0x7de7b2]
	(No symbol) [0x0x7df654]
	GetHandleVerifier [0x0xbd8883+2672035]
	GetHandleVerifier [0x0xbd3cba+2652634]
	GetHandleVerifier [0x0x982bca+223466]
	GetHandleVerifier [0x0x972cb8+158168]
	GetHandleVerifier [0x0x97978d+185517]
	GetHandleVerifier [0x0x963b78+96408]
	GetHandleVerifier [0x0x963d02+96802]
	GetHandleVerifier [0x0x94e90a+9770]
	BaseThreadInitThunk [0x0x761e5d49+25]
	RtlInitializeExceptionChain [0x0x76fad1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x76fad131+561]

2025-07-31 16:53:05 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:701 | 所有Chrome创建方式都失败: Message: session not created: cannot connect to chrome at 127.0.0.1:32162
from chrome not reachable
Stacktrace:
	GetHandleVerifier [0x0x3dba83+63395]
	GetHandleVerifier [0x0x3dbac4+63460]
	(No symbol) [0x0x221f70]
	(No symbol) [0x0x215f1a]
	(No symbol) [0x0x25add6]
	(No symbol) [0x0x25128f]
	(No symbol) [0x0x2510c6]
	(No symbol) [0x0x29ae57]
	(No symbol) [0x0x29a74a]
	(No symbol) [0x0x28f1a6]
	(No symbol) [0x0x25e7b2]
	(No symbol) [0x0x25f654]
	GetHandleVerifier [0x0x658883+2672035]
	GetHandleVerifier [0x0x653cba+2652634]
	GetHandleVerifier [0x0x402bca+223466]
	GetHandleVerifier [0x0x3f2cb8+158168]
	GetHandleVerifier [0x0x3f978d+185517]
	GetHandleVerifier [0x0x3e3b78+96408]
	GetHandleVerifier [0x0x3e3d02+96802]
	GetHandleVerifier [0x0x3ce90a+9770]
	BaseThreadInitThunk [0x0x761e5d49+25]
	RtlInitializeExceptionChain [0x0x76fad1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x76fad131+561]

2025-07-31 16:54:59 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:701 | 所有Chrome创建方式都失败: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-31 16:54:59 | ERROR    | src.core.browser_manager:create_chrome_with_complete_cleanup:719 | Chrome启动彻底失败: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
