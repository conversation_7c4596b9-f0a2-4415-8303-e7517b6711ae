#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示启动控制 vs 执行并发的区别
"""

import asyncio
import time
from datetime import datetime

def log_time(message):
    """带时间戳的日志"""
    current_time = datetime.now().strftime("%H:%M:%S.%f")[:-3]
    print(f"[{current_time}] {message}")

async def simulate_chrome_startup(chrome_id, startup_semaphore):
    """模拟Chrome启动过程"""
    
    # 🚀 启动控制阶段 - 受信号量限制
    async with startup_semaphore:
        log_time(f"🚀 开始启动 Chrome{chrome_id}")
        
        # 模拟Chrome启动时间（20-25秒）
        startup_time = 3 + chrome_id * 0.5  # 模拟不同的启动时间
        await asyncio.sleep(startup_time)
        
        log_time(f"✅ Chrome{chrome_id} 启动完成 (耗时{startup_time}秒)")
    
    return f"Chrome{chrome_id}"

async def simulate_interaction(chrome_id, action):
    """模拟互动执行过程"""
    log_time(f"🎯 Chrome{chrome_id} 开始执行 {action}")
    
    # 模拟互动执行时间（3-8秒）
    execution_time = 2 + (chrome_id % 3)
    await asyncio.sleep(execution_time)
    
    log_time(f"✅ Chrome{chrome_id} 完成 {action} (耗时{execution_time}秒)")
    return f"Chrome{chrome_id}-{action}"

async def demo_startup_control():
    """演示启动控制的效果"""
    print("🚀 演示：启动控制 vs 执行并发")
    print("=" * 60)
    
    # 创建启动信号量（最多同时启动2个）
    startup_semaphore = asyncio.Semaphore(2)
    
    log_time("📋 阶段1: Chrome启动阶段（受信号量控制）")
    print("   信号量限制：最多同时启动2个Chrome")
    print()
    
    # 🚀 启动阶段 - 受信号量控制
    startup_start_time = time.time()
    
    startup_tasks = [
        simulate_chrome_startup(i, startup_semaphore) 
        for i in range(1, 6)
    ]
    
    # 启动所有Chrome（但受信号量控制）
    chrome_instances = await asyncio.gather(*startup_tasks)
    
    startup_end_time = time.time()
    startup_duration = startup_end_time - startup_start_time
    
    print()
    log_time(f"🎉 所有Chrome启动完成！总耗时: {startup_duration:.1f}秒")
    log_time(f"📊 启动的Chrome: {chrome_instances}")
    print()
    
    # 🎯 执行阶段 - 完全并发，无限制
    log_time("📋 阶段2: 互动执行阶段（完全并发，无限制）")
    print("   所有Chrome同时执行互动，真正的并发！")
    print()
    
    execution_start_time = time.time()
    
    # 定义互动任务
    interactions = [
        ("点赞", 1),
        ("转发", 2), 
        ("评论", 3),
        ("点赞", 4),
        ("转发", 5)
    ]
    
    # 🚀 完全并发执行互动
    interaction_tasks = [
        simulate_interaction(chrome_id, action)
        for (action, chrome_id) in interactions
    ]
    
    # 所有互动同时执行！
    results = await asyncio.gather(*interaction_tasks)
    
    execution_end_time = time.time()
    execution_duration = execution_end_time - execution_start_time
    
    print()
    log_time(f"🎉 所有互动完成！执行耗时: {execution_duration:.1f}秒")
    log_time(f"📊 执行结果: {results}")
    
    # 总结
    total_duration = startup_duration + execution_duration
    print()
    print("=" * 60)
    print("📊 性能总结")
    print("=" * 60)
    print(f"  启动阶段耗时: {startup_duration:.1f}秒 (受信号量控制)")
    print(f"  执行阶段耗时: {execution_duration:.1f}秒 (完全并发)")
    print(f"  总耗时: {total_duration:.1f}秒")
    print()
    print("✅ 关键点:")
    print("  - 启动控制确保了100%成功率")
    print("  - 执行阶段是完全并发的")
    print("  - 最终实现了5个任务同时执行")
    print("  - 启动控制不影响执行并发性能")

async def demo_without_control():
    """演示没有启动控制的情况（模拟）"""
    print("\n🔥 对比：没有启动控制的情况（模拟）")
    print("=" * 60)
    
    log_time("❌ 模拟同时启动5个Chrome（无控制）")
    
    # 模拟资源竞争导致的失败
    import random
    
    startup_results = []
    for i in range(1, 6):
        # 模拟启动成功率只有60%
        if random.random() < 0.6:
            startup_results.append(f"Chrome{i}")
            log_time(f"✅ Chrome{i} 启动成功")
        else:
            log_time(f"❌ Chrome{i} 启动失败 (资源竞争)")
    
    print()
    log_time(f"📊 启动结果: {len(startup_results)}/5 成功")
    log_time(f"📊 成功的Chrome: {startup_results}")
    
    if len(startup_results) < 5:
        print()
        print("⚠️  结果分析:")
        print(f"  - 只有 {len(startup_results)} 个Chrome启动成功")
        print(f"  - 失败了 {5 - len(startup_results)} 个Chrome")
        print("  - 无法实现预期的5个并发任务")
        print("  - 整体性能下降")

async def main():
    """主演示函数"""
    await demo_startup_control()
    await demo_without_control()
    
    print("\n" + "=" * 60)
    print("🎯 结论")
    print("=" * 60)
    print("启动控制（信号量=2）的作用：")
    print("  ✅ 确保Chrome启动成功率100%")
    print("  ✅ 避免系统资源竞争")
    print("  ✅ 最终实现完全并发执行")
    print("  ✅ 不影响执行阶段的并发性能")
    print()
    print("📋 启动控制 ≠ 执行限制")
    print("  - 启动阶段：有序启动，确保成功")
    print("  - 执行阶段：完全并发，无限制")
    print("  - 最终效果：5个任务同时执行")

if __name__ == "__main__":
    asyncio.run(main())
