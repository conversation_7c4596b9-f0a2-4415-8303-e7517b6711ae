#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试当前修复效果 - 验证Chrome冲突和并发执行修复
"""

import asyncio
import sys
import os
import time
from dataclasses import dataclass
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

@dataclass
class MockAccount:
    """完整的模拟账号类"""
    id: int
    username: str
    password: str = "test_password"
    email: str = "<EMAIL>"
    proxy: str = None
    user_agent: str = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    cookies: str = None
    cookies_dict: dict = None
    token: str = None
    status: str = "active"
    group_id: int = 1
    created_at: str = "2024-01-01"
    updated_at: str = "2024-01-01"

async def test_chrome_port_management():
    """测试Chrome端口管理"""
    print("🔧 测试Chrome端口管理...")
    
    try:
        from src.core.browser_manager import ChromePortManager
        
        port_manager = ChromePortManager()
        
        # 测试并发端口分配
        print("1. 测试并发端口分配...")
        
        async def allocate_port_for_account(account_id):
            port = await port_manager.allocate_port(account_id)
            return account_id, port
        
        # 模拟5个账号同时请求端口
        tasks = [allocate_port_for_account(i) for i in range(1, 6)]
        results = await asyncio.gather(*tasks)
        
        ports = [result[1] for result in results]
        print(f"  分配的端口: {ports}")
        
        # 验证端口不重复
        if len(set(ports)) == len(ports):
            print("  ✅ 端口分配无冲突")
            return True
        else:
            print("  ❌ 端口分配有冲突")
            return False
            
    except Exception as e:
        print(f"  ❌ 端口管理测试失败: {e}")
        return False

async def test_browser_pool_startup_control():
    """测试浏览器池启动控制"""
    print("\n🔧 测试浏览器池启动控制...")
    
    try:
        from src.core.browser_manager import BrowserPool
        
        browser_pool = BrowserPool()
        
        # 测试启动信号量
        print("1. 测试启动信号量配置...")
        semaphore_count = browser_pool.startup_semaphore._value
        print(f"  启动信号量计数: {semaphore_count}")
        
        if semaphore_count == 2:
            print("  ✅ 启动信号量配置正确")
        else:
            print("  ❌ 启动信号量配置错误")
            return False
        
        # 测试端口管理器集成
        print("2. 测试端口管理器集成...")
        port1 = await browser_pool.port_manager.allocate_port(1)
        port2 = await browser_pool.port_manager.allocate_port(2)
        
        print(f"  浏览器池分配端口1: {port1}")
        print(f"  浏览器池分配端口2: {port2}")
        
        if port1 != port2:
            print("  ✅ 浏览器池端口管理正常")
            return True
        else:
            print("  ❌ 浏览器池端口管理有问题")
            return False
            
    except Exception as e:
        print(f"  ❌ 浏览器池测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_concurrent_executor_logic():
    """测试并发执行器逻辑"""
    print("\n🔧 测试并发执行器逻辑...")
    
    try:
        from src.modules.interaction.concurrent_executor import ConcurrentInteractionExecutor
        
        # 创建执行器
        executor = ConcurrentInteractionExecutor()
        
        # 测试基本功能
        print("1. 测试基本功能...")
        stats = executor.get_error_stats()
        print(f"  错误统计: {stats}")
        
        config = executor.recovery_config
        print(f"  恢复配置: {config}")
        
        task_times = executor.task_times
        print(f"  任务时间配置: {task_times}")
        
        # 创建模拟账号
        mock_accounts = [
            MockAccount(i, f"test_user_{i}") for i in range(1, 6)
        ]
        
        # 准备测试任务
        test_tasks = [
            (mock_accounts[0], 'like', None),
            (mock_accounts[1], 'retweet', None),
            (mock_accounts[2], 'comment', '测试评论'),
            (mock_accounts[3], 'like', None),
            (mock_accounts[4], 'retweet', None),
        ]
        
        print(f"\n2. 测试并发逻辑（模拟模式）...")
        print(f"  准备执行 {len(test_tasks)} 个并发任务")
        
        # 临时替换执行方法为纯模拟
        original_method = executor._execute_single_interaction
        
        async def mock_interaction(account, post_url, action_type, comment_content=None):
            """纯模拟互动方法"""
            import random
            
            # 模拟执行时间
            expected_time = executor.task_times.get(action_type.upper(), {}).get('expected', 3)
            delay = random.uniform(expected_time * 0.5, expected_time * 1.5)
            await asyncio.sleep(delay)
            
            # 模拟高成功率
            success = random.random() < 0.85
            
            if success:
                print(f"    ✅ 模拟互动成功: {account.username} - {action_type}")
            else:
                print(f"    ❌ 模拟互动失败: {account.username} - {action_type}")
            
            return success
        
        # 替换方法
        executor._execute_single_interaction = mock_interaction
        
        # 测试并发执行
        start_time = time.time()
        
        results = await executor.execute_concurrent_interactions(
            test_tasks, 
            "https://x.com/test/status/*********", 
            max_concurrent=3
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n  ⏱️ 执行耗时: {duration:.2f}秒")
        
        success_count = sum(1 for result in results if result.get('success', False))
        print(f"  📊 执行结果: {success_count}/{len(results)} 成功")
        print(f"  📈 成功率: {success_count/len(results)*100:.1f}%")
        
        # 验证并发性能
        expected_serial_time = len(test_tasks) * 3  # 假设每个任务3秒
        if duration < expected_serial_time * 0.7:
            print("  ✅ 并发执行效率验证通过")
            print(f"    串行预期时间: {expected_serial_time}秒")
            print(f"    并发实际时间: {duration:.2f}秒")
            print(f"    效率提升: {(expected_serial_time - duration) / expected_serial_time * 100:.1f}%")
        else:
            print("  ⚠️ 并发执行效率可能需要优化")
        
        # 恢复原方法
        executor._execute_single_interaction = original_method
        
        return success_count > 0
        
    except Exception as e:
        print(f"  ❌ 并发执行器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_system_stability():
    """测试系统稳定性"""
    print("\n🔧 测试系统稳定性...")
    
    try:
        # 测试多轮并发执行
        print("1. 测试多轮并发执行稳定性...")
        
        from src.modules.interaction.concurrent_executor import ConcurrentInteractionExecutor
        
        success_rounds = 0
        total_rounds = 3
        
        for round_num in range(1, total_rounds + 1):
            print(f"  第 {round_num} 轮测试...")
            
            executor = ConcurrentInteractionExecutor()
            
            # 创建测试任务
            mock_accounts = [MockAccount(i, f"test_user_{i}") for i in range(1, 4)]
            test_tasks = [
                (mock_accounts[0], 'like', None),
                (mock_accounts[1], 'retweet', None),
                (mock_accounts[2], 'comment', '测试评论'),
            ]
            
            # 模拟执行
            async def quick_mock(account, post_url, action_type, comment_content=None):
                await asyncio.sleep(0.5)  # 快速模拟
                return True
            
            executor._execute_single_interaction = quick_mock
            
            try:
                results = await executor.execute_concurrent_interactions(
                    test_tasks, 
                    "https://x.com/test/status/*********", 
                    max_concurrent=3
                )
                
                if all(result.get('success', False) for result in results):
                    success_rounds += 1
                    print(f"    ✅ 第 {round_num} 轮成功")
                else:
                    print(f"    ❌ 第 {round_num} 轮失败")
                    
            except Exception as e:
                print(f"    ❌ 第 {round_num} 轮异常: {e}")
        
        print(f"\n  📊 稳定性测试结果: {success_rounds}/{total_rounds} 轮成功")
        
        if success_rounds == total_rounds:
            print("  ✅ 系统稳定性良好")
            return True
        else:
            print("  ⚠️ 系统稳定性需要改进")
            return False
            
    except Exception as e:
        print(f"  ❌ 稳定性测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 测试当前修复效果")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    test_results = []
    
    # 测试1: Chrome端口管理
    test_results.append(await test_chrome_port_management())
    
    # 测试2: 浏览器池启动控制
    test_results.append(await test_browser_pool_startup_control())
    
    # 测试3: 并发执行器逻辑
    test_results.append(await test_concurrent_executor_logic())
    
    # 测试4: 系统稳定性
    test_results.append(await test_system_stability())
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 修复效果测试总结")
    print("=" * 60)
    
    test_names = [
        "Chrome端口管理",
        "浏览器池启动控制", 
        "并发执行器逻辑",
        "系统稳定性"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {name}: {status}")
    
    success_count = sum(test_results)
    total_count = len(test_results)
    
    print(f"\n📈 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        print("\n🎉 当前修复效果优秀！")
        print("\n✅ 修复验证:")
        print("  - ✅ Chrome端口冲突已解决")
        print("  - ✅ 并发启动控制正常")
        print("  - ✅ 真正并发执行工作正常")
        print("  - ✅ 系统稳定性良好")
        print("\n📋 建议:")
        print("  - 当前系统可以正常使用")
        print("  - 可以进行真实互动测试")
        print("  - 如需长期稳定运行，可考虑修复潜在问题")
    elif success_count >= total_count * 0.75:
        print("\n✅ 当前修复效果良好！")
        print("  - 核心功能正常")
        print("  - 部分测试需要优化")
        print("  - 建议进行真实环境测试")
    else:
        print("\n⚠️ 修复效果需要改进")
        print("  - 建议检查失败的测试项")
        print("  - 可能需要进一步调试")
    
    return success_count >= total_count * 0.75

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        print(f"\n🏁 测试完成，结果: {'成功' if result else '需要改进'}")
        exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断测试")
        exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程异常: {e}")
        import traceback
        traceback.print_exc()
        exit(1)
