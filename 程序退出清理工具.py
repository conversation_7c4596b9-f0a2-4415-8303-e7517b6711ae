#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
程序退出清理工具 - 确保程序完全退出
解决终端依然在运行的问题
"""

import os
import sys
import time
import psutil
import subprocess
import threading
from pathlib import Path

def get_current_project_dir():
    """获取当前项目目录"""
    return os.getcwd()

def find_program_related_processes():
    """查找所有程序相关的进程"""
    current_dir = get_current_project_dir()
    current_pid = os.getpid()
    program_processes = []
    
    print("🔍 扫描程序相关进程...")
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'cwd', 'exe']):
        try:
            proc_info = proc.info
            
            # 跳过当前进程
            if proc_info['pid'] == current_pid:
                continue
            
            is_program_process = False
            process_type = "未知"
            
            # 1. 检查Python进程
            if 'python' in proc_info['name'].lower():
                if proc_info.get('cmdline'):
                    cmdline_str = ' '.join(proc_info['cmdline'])
                    if any(script in cmdline_str for script in [
                        'main.py',
                        current_dir,
                        '测试',
                        '修复',
                        '清理'
                    ]):
                        is_program_process = True
                        process_type = "Python脚本"
            
            # 2. 检查Chrome进程
            elif 'chrome' in proc_info['name'].lower():
                if proc_info.get('cmdline'):
                    cmdline_str = ' '.join(proc_info['cmdline'])
                    if any(indicator in cmdline_str for indicator in [
                        'browser_profiles',
                        'account_',
                        '--remote-debugging-port=9',
                        '--disable-blink-features=AutomationControlled',
                        '--test-type',
                        '--enable-automation'
                    ]):
                        is_program_process = True
                        process_type = "自动化Chrome"
            
            # 3. 检查ChromeDriver进程
            elif 'chromedriver' in proc_info['name'].lower():
                is_program_process = True
                process_type = "ChromeDriver"
            
            # 4. 检查工作目录
            if proc_info.get('cwd') and current_dir in str(proc_info['cwd']):
                is_program_process = True
                if process_type == "未知":
                    process_type = "工作目录匹配"
            
            if is_program_process:
                program_processes.append({
                    'pid': proc_info['pid'],
                    'name': proc_info['name'],
                    'type': process_type,
                    'cmdline': ' '.join(proc_info['cmdline'][:3]) if proc_info['cmdline'] else ''
                })
                
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            continue
    
    return program_processes

def terminate_processes_gracefully(processes):
    """温和地终止进程"""
    print("🛑 温和终止进程...")
    
    terminated_count = 0
    
    for proc_info in processes:
        try:
            proc = psutil.Process(proc_info['pid'])
            proc.terminate()
            print(f"  ✅ 发送终止信号: PID {proc_info['pid']} ({proc_info['name']}) - {proc_info['type']}")
            terminated_count += 1
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            print(f"  ⚠️  无法终止: PID {proc_info['pid']} (可能已结束或权限不足)")
        except Exception as e:
            print(f"  ❌ 终止失败: PID {proc_info['pid']} - {e}")
    
    return terminated_count

def force_kill_remaining_processes(processes):
    """强制杀死残留进程"""
    print("🔨 强制杀死残留进程...")
    
    killed_count = 0
    
    for proc_info in processes:
        try:
            proc = psutil.Process(proc_info['pid'])
            if proc.is_running():
                proc.kill()
                print(f"  ✅ 强制杀死: PID {proc_info['pid']} ({proc_info['name']}) - {proc_info['type']}")
                killed_count += 1
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            print(f"  ⚠️  无法杀死: PID {proc_info['pid']} (可能已结束或权限不足)")
        except Exception as e:
            print(f"  ❌ 杀死失败: PID {proc_info['pid']} - {e}")
    
    return killed_count

def system_level_cleanup():
    """系统级清理"""
    print("🧹 系统级清理...")
    
    try:
        # 强制终止Chrome
        result = subprocess.run(['taskkill', '/f', '/im', 'chrome.exe'], 
                              capture_output=True, check=False)
        if result.returncode == 0:
            print("  ✅ 系统级Chrome清理完成")
        
        # 强制终止ChromeDriver
        result = subprocess.run(['taskkill', '/f', '/im', 'chromedriver.exe'], 
                              capture_output=True, check=False)
        if result.returncode == 0:
            print("  ✅ 系统级ChromeDriver清理完成")
            
    except Exception as e:
        print(f"  ⚠️  系统级清理失败: {e}")

def cleanup_temp_files():
    """清理临时文件"""
    print("🗑️  清理临时文件...")
    
    try:
        # 清理浏览器配置文件
        browser_profiles_dir = Path("browser_profiles")
        if browser_profiles_dir.exists():
            import shutil
            shutil.rmtree(browser_profiles_dir, ignore_errors=True)
            print("  ✅ 清理浏览器配置文件")
        
        # 清理日志文件中的锁定文件
        logs_dir = Path("logs")
        if logs_dir.exists():
            for lock_file in logs_dir.glob("*.lock"):
                try:
                    lock_file.unlink()
                    print(f"  ✅ 清理锁定文件: {lock_file.name}")
                except Exception:
                    pass
                    
    except Exception as e:
        print(f"  ⚠️  清理临时文件失败: {e}")

def main():
    """主清理函数"""
    print("🚀 程序退出清理工具")
    print("=" * 50)
    print("解决终端依然在运行的问题")
    print("=" * 50)
    
    # 第一步：查找程序相关进程
    program_processes = find_program_related_processes()
    
    if program_processes:
        print(f"\n📊 发现 {len(program_processes)} 个程序相关进程:")
        for proc in program_processes:
            print(f"  PID {proc['pid']}: {proc['name']} ({proc['type']})")
        
        # 第二步：温和终止
        terminated_count = terminate_processes_gracefully(program_processes)
        
        # 等待进程终止
        print("\n⏳ 等待进程终止...")
        time.sleep(3)
        
        # 第三步：检查残留进程
        remaining_processes = find_program_related_processes()
        if remaining_processes:
            print(f"\n⚠️  发现 {len(remaining_processes)} 个残留进程")
            killed_count = force_kill_remaining_processes(remaining_processes)
            
            # 再次等待
            time.sleep(2)
        
        # 第四步：系统级清理
        system_level_cleanup()
        
        # 第五步：清理临时文件
        cleanup_temp_files()
        
        # 最终检查
        print("\n🔍 最终检查...")
        final_processes = find_program_related_processes()
        if final_processes:
            print(f"⚠️  仍有 {len(final_processes)} 个进程未清理:")
            for proc in final_processes:
                print(f"  PID {proc['pid']}: {proc['name']} ({proc['type']})")
            print("\n建议:")
            print("  1. 重启计算机")
            print("  2. 检查是否有其他程序占用相关资源")
        else:
            print("✅ 所有程序进程已清理完成")
            
    else:
        print("✅ 没有发现程序相关进程")
        print("程序可能已经正常关闭")
    
    print("\n📋 清理完成建议:")
    print("  1. 关闭所有相关的终端窗口")
    print("  2. 重启IDE或编辑器")
    print("  3. 确保下次启动前环境干净")
    print("  4. 如果问题持续，考虑重启计算机")

if __name__ == "__main__":
    try:
        main()
        print("\n程序将在3秒后自动退出...")
        time.sleep(3)
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断清理")
    except Exception as e:
        print(f"\n❌ 清理异常: {e}")
        import traceback
        traceback.print_exc()
