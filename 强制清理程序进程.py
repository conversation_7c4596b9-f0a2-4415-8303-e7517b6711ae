#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强制清理程序相关的所有进程
"""

import os
import sys
import psutil
import subprocess
import time
from pathlib import Path

def find_program_processes():
    """查找程序相关的所有进程"""
    print("🔍 查找程序相关进程...")
    
    program_processes = []
    current_dir = os.getcwd()
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'cwd', 'exe']):
        try:
            proc_info = proc.info
            
            # 检查是否是程序相关进程
            is_program_process = False
            process_type = "未知"
            
            # 1. 检查工作目录
            if proc_info.get('cwd') and current_dir in str(proc_info['cwd']):
                is_program_process = True
                process_type = "工作目录匹配"
            
            # 2. 检查可执行文件路径
            if proc_info.get('exe') and current_dir in str(proc_info['exe']):
                is_program_process = True
                process_type = "可执行文件路径匹配"
            
            # 3. 检查命令行参数
            if proc_info.get('cmdline'):
                cmdline_str = ' '.join(proc_info['cmdline'])
                if any(keyword in cmdline_str for keyword in [
                    'browser_profiles',
                    'account_',
                    'remote-debugging-port=9',
                    current_dir.replace('\\', '/')
                ]):
                    is_program_process = True
                    process_type = "命令行参数匹配"
            
            # 4. 检查Python进程
            if 'python' in proc_info['name'].lower():
                if proc_info.get('cmdline'):
                    cmdline_str = ' '.join(proc_info['cmdline'])
                    if any(script in cmdline_str for script in [
                        'main.py',
                        '测试',
                        '修复',
                        '清理',
                        current_dir
                    ]):
                        is_program_process = True
                        process_type = "Python脚本"
            
            if is_program_process:
                program_processes.append({
                    'pid': proc_info['pid'],
                    'name': proc_info['name'],
                    'type': process_type,
                    'cmdline': ' '.join(proc_info['cmdline'][:3]) if proc_info['cmdline'] else ''
                })
                
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            continue
    
    return program_processes

def force_kill_program_processes():
    """强制终止程序相关进程"""
    print("🔨 强制终止程序相关进程...")
    
    program_processes = find_program_processes()
    
    if not program_processes:
        print("  ✅ 没有发现程序相关进程")
        return 0
    
    print(f"  发现 {len(program_processes)} 个程序相关进程:")
    for proc in program_processes:
        print(f"    PID {proc['pid']}: {proc['name']} ({proc['type']})")
    
    killed_count = 0
    
    for proc in program_processes:
        try:
            process = psutil.Process(proc['pid'])
            
            # 先尝试温和终止
            process.terminate()
            
            # 等待3秒
            try:
                process.wait(timeout=3)
                print(f"  ✅ 温和终止: PID {proc['pid']} ({proc['name']})")
                killed_count += 1
            except psutil.TimeoutExpired:
                # 强制杀死
                process.kill()
                print(f"  🔨 强制杀死: PID {proc['pid']} ({proc['name']})")
                killed_count += 1
                
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            print(f"  ⚠️  无法终止: PID {proc['pid']} (可能已结束或权限不足)")
        except Exception as e:
            print(f"  ❌ 终止失败: PID {proc['pid']} - {e}")
    
    return killed_count

def clean_chrome_processes():
    """清理Chrome相关进程"""
    print("\n🔨 清理Chrome相关进程...")
    
    current_dir = os.getcwd()
    killed_count = 0
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if 'chrome' in proc.info['name'].lower():
                cmdline = ' '.join(proc.info['cmdline'] or [])
                
                # 检查是否是程序启动的Chrome
                if any(indicator in cmdline for indicator in [
                    'browser_profiles',
                    'account_',
                    'remote-debugging-port=9',
                    current_dir.replace('\\', '/')
                ]):
                    try:
                        process = psutil.Process(proc.info['pid'])
                        process.kill()  # 直接强制杀死
                        print(f"  🔨 强制杀死Chrome: PID {proc.info['pid']}")
                        killed_count += 1
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass
                        
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    return killed_count

def clean_chromedriver_processes():
    """清理ChromeDriver进程"""
    print("\n🔨 清理ChromeDriver进程...")
    
    killed_count = 0
    
    for proc in psutil.process_iter(['pid', 'name']):
        try:
            if 'chromedriver' in proc.info['name'].lower():
                try:
                    process = psutil.Process(proc.info['pid'])
                    process.kill()
                    print(f"  🔨 强制杀死ChromeDriver: PID {proc.info['pid']}")
                    killed_count += 1
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
                    
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    return killed_count

def system_cleanup():
    """系统级清理"""
    print("\n🧹 系统级清理...")
    
    try:
        # Windows系统命令清理
        if os.name == 'nt':
            # 强制终止Chrome
            subprocess.run(['taskkill', '/f', '/im', 'chrome.exe'], 
                         capture_output=True, check=False)
            print("  ✅ 系统级Chrome清理")
            
            # 强制终止ChromeDriver
            subprocess.run(['taskkill', '/f', '/im', 'chromedriver.exe'], 
                         capture_output=True, check=False)
            print("  ✅ 系统级ChromeDriver清理")
            
            # 强制终止Python进程（谨慎）
            current_pid = os.getpid()
            subprocess.run(['taskkill', '/f', '/im', 'python.exe', '/fi', f'PID ne {current_pid}'], 
                         capture_output=True, check=False)
            print("  ✅ 系统级Python清理（保护当前进程）")
            
    except Exception as e:
        print(f"  ⚠️  系统级清理失败: {e}")

def main():
    """主清理函数"""
    print("🚨 强制清理程序进程工具")
    print("=" * 50)
    print("⚠️  警告: 这将强制终止所有程序相关进程")
    print("=" * 50)
    
    # 显示当前进程状态
    program_processes = find_program_processes()
    
    if program_processes:
        print(f"发现 {len(program_processes)} 个程序相关进程:")
        for proc in program_processes:
            print(f"  PID {proc['pid']}: {proc['name']} - {proc['type']}")
        
        print("\n清理选项:")
        print("1. 温和清理 (推荐)")
        print("2. 强制清理")
        print("3. 系统级清理 (最强力)")
        print("4. 退出")
        
        choice = input("\n请选择 (1/2/3/4): ").strip()
        
        if choice == '1':
            print("\n🔧 执行温和清理...")
            killed = force_kill_program_processes()
            print(f"清理了 {killed} 个进程")
            
        elif choice == '2':
            print("\n🔨 执行强制清理...")
            killed1 = force_kill_program_processes()
            killed2 = clean_chrome_processes()
            killed3 = clean_chromedriver_processes()
            total_killed = killed1 + killed2 + killed3
            print(f"强制清理了 {total_killed} 个进程")
            
        elif choice == '3':
            print("\n💥 执行系统级清理...")
            system_cleanup()
            print("系统级清理完成")
            
        else:
            print("退出清理")
            return
        
        # 等待进程完全终止
        print("\n⏳ 等待进程完全终止...")
        time.sleep(5)
        
        # 再次检查
        remaining_processes = find_program_processes()
        if remaining_processes:
            print(f"⚠️  仍有 {len(remaining_processes)} 个进程未清理:")
            for proc in remaining_processes:
                print(f"  PID {proc['pid']}: {proc['name']}")
            print("建议重启计算机")
        else:
            print("✅ 所有程序进程已清理完成")
            
    else:
        print("✅ 没有发现程序相关进程")
        print("程序可能已经正常关闭")
    
    print("\n📋 建议:")
    print("  1. 关闭所有相关的终端窗口")
    print("  2. 重启IDE或编辑器")
    print("  3. 如果问题持续，重启计算机")
    print("  4. 下次启动程序前确保完全清理")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断清理")
    except Exception as e:
        print(f"\n❌ 清理异常: {e}")
        import traceback
        traceback.print_exc()
