#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
X自动发帖程序主入口
"""

import sys
import os
import asyncio
import signal
import atexit
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt6.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QPixmap, QFont

from src.config.settings import get_settings
from src.utils.logger import setup_logger, get_logger
from src.database.connection import DatabaseManager
from src.ui.main_window import MainWindow
from src.core.system_monitor import get_system_monitor
from src.core.task_scheduler import get_task_scheduler


class Application:
    """应用程序主类"""
    
    def __init__(self):
        self.app = None
        self.main_window = None
        self.settings = None
        self.logger = None
        self._cleanup_called = False

        # 🔧 注册退出处理器
        atexit.register(self._emergency_cleanup)

        # 🔧 注册信号处理器
        if sys.platform != "win32":
            signal.signal(signal.SIGTERM, self._signal_handler)
            signal.signal(signal.SIGINT, self._signal_handler)
        else:
            signal.signal(signal.SIGTERM, self._signal_handler)
            signal.signal(signal.SIGINT, self._signal_handler)
        
    def initialize(self):
        """初始化应用程序"""
        try:
            # 加载配置
            self.settings = get_settings()
            
            # 设置日志系统
            setup_logger(self.settings)
            self.logger = get_logger("Application")
            
            self.logger.info("=" * 50)
            self.logger.info(f"启动 {self.settings.app_name} v{self.settings.app_version}")
            self.logger.info("=" * 50)
            
            # 初始化数据库
            self._init_database()
            
            # 创建Qt应用
            self._create_qt_app()
            
            # 显示启动画面
            splash = self._show_splash_screen()
            
            # 初始化核心组件
            self._init_core_components()
            
            # 创建主窗口
            self._create_main_window()
            
            # 关闭启动画面
            if splash:
                splash.finish(self.main_window)
            
            self.logger.info("应用程序初始化完成")
            return True
            
        except Exception as e:
            error_msg = f"应用程序初始化失败: {e}"
            if self.logger:
                self.logger.error(error_msg)
            else:
                print(error_msg)
            
            # 显示错误对话框
            if self.app:
                QMessageBox.critical(None, "初始化错误", error_msg)
            
            return False
    
    def _init_database(self):
        """初始化数据库"""
        try:
            self.logger.info("初始化数据库...")

            # 初始化全局数据库管理器
            from src.database.connection import init_db_manager
            init_db_manager(self.settings.database_url)

            self.logger.info("数据库初始化完成")
            
            # 初始化变量
            from src.utils.variable_initializer import initialize_variables
            initialize_variables(self.logger)
            self.logger.info("变量初始化完成")

        except Exception as e:
            raise Exception(f"数据库初始化失败: {e}")
    
    def _create_qt_app(self):
        """创建Qt应用"""
        try:
            self.app = QApplication(sys.argv)
            
            # 设置应用程序信息
            self.app.setApplicationName(self.settings.app_name)
            self.app.setApplicationVersion(self.settings.app_version)
            self.app.setOrganizationName("X自动发帖程序")
            
            # 设置应用程序字体
            font = QFont("Microsoft YaHei", 9)
            self.app.setFont(font)
            
            # 设置样式
            self._set_app_style()
            
        except Exception as e:
            raise Exception(f"创建Qt应用失败: {e}")
    
    def _set_app_style(self):
        """设置应用程序样式"""
        try:
            style = """
            QMainWindow {
                background-color: #f0f0f0;
            }
            
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            
            QPushButton {
                background-color: #4CAF50;
                border: none;
                color: white;
                padding: 8px 16px;
                text-align: center;
                text-decoration: none;
                font-size: 12px;
                margin: 2px;
                border-radius: 4px;
            }
            
            QPushButton:hover {
                background-color: #45a049;
            }
            
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
            
            QTableWidget {
                gridline-color: #d0d0d0;
                background-color: white;
                alternate-background-color: #f9f9f9;
            }
            
            QTableWidget::item:selected {
                background-color: #3daee9;
                color: white;
            }
            
            QTabWidget::pane {
                border: 1px solid #cccccc;
                background-color: white;
            }
            
            QTabBar::tab {
                background-color: #e0e0e0;
                padding: 8px 16px;
                margin-right: 2px;
            }
            
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #3daee9;
            }
            """
            
            self.app.setStyleSheet(style)
            
        except Exception as e:
            self.logger.warning(f"设置应用程序样式失败: {e}")
    
    def _show_splash_screen(self):
        """显示启动画面"""
        try:
            # 创建启动画面
            splash_pixmap = QPixmap(400, 300)
            splash_pixmap.fill(Qt.GlobalColor.white)
            
            splash = QSplashScreen(splash_pixmap)
            splash.setWindowFlags(Qt.WindowType.WindowStaysOnTopHint | Qt.WindowType.FramelessWindowHint)
            
            # 显示启动信息
            splash.showMessage(
                f"正在启动 {self.settings.app_name} v{self.settings.app_version}...",
                Qt.AlignmentFlag.AlignBottom | Qt.AlignmentFlag.AlignCenter,
                Qt.GlobalColor.black
            )
            
            splash.show()
            self.app.processEvents()
            
            return splash
            
        except Exception as e:
            self.logger.warning(f"显示启动画面失败: {e}")
            return None
    
    def _init_core_components(self):
        """初始化核心组件"""
        try:
            self.logger.info("初始化核心组件...")
            
            # 启动系统监控
            system_monitor = get_system_monitor()
            system_monitor.start_monitoring()
            
            # 启动任务调度器
            task_scheduler = get_task_scheduler()
            task_scheduler.start()
            
            self.logger.info("核心组件初始化完成")
            
        except Exception as e:
            raise Exception(f"核心组件初始化失败: {e}")
    
    def _create_main_window(self):
        """创建主窗口"""
        try:
            self.logger.info("创建主窗口...")
            print("DEBUG: 开始创建主窗口")
            
            self.main_window = MainWindow(self.settings)
            print("DEBUG: MainWindow实例创建完成")
            
            self.main_window.show()
            print("DEBUG: 主窗口显示完成")
            
            self.logger.info("主窗口创建完成")
            
        except Exception as e:
            print(f"DEBUG: 创建主窗口时发生错误: {e}")
            import traceback
            traceback.print_exc()
            raise Exception(f"创建主窗口失败: {e}")
    
    def run(self):
        """运行应用程序"""
        try:
            if not self.initialize():
                return 1
            
            # 运行Qt事件循环
            return self.app.exec()
            
        except KeyboardInterrupt:
            self.logger.info("用户中断程序")
            return 0
        except Exception as e:
            error_msg = f"程序运行异常: {e}"
            if self.logger:
                self.logger.error(error_msg)
            else:
                print(error_msg)
            return 1
        finally:
            self.cleanup()
    
    def cleanup(self):
        """清理资源"""
        try:
            self.logger.info("正在清理资源...")
            
            # 停止系统监控
            try:
                system_monitor = get_system_monitor()
                system_monitor.stop_monitoring()
                self.logger.debug("系统监控已停止")
            except Exception as e:
                self.logger.warning(f"停止系统监控失败: {e}")
            
            # 停止任务调度器
            try:
                task_scheduler = get_task_scheduler()
                task_scheduler.stop()
                self.logger.debug("任务调度器已停止")
            except Exception as e:
                self.logger.warning(f"停止任务调度器失败: {e}")

            # 🔧 停止浏览器稳定性监控器
            try:
                from src.core.browser_stability_monitor import stop_browser_monitoring
                stop_browser_monitoring()
                self.logger.debug("浏览器稳定性监控已停止")
            except Exception as e:
                self.logger.warning(f"停止浏览器稳定性监控失败: {e}")

            # 关闭浏览器池
            try:
                from src.core.browser_manager import get_browser_pool
                browser_pool = get_browser_pool()
                self._cleanup_browser_pool_safely(browser_pool)
                self.logger.debug("浏览器池清理完成")
            except Exception as e:
                self.logger.warning(f"清理浏览器池失败: {e}")

            self.logger.info("资源清理完成")
            self._cleanup_called = True
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"清理资源失败: {e}")
            else:
                print(f"清理资源失败: {e}")
    
    def _cleanup_browser_pool_safely(self, browser_pool):
        """安全地清理浏览器池"""
        import threading
        
        def cleanup_task():
            """在新线程中执行清理任务"""
            try:
                # 创建新的事件循环
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                # 设置超时时间
                async def cleanup_with_timeout():
                    try:
                        await asyncio.wait_for(browser_pool.close_all(), timeout=10.0)
                    except asyncio.TimeoutError:
                        if self.logger:
                            self.logger.warning("浏览器池清理超时，强制关闭")
                    except Exception as e:
                        if self.logger:
                            self.logger.warning(f"浏览器池清理异常: {e}")
                
                # 运行清理任务
                loop.run_until_complete(cleanup_with_timeout())
                
            except Exception as e:
                if self.logger:
                    self.logger.warning(f"清理线程异常: {e}")
            finally:
                try:
                    # 确保事件循环正确关闭
                    if not loop.is_closed():
                        loop.close()
                except Exception:
                    pass
        
        # 在新线程中执行清理
        cleanup_thread = threading.Thread(target=cleanup_task, daemon=True)
        cleanup_thread.start()
        
        # 等待清理完成，但不超过5秒
        cleanup_thread.join(timeout=5.0)

        if cleanup_thread.is_alive() and self.logger:
            self.logger.warning("浏览器池清理线程未在预期时间内完成")
            # 🔧 强制终止清理线程相关的进程
            try:
                import psutil
                current_pid = os.getpid()
                for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                    try:
                        if 'chrome' in proc.info['name'].lower():
                            cmdline = ' '.join(proc.info['cmdline'] or [])
                            if any(indicator in cmdline for indicator in [
                                '--remote-debugging-port',
                                'browser_profiles',
                                '--disable-blink-features=AutomationControlled'
                            ]):
                                proc.terminate()
                                self.logger.debug(f"强制终止Chrome进程: PID {proc.info['pid']}")
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue
            except Exception as e:
                self.logger.warning(f"强制清理Chrome进程失败: {e}")

    def _signal_handler(self, signum, frame):
        """信号处理器"""
        try:
            if self.logger:
                self.logger.info(f"接收到信号 {signum}，开始清理...")
            else:
                print(f"接收到信号 {signum}，开始清理...")

            self.cleanup()
            sys.exit(0)
        except Exception as e:
            print(f"信号处理异常: {e}")
            self._emergency_cleanup()
            sys.exit(1)

    def _emergency_cleanup(self):
        """紧急清理 - 在程序异常退出时调用"""
        if self._cleanup_called:
            return

        try:
            print("🚨 执行紧急清理...")

            # 强制清理Chrome进程
            import psutil
            cleaned_count = 0
            current_dir = os.getcwd()

            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if 'chrome' in proc.info['name'].lower():
                        cmdline = ' '.join(proc.info['cmdline'] or [])
                        if any(indicator in cmdline for indicator in [
                            'browser_profiles',
                            '--remote-debugging-port=9',
                            '--disable-blink-features=AutomationControlled'
                        ]):
                            proc.terminate()
                            cleaned_count += 1
                            print(f"  ✅ 紧急终止Chrome: PID {proc.info['pid']}")
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            if cleaned_count > 0:
                print(f"🧹 紧急清理了 {cleaned_count} 个Chrome进程")

            self._cleanup_called = True

        except Exception as e:
            print(f"紧急清理失败: {e}")


def main():
    """主函数"""
    # 设置异常处理
    def handle_exception(exc_type, exc_value, exc_traceback):
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        import traceback
        logger = get_logger("Exception")
        tb_str = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        logger.error(f"未捕获的异常: {exc_type.__name__}: {exc_value}\n堆栈跟踪:\n{tb_str}")
    
    sys.excepthook = handle_exception
    
    # 创建并运行应用程序
    app = Application()
    return app.run()


if __name__ == "__main__":
    sys.exit(main())
