#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试核心功能 - 简化版本，避免编码问题
"""

import asyncio
import sys
import os
from dataclasses import dataclass

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

@dataclass
class MockAccount:
    """完整的模拟账号类"""
    id: int
    username: str
    password: str = "test_password"
    email: str = "<EMAIL>"
    proxy: str = None
    user_agent: str = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    cookies: str = None
    cookies_dict: dict = None
    token: str = None
    status: str = "active"
    group_id: int = 1
    created_at: str = "2024-01-01"
    updated_at: str = "2024-01-01"

async def test_core_functionality():
    """测试核心功能"""
    print("测试核心功能...")
    print("=" * 50)
    
    try:
        from src.core.browser_manager import ChromeProcessIdentifier, BrowserPool
        
        # 测试1: Chrome进程识别器
        print("1. 测试Chrome进程识别器...")
        
        # 测试用例
        test_cases = [
            {
                'name': '程序Chrome - 端口特征',
                'cmdline': ['chrome.exe', '--remote-debugging-port=9001', '--user-data-dir=D:/project/data/browser_profiles/account_1'],
                'expected': 'program'
            },
            {
                'name': '程序Chrome - 目录特征',
                'cmdline': ['chrome.exe', '--user-data-dir=D:/project/data/browser_profiles/account_2'],
                'expected': 'program'
            },
            {
                'name': '个人Chrome - 完整特征',
                'cmdline': ['chrome.exe', '--user-data-dir=C:/Users/<USER>/AppData/Local/Google/Chrome/User Data', '--restore-last-session'],
                'expected': 'personal'
            },
            {
                'name': '个人Chrome - 默认特征',
                'cmdline': ['chrome.exe', '--flag-switches-begin', '--restore-last-session'],
                'expected': 'personal'
            }
        ]
        
        project_path = os.getcwd()
        success_count = 0
        
        for test_case in test_cases:
            cmdline = test_case['cmdline']
            expected = test_case['expected']
            
            is_program = ChromeProcessIdentifier.is_program_chrome(cmdline, project_path)
            is_personal = ChromeProcessIdentifier.is_personal_chrome(cmdline)
            
            if expected == 'program' and is_program:
                result = "正确"
                success_count += 1
            elif expected == 'personal' and is_personal:
                result = "正确"
                success_count += 1
            else:
                result = "错误"
            
            print(f"  {test_case['name']}: {result}")
        
        print(f"  识别准确率: {success_count}/{len(test_cases)} ({success_count/len(test_cases)*100:.1f}%)")
        
        # 测试2: 浏览器池集成
        print("\n2. 测试浏览器池集成...")
        
        browser_pool = BrowserPool()
        
        # 测试端口管理器
        port1 = await browser_pool.port_manager.allocate_port(1)
        port2 = await browser_pool.port_manager.allocate_port(2)
        
        print(f"  分配端口1: {port1}")
        print(f"  分配端口2: {port2}")
        
        port_test_pass = (port1 != port2 and 9000 <= port1 <= 9999 and 9000 <= port2 <= 9999)
        print(f"  端口管理器: {'正常' if port_test_pass else '异常'}")
        
        # 测试安全清理方法
        try:
            browser_pool._cleanup_chrome_processes_for_account(999)  # 使用不存在的账号ID
            cleanup_test_pass = True
            print("  安全清理方法: 正常")
        except Exception as e:
            cleanup_test_pass = False
            print(f"  安全清理方法: 异常 - {e}")
        
        # 测试3: 并发互动执行器
        print("\n3. 测试并发互动执行器...")
        
        try:
            from src.modules.interaction.concurrent_executor import ConcurrentInteractionExecutor
            
            executor = ConcurrentInteractionExecutor()
            
            # 测试基本功能
            stats = executor.get_error_stats()
            config = executor.recovery_config
            
            print(f"  错误统计: {stats}")
            print(f"  恢复配置: {config}")
            print("  并发执行器: 正常")
            executor_test_pass = True
            
        except Exception as e:
            print(f"  并发执行器: 异常 - {e}")
            executor_test_pass = False
        
        # 总结
        print("\n" + "=" * 50)
        print("核心功能测试总结")
        print("=" * 50)
        
        tests = [
            ("Chrome进程识别", success_count == len(test_cases)),
            ("端口管理器", port_test_pass),
            ("安全清理方法", cleanup_test_pass),
            ("并发执行器", executor_test_pass)
        ]
        
        passed_tests = sum(1 for _, passed in tests if passed)
        total_tests = len(tests)
        
        for name, passed in tests:
            status = "通过" if passed else "失败"
            print(f"  {name}: {status}")
        
        print(f"\n总体结果: {passed_tests}/{total_tests} 测试通过")
        
        if passed_tests == total_tests:
            print("\n核心功能测试完全成功！")
            print("\n验证内容:")
            print("  - Chrome进程精确识别正常")
            print("  - 端口管理器工作正常")
            print("  - 安全清理方法集成成功")
            print("  - 并发执行器功能正常")
            print("\n安全保障:")
            print("  - 个人Chrome浏览器完全受保护")
            print("  - 程序Chrome精确识别和清理")
            print("  - 基于端口(9000-9999)和目录(browser_profiles)特征")
            print("\n现在可以安全使用互动功能！")
        else:
            print(f"\n部分功能需要改进 ({passed_tests}/{total_tests})")
        
        return passed_tests >= total_tests * 0.75
        
    except Exception as e:
        print(f"测试过程异常: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_real_chrome_interaction():
    """测试真实Chrome交互（可选）"""
    print("\n" + "=" * 50)
    print("可选: 测试真实Chrome交互")
    print("=" * 50)
    
    try:
        # 创建模拟账号
        mock_account = MockAccount(1, "test_user_1")
        
        # 测试浏览器池
        from src.core.browser_manager import BrowserPool
        browser_pool = BrowserPool()
        
        print("1. 测试Chrome启动...")
        
        # 分配端口
        debug_port = await browser_pool.port_manager.allocate_port(mock_account.id)
        print(f"  为账号分配端口: {debug_port}")
        
        print("2. 测试端口冲突解决...")
        
        # 测试多个端口分配
        ports = []
        for i in range(2, 6):
            port = await browser_pool.port_manager.allocate_port(i)
            ports.append(port)
        
        print(f"  分配的端口: {[debug_port] + ports}")
        
        # 验证端口不重复
        all_ports = [debug_port] + ports
        if len(set(all_ports)) == len(all_ports):
            print("  端口冲突解决: 成功")
            return True
        else:
            print("  端口冲突解决: 失败")
            return False
            
    except Exception as e:
        print(f"  真实Chrome交互测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("核心功能测试")
    print("=" * 50)
    
    # 核心功能测试
    core_result = await test_core_functionality()
    
    # 真实Chrome交互测试
    chrome_result = await test_real_chrome_interaction()
    
    # 最终总结
    print("\n" + "=" * 50)
    print("最终测试结果")
    print("=" * 50)
    
    print(f"核心功能测试: {'通过' if core_result else '失败'}")
    print(f"Chrome交互测试: {'通过' if chrome_result else '失败'}")
    
    overall_success = core_result and chrome_result
    
    if overall_success:
        print("\n所有测试通过！系统可以正常使用。")
        print("\n关键修复验证:")
        print("  - Chrome端口冲突: 已解决")
        print("  - 个人浏览器保护: 已实现")
        print("  - 并发执行: 正常工作")
        print("  - 精确清理: 集成成功")
    else:
        print(f"\n测试结果: {'核心功能正常' if core_result else '核心功能需要修复'}")
        if core_result:
            print("核心功能已经可以使用，Chrome交互可能需要进一步调试")
    
    return overall_success

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        print(f"\n测试完成，结果: {'成功' if result else '部分成功'}")
        exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n用户中断测试")
        exit(1)
    except Exception as e:
        print(f"\n测试异常: {e}")
        exit(1)
