#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
紧急Chrome启动问题诊断
"""

import os
import sys
import socket
import psutil
import subprocess
from pathlib import Path

def check_port_9000():
    """检查端口9000是否被占用"""
    print("1. 检查端口9000状态...")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(2)
        result = sock.connect_ex(('127.0.0.1', 9000))
        sock.close()
        
        if result == 0:
            print("  ❌ 端口9000被占用！")
            
            # 查找占用端口的进程
            for proc in psutil.process_iter(['pid', 'name', 'connections']):
                try:
                    for conn in proc.info['connections'] or []:
                        if hasattr(conn, 'laddr') and conn.laddr.port == 9000:
                            print(f"  占用进程: PID {proc.info['pid']}, 名称: {proc.info['name']}")
                            return False
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            return False
        else:
            print("  ✅ 端口9000可用")
            return True
            
    except Exception as e:
        print(f"  ❌ 检查端口9000失败: {e}")
        return False

def check_chrome_processes():
    """检查Chrome进程"""
    print("\n2. 检查Chrome进程...")
    
    chrome_processes = []
    for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'status']):
        try:
            if 'chrome' in proc.info['name'].lower():
                chrome_processes.append({
                    'pid': proc.info['pid'],
                    'name': proc.info['name'],
                    'status': proc.info['status'],
                    'cmdline': ' '.join(proc.info['cmdline'][:3]) if proc.info['cmdline'] else ''
                })
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    if chrome_processes:
        print(f"  发现 {len(chrome_processes)} 个Chrome进程:")
        for proc in chrome_processes:
            print(f"    PID {proc['pid']}: {proc['name']} ({proc['status']})")
            if 'browser_profiles' in proc['cmdline']:
                print(f"      ⚠️  这是程序Chrome进程")
        return len(chrome_processes)
    else:
        print("  ✅ 没有Chrome进程")
        return 0

def check_user_data_directory():
    """检查用户数据目录"""
    print("\n3. 检查用户数据目录...")
    
    user_data_dir = Path("data/browser_profiles/account_10")
    
    print(f"  目录路径: {user_data_dir.absolute()}")
    print(f"  路径长度: {len(str(user_data_dir.absolute()))} 字符")
    
    if len(str(user_data_dir.absolute())) > 260:
        print("  ⚠️  路径可能过长 (Windows限制260字符)")
    
    if user_data_dir.exists():
        print("  ✅ 目录存在")
        
        # 检查关键文件
        key_files = ['Preferences', 'Local State']
        for file_name in key_files:
            file_path = user_data_dir / file_name
            if file_path.exists():
                print(f"    ✅ {file_name} 存在")
            else:
                print(f"    ⚠️  {file_name} 不存在")
        
        # 检查锁文件
        lock_files = list(user_data_dir.rglob("*LOCK*")) + list(user_data_dir.rglob("*.lock"))
        if lock_files:
            print(f"  ⚠️  发现 {len(lock_files)} 个锁文件:")
            for lock_file in lock_files[:3]:
                print(f"    {lock_file.name}")
            return False
        else:
            print("  ✅ 没有锁文件")
            return True
    else:
        print("  ❌ 目录不存在")
        return False

def test_simple_chrome_startup():
    """测试简单Chrome启动"""
    print("\n4. 测试简单Chrome启动...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        
        options = Options()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--remote-debugging-port=9001')  # 使用不同端口
        
        print("  🚀 尝试启动简单Chrome...")
        
        service = Service()
        driver = webdriver.Chrome(service=service, options=options)
        
        print("  ✅ 简单Chrome启动成功")
        
        driver.get("data:text/html,<html><body><h1>Test</h1></body></html>")
        title = driver.title
        print(f"  📄 页面标题: {title}")
        
        driver.quit()
        print("  ✅ Chrome关闭成功")
        return True
        
    except Exception as e:
        print(f"  ❌ 简单Chrome启动失败: {e}")
        return False

def emergency_cleanup():
    """紧急清理"""
    print("\n5. 紧急清理...")
    
    cleaned_items = 0
    
    # 清理Chrome进程
    try:
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            if 'chrome' in proc.info['name'].lower():
                cmdline = ' '.join(proc.info['cmdline'] or [])
                if 'browser_profiles' in cmdline or 'remote-debugging-port=9000' in cmdline:
                    try:
                        proc.terminate()
                        print(f"  ✅ 终止程序Chrome进程: PID {proc.info['pid']}")
                        cleaned_items += 1
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass
    except Exception as e:
        print(f"  ⚠️  清理Chrome进程失败: {e}")
    
    # 清理锁文件
    try:
        user_data_dir = Path("data/browser_profiles/account_10")
        if user_data_dir.exists():
            lock_files = list(user_data_dir.rglob("*LOCK*")) + list(user_data_dir.rglob("*.lock"))
            for lock_file in lock_files:
                try:
                    lock_file.unlink()
                    print(f"  ✅ 删除锁文件: {lock_file.name}")
                    cleaned_items += 1
                except Exception:
                    pass
    except Exception as e:
        print(f"  ⚠️  清理锁文件失败: {e}")
    
    print(f"  📊 清理了 {cleaned_items} 个项目")
    return cleaned_items > 0

def main():
    """主诊断函数"""
    print("🚨 紧急Chrome启动问题诊断")
    print("=" * 50)
    
    # 诊断步骤
    port_ok = check_port_9000()
    chrome_count = check_chrome_processes()
    dir_ok = check_user_data_directory()
    simple_chrome_ok = test_simple_chrome_startup()
    
    # 问题总结
    print("\n" + "=" * 50)
    print("📊 诊断结果")
    print("=" * 50)
    
    issues = []
    if not port_ok:
        issues.append("端口9000被占用")
    if chrome_count > 0:
        issues.append(f"发现{chrome_count}个Chrome进程残留")
    if not dir_ok:
        issues.append("用户数据目录有问题")
    if not simple_chrome_ok:
        issues.append("Chrome基础启动失败")
    
    if issues:
        print("发现问题:")
        for issue in issues:
            print(f"  ❌ {issue}")
        
        print(f"\n🔧 建议修复:")
        choice = input("是否执行紧急清理？(y/n): ").lower().strip()
        if choice == 'y':
            cleanup_result = emergency_cleanup()
            if cleanup_result:
                print("\n✅ 紧急清理完成")
                print("建议:")
                print("  1. 等待5秒让系统稳定")
                print("  2. 重新尝试启动Chrome")
                print("  3. 如果还有问题，重启程序")
            else:
                print("\n⚠️  紧急清理效果有限")
                print("建议重启程序或计算机")
        else:
            print("跳过清理")
    else:
        print("✅ 所有检查都通过")
        print("Chrome启动环境正常")
        print("问题可能在于:")
        print("  - Chrome启动参数")
        print("  - 系统资源不足")
        print("  - 防火墙阻止")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n用户中断诊断")
    except Exception as e:
        print(f"\n异常: {e}")
        import traceback
        traceback.print_exc()
