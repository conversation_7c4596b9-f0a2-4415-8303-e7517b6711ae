#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试程序退出功能
验证终端是否能正确返回命令行模式
"""

import os
import sys
import time
import subprocess
import threading
from pathlib import Path

def test_program_exit():
    """测试程序退出功能"""
    print("🧪 测试程序退出功能")
    print("=" * 50)
    
    # 启动主程序
    print("1. 启动主程序...")
    try:
        # 使用subprocess启动程序，这样可以控制它
        process = subprocess.Popen(
            [sys.executable, "main.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=os.getcwd()
        )
        
        print(f"   ✅ 程序已启动，PID: {process.pid}")
        
        # 等待程序启动
        print("2. 等待程序完全启动...")
        time.sleep(10)
        
        # 检查程序是否还在运行
        if process.poll() is None:
            print("   ✅ 程序正在运行")
        else:
            print("   ❌ 程序启动失败或已退出")
            stdout, stderr = process.communicate()
            print(f"   输出: {stdout}")
            print(f"   错误: {stderr}")
            return False
        
        # 发送中断信号
        print("3. 发送中断信号...")
        process.terminate()
        
        # 等待程序退出
        print("4. 等待程序退出...")
        try:
            stdout, stderr = process.communicate(timeout=15)
            exit_code = process.returncode
            print(f"   ✅ 程序已退出，退出码: {exit_code}")
            
            # 显示输出的最后几行
            if stdout:
                lines = stdout.strip().split('\n')
                print("   📋 程序输出的最后几行:")
                for line in lines[-5:]:
                    if line.strip():
                        print(f"      {line}")
            
            return True
            
        except subprocess.TimeoutExpired:
            print("   ⚠️  程序退出超时，强制终止...")
            process.kill()
            stdout, stderr = process.communicate()
            print("   ❌ 程序未能在预期时间内退出")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def check_remaining_processes():
    """检查残留进程"""
    print("\n5. 检查残留进程...")
    
    try:
        import psutil
        current_dir = os.getcwd()
        found_processes = []
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                # 检查Python进程
                if 'python' in proc.info['name'].lower():
                    if proc.info.get('cmdline'):
                        cmdline_str = ' '.join(proc.info['cmdline'])
                        if 'main.py' in cmdline_str or current_dir in cmdline_str:
                            found_processes.append({
                                'pid': proc.info['pid'],
                                'name': proc.info['name'],
                                'type': 'Python程序'
                            })
                
                # 检查Chrome进程
                elif 'chrome' in proc.info['name'].lower():
                    if proc.info.get('cmdline'):
                        cmdline_str = ' '.join(proc.info['cmdline'])
                        if any(indicator in cmdline_str for indicator in [
                            'browser_profiles',
                            '--remote-debugging-port=9',
                            '--disable-blink-features=AutomationControlled'
                        ]):
                            found_processes.append({
                                'pid': proc.info['pid'],
                                'name': proc.info['name'],
                                'type': '自动化Chrome'
                            })
                            
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        if found_processes:
            print(f"   ⚠️  发现 {len(found_processes)} 个残留进程:")
            for proc in found_processes:
                print(f"      PID {proc['pid']}: {proc['name']} ({proc['type']})")
            return False
        else:
            print("   ✅ 没有发现残留进程")
            return True
            
    except Exception as e:
        print(f"   ❌ 检查进程失败: {e}")
        return False

def test_terminal_responsiveness():
    """测试终端响应性"""
    print("\n6. 测试终端响应性...")
    
    try:
        # 尝试执行一个简单的命令
        result = subprocess.run(
            ["echo", "Terminal test"],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if result.returncode == 0:
            print("   ✅ 终端响应正常")
            return True
        else:
            print("   ❌ 终端响应异常")
            return False
            
    except subprocess.TimeoutExpired:
        print("   ❌ 终端响应超时")
        return False
    except Exception as e:
        print(f"   ❌ 终端测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 程序退出测试工具")
    print("=" * 50)
    print("测试程序是否能正确退出并返回命令行模式")
    print("=" * 50)
    
    results = []
    
    # 测试1: 程序退出功能
    results.append(test_program_exit())
    
    # 等待一下
    time.sleep(2)
    
    # 测试2: 检查残留进程
    results.append(check_remaining_processes())
    
    # 测试3: 测试终端响应性
    results.append(test_terminal_responsiveness())
    
    # 总结结果
    print("\n" + "=" * 50)
    print("📊 测试结果总结")
    print("=" * 50)
    
    success_count = sum(results)
    total_count = len(results)
    
    print(f"测试通过: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("\n🎉 所有测试通过！")
        print("✅ 程序能够正确退出")
        print("✅ 没有残留进程")
        print("✅ 终端响应正常")
        print("\n你的终端退出问题已经解决！")
    else:
        print("\n⚠️  部分测试失败")
        if not results[0]:
            print("❌ 程序退出异常")
        if not results[1]:
            print("❌ 存在残留进程")
        if not results[2]:
            print("❌ 终端响应异常")
        
        print("\n建议:")
        print("  1. 运行清理工具: python 程序退出清理工具.py")
        print("  2. 重启终端")
        print("  3. 检查系统资源")
    
    return success_count == total_count

if __name__ == "__main__":
    try:
        success = main()
        print(f"\n程序将在3秒后退出...")
        time.sleep(3)
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
