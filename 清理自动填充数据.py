#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理Chrome自动填充数据问题
"""

import os
import shutil
from pathlib import Path

def clean_autofill_data(account_id=None):
    """清理Chrome自动填充数据"""
    print("清理Chrome自动填充数据...")
    
    browser_profiles_dir = Path("data/browser_profiles")
    
    if not browser_profiles_dir.exists():
        print("浏览器配置文件目录不存在")
        return
    
    cleaned_count = 0
    
    # 如果指定了账号ID，只清理该账号
    if account_id:
        account_dirs = [browser_profiles_dir / f"account_{account_id}"]
    else:
        # 清理所有账号
        account_dirs = [d for d in browser_profiles_dir.iterdir() if d.is_dir() and d.name.startswith("account_")]
    
    for account_dir in account_dirs:
        if not account_dir.exists():
            continue
            
        print(f"清理账号: {account_dir.name}")
        
        # 清理自动填充相关目录
        autofill_dirs = [
            "AutofillStates",
            "AutofillRegex",
            "AutofillImages", 
            "Autofill Assistant",
            "Web Data",
            "Web Data-journal"
        ]
        
        for dir_name in autofill_dirs:
            target_path = account_dir / dir_name
            if target_path.exists():
                try:
                    if target_path.is_dir():
                        shutil.rmtree(target_path)
                        print(f"  删除目录: {dir_name}")
                    else:
                        target_path.unlink()
                        print(f"  删除文件: {dir_name}")
                    cleaned_count += 1
                except Exception as e:
                    print(f"  删除失败 {dir_name}: {e}")
        
        # 清理可能损坏的缓存文件
        cache_patterns = [
            "*.tmp",
            "*.lock",
            "*.log",
            "*LOCK*"
        ]
        
        for pattern in cache_patterns:
            for file_path in account_dir.rglob(pattern):
                try:
                    file_path.unlink()
                    print(f"  删除缓存文件: {file_path.name}")
                    cleaned_count += 1
                except Exception as e:
                    print(f"  删除缓存文件失败 {file_path.name}: {e}")
    
    print(f"清理完成，共处理 {cleaned_count} 个文件/目录")

def fix_specific_ad_file():
    """修复特定的AD文件问题"""
    print("修复特定AD文件问题...")
    
    problem_file = Path("data/browser_profiles/account_10/AutofillStates/2025.6.13.84507/AD")
    
    if problem_file.exists():
        print(f"发现问题文件: {problem_file}")
        
        try:
            # 删除整个AutofillStates目录
            autofill_dir = problem_file.parent.parent
            if autofill_dir.exists():
                shutil.rmtree(autofill_dir)
                print(f"删除自动填充目录: {autofill_dir}")
                print("Chrome会在下次启动时重新创建")
                return True
        except Exception as e:
            print(f"删除失败: {e}")
            
            # 备用方案：只删除问题文件
            try:
                problem_file.unlink()
                print(f"删除问题文件: {problem_file}")
                return True
            except Exception as e2:
                print(f"删除问题文件也失败: {e2}")
                return False
    else:
        print("问题文件不存在，可能已经被清理")
        return True

def check_chrome_profile_health():
    """检查Chrome配置文件健康状态"""
    print("检查Chrome配置文件健康状态...")
    
    browser_profiles_dir = Path("data/browser_profiles")
    
    if not browser_profiles_dir.exists():
        print("浏览器配置文件目录不存在")
        return
    
    for account_dir in browser_profiles_dir.iterdir():
        if not account_dir.is_dir() or not account_dir.name.startswith("account_"):
            continue
            
        print(f"\n检查账号: {account_dir.name}")
        
        # 检查关键文件
        key_files = [
            "Preferences",
            "Local State", 
            "Cookies",
            "History"
        ]
        
        for file_name in key_files:
            file_path = account_dir / file_name
            if file_path.exists():
                try:
                    size = file_path.stat().st_size
                    print(f"  {file_name}: {size} bytes - 正常")
                except Exception as e:
                    print(f"  {file_name}: 访问失败 - {e}")
            else:
                print(f"  {file_name}: 不存在")
        
        # 检查可疑文件
        suspicious_patterns = [
            "*.tmp",
            "*.lock", 
            "*LOCK*",
            "*.corrupt"
        ]
        
        suspicious_files = []
        for pattern in suspicious_patterns:
            suspicious_files.extend(account_dir.rglob(pattern))
        
        if suspicious_files:
            print(f"  发现 {len(suspicious_files)} 个可疑文件:")
            for file_path in suspicious_files[:5]:  # 只显示前5个
                print(f"    {file_path.relative_to(account_dir)}")
            if len(suspicious_files) > 5:
                print(f"    ... 还有 {len(suspicious_files) - 5} 个")

def main():
    """主函数"""
    print("Chrome自动填充数据清理工具")
    print("=" * 40)
    
    # 1. 修复特定AD文件问题
    print("1. 修复特定AD文件问题")
    fix_result = fix_specific_ad_file()
    
    # 2. 检查配置文件健康状态
    print("\n2. 检查配置文件健康状态")
    check_chrome_profile_health()
    
    # 3. 询问是否清理所有自动填充数据
    print("\n3. 清理选项")
    if fix_result:
        print("特定问题已修复")
    else:
        print("特定问题修复失败，建议清理自动填充数据")
    
    choice = input("\n是否清理所有账号的自动填充数据？(y/n): ").lower().strip()
    
    if choice == 'y':
        clean_autofill_data()
        print("\n清理完成！")
        print("建议:")
        print("  1. 重启程序")
        print("  2. Chrome会重新创建必要的文件")
        print("  3. 自动填充功能会重置")
    else:
        print("跳过清理")
    
    print("\n修复建议:")
    print("  - AD文件问题通常不影响核心功能")
    print("  - Chrome会自动重建损坏的自动填充文件")
    print("  - 如果问题持续，可以删除整个账号配置文件目录")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"\n异常: {e}")
        import traceback
        traceback.print_exc()
