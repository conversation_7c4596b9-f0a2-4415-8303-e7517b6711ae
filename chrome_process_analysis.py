#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Chrome进程分析工具 - 区分个人浏览器和程序浏览器
"""

import psutil
import os
from datetime import datetime

def analyze_chrome_processes():
    """分析所有Chrome进程"""
    print("🔍 分析Chrome进程...")
    print("=" * 80)
    
    chrome_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time', 'cwd']):
        try:
            if 'chrome' in proc.info['name'].lower():
                chrome_processes.append({
                    'pid': proc.info['pid'],
                    'name': proc.info['name'],
                    'cmdline': proc.info['cmdline'] or [],
                    'create_time': proc.info['create_time'],
                    'cwd': proc.info.get('cwd', 'N/A')
                })
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    if not chrome_processes:
        print("❌ 没有发现Chrome进程")
        return {
            'personal': [],
            'program': [],
            'unknown': []
        }
    
    print(f"📊 发现 {len(chrome_processes)} 个Chrome进程")
    print()
    
    # 分类Chrome进程
    personal_chrome = []
    program_chrome = []
    unknown_chrome = []
    
    for proc in chrome_processes:
        cmdline_str = ' '.join(proc['cmdline'])
        
        # 判断是否是程序启动的Chrome
        program_indicators = [
            '--remote-debugging-port',
            '--user-data-dir',
            'browser_profiles',
            'account_',
            '--headless',
            '--disable-blink-features',
            '--no-sandbox'
        ]
        
        # 判断是否是个人Chrome
        personal_indicators = [
            'User Data\\Default',
            'AppData\\Local\\Google\\Chrome',
            '--restore-last-session',
            '--flag-switches-begin'
        ]
        
        is_program_chrome = any(indicator in cmdline_str for indicator in program_indicators)
        is_personal_chrome = any(indicator in cmdline_str for indicator in personal_indicators)
        
        if is_program_chrome:
            program_chrome.append(proc)
        elif is_personal_chrome:
            personal_chrome.append(proc)
        else:
            unknown_chrome.append(proc)
    
    # 显示分析结果
    print("👤 个人Chrome进程:")
    if personal_chrome:
        for proc in personal_chrome:
            create_time = datetime.fromtimestamp(proc['create_time']).strftime('%H:%M:%S')
            print(f"  PID: {proc['pid']}, 启动时间: {create_time}")
            
            # 显示关键命令行参数
            cmdline_str = ' '.join(proc['cmdline'])
            if '--user-data-dir' in cmdline_str:
                start = cmdline_str.find('--user-data-dir=') + 16
                end = cmdline_str.find(' ', start)
                if end == -1:
                    end = len(cmdline_str)
                user_data_dir = cmdline_str[start:end]
                print(f"    用户数据目录: {user_data_dir}")
    else:
        print("  ✅ 没有发现个人Chrome进程")
    
    print()
    print("🤖 程序Chrome进程:")
    if program_chrome:
        for proc in program_chrome:
            create_time = datetime.fromtimestamp(proc['create_time']).strftime('%H:%M:%S')
            print(f"  PID: {proc['pid']}, 启动时间: {create_time}")
            
            # 显示关键命令行参数
            cmdline_str = ' '.join(proc['cmdline'])
            
            # 提取调试端口
            if '--remote-debugging-port=' in cmdline_str:
                start = cmdline_str.find('--remote-debugging-port=') + 24
                end = cmdline_str.find(' ', start)
                if end == -1:
                    end = len(cmdline_str)
                debug_port = cmdline_str[start:end]
                print(f"    调试端口: {debug_port}")
            
            # 提取用户数据目录
            if '--user-data-dir=' in cmdline_str:
                start = cmdline_str.find('--user-data-dir=') + 16
                end = cmdline_str.find(' ', start)
                if end == -1:
                    end = len(cmdline_str)
                user_data_dir = cmdline_str[start:end]
                print(f"    用户数据目录: {user_data_dir}")
    else:
        print("  ✅ 没有发现程序Chrome进程")
    
    print()
    print("❓ 未知Chrome进程:")
    if unknown_chrome:
        for proc in unknown_chrome:
            create_time = datetime.fromtimestamp(proc['create_time']).strftime('%H:%M:%S')
            print(f"  PID: {proc['pid']}, 启动时间: {create_time}")
            print(f"    命令行: {' '.join(proc['cmdline'][:3])}...")
    else:
        print("  ✅ 没有发现未知Chrome进程")
    
    return {
        'personal': personal_chrome,
        'program': program_chrome,
        'unknown': unknown_chrome
    }

def safe_cleanup_program_chrome():
    """安全清理程序Chrome进程（保护个人浏览器）"""
    print("\n🔧 安全清理程序Chrome进程...")
    
    analysis = analyze_chrome_processes()
    program_chrome = analysis['program']
    
    if not program_chrome:
        print("✅ 没有发现需要清理的程序Chrome进程")
        return 0
    
    print(f"🎯 发现 {len(program_chrome)} 个程序Chrome进程需要清理")
    
    cleaned_count = 0
    for proc in program_chrome:
        try:
            process = psutil.Process(proc['pid'])
            process.terminate()
            print(f"  ✅ 清理程序Chrome进程: PID {proc['pid']}")
            cleaned_count += 1
        except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
            print(f"  ❌ 无法清理进程 PID {proc['pid']}: {e}")
    
    print(f"📊 成功清理 {cleaned_count} 个程序Chrome进程")
    return cleaned_count

def explain_chrome_architecture():
    """解释Chrome架构和冲突原因"""
    print("\n📚 Chrome架构和冲突原因解释")
    print("=" * 80)
    
    print("🏗️ Chrome架构:")
    print("  您的程序:")
    print("    Python → Selenium → ChromeDriver → 系统Chrome.exe")
    print("  您的个人使用:")
    print("    直接启动 → 系统Chrome.exe")
    print()
    print("🔗 共享资源:")
    print("  ✅ 同一个Chrome.exe可执行文件")
    print("  ✅ 同一个Chrome进程管理器")
    print("  ✅ 同一套系统资源（内存、CPU、网络）")
    print("  ✅ Chrome的全局设置和注册表项")
    print()
    print("⚠️ 冲突原因:")
    print("  1. 进程名称相同 - 都叫chrome.exe")
    print("  2. 进程树共享 - 所有Chrome实例在同一进程树下")
    print("  3. 资源竞争 - 争抢端口、内存、文件句柄")
    print("  4. 清理误伤 - 无法准确区分进程来源")
    print()
    print("✅ 解决方案:")
    print("  1. 精确进程识别 - 通过命令行参数区分")
    print("  2. 选择性清理 - 只清理程序启动的Chrome")
    print("  3. 资源隔离 - 使用独立的用户数据目录")
    print("  4. 端口管理 - 分配专用调试端口")

def main():
    """主函数"""
    print("🔍 Chrome进程分析工具")
    print("=" * 80)
    print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # 分析当前Chrome进程
    analysis = analyze_chrome_processes()
    
    # 解释架构和冲突原因
    explain_chrome_architecture()
    
    # 提供安全清理选项
    print("\n🔧 安全清理选项")
    print("=" * 80)
    
    program_count = len(analysis['program'])
    personal_count = len(analysis['personal'])
    
    print(f"📊 当前状态:")
    print(f"  个人Chrome进程: {personal_count} 个")
    print(f"  程序Chrome进程: {program_count} 个")
    
    if program_count > 0:
        print(f"\n⚠️ 发现 {program_count} 个程序Chrome进程")
        print("这些进程可能是之前程序运行留下的残留进程")
        print("建议清理这些进程以避免冲突")
        
        response = input("\n是否要安全清理程序Chrome进程？(y/n): ").lower().strip()
        if response == 'y':
            cleaned = safe_cleanup_program_chrome()
            print(f"\n✅ 清理完成，共清理 {cleaned} 个程序Chrome进程")
            print("您的个人Chrome浏览器未受影响")
        else:
            print("跳过清理")
    else:
        print("\n✅ 没有发现程序Chrome进程残留")
        print("当前状态良好，无需清理")
    
    print("\n📋 建议:")
    print("  1. 程序退出时确保正常清理Chrome进程")
    print("  2. 定期检查是否有Chrome进程残留")
    print("  3. 使用精确的进程识别避免误清理")
    print("  4. 保持个人浏览器和程序浏览器的隔离")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断分析")
    except Exception as e:
        print(f"\n❌ 分析过程异常: {e}")
        import traceback
        traceback.print_exc()
