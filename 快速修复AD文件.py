#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复损坏的AD文件
"""

import os
import shutil
from pathlib import Path

def fix_corrupted_ad_file():
    """修复损坏的AD文件"""
    print("修复损坏的AD文件...")
    
    ad_file = Path("data/browser_profiles/account_10/AutofillStates/2025.6.13.84507/AD")
    
    if not ad_file.exists():
        print("AD文件不存在")
        return True
    
    try:
        # 1. 备份原文件
        backup_file = ad_file.with_name("AD.corrupted.backup")
        shutil.copy2(ad_file, backup_file)
        print(f"已备份损坏文件到: {backup_file}")
        
        # 2. 删除损坏的文件
        ad_file.unlink()
        print("已删除损坏的AD文件")
        
        print("✅ 修复完成！")
        print("Chrome会在下次启动时重新下载正确的AD文件")
        
        return True
        
    except Exception as e:
        print(f"修复失败: {e}")
        return False

def main():
    """主函数"""
    print("快速修复AD文件")
    print("=" * 30)
    
    print("问题分析:")
    print("- AD文件的分隔符(���)丢失")
    print("- 数据格式损坏，无法正常解析")
    print("- 需要删除让Chrome重新下载")
    print()
    
    choice = input("是否立即修复AD文件？(y/n): ").lower().strip()
    
    if choice == 'y':
        success = fix_corrupted_ad_file()
        if success:
            print("\n🎉 AD文件修复成功！")
            print("建议:")
            print("  1. 重启Chrome相关程序")
            print("  2. Chrome会自动重新下载AD文件")
            print("  3. 新的AD文件将包含正确的分隔符")
        else:
            print("\n❌ AD文件修复失败")
    else:
        print("跳过修复")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"\n异常: {e}")
