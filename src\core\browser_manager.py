#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器管理模块 - Selenium版本
"""

import asyncio
import json
import random
import threading
from pathlib import Path
from typing import Dict, Optional, Any, List
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import WebDriverException, TimeoutException
from webdriver_manager.chrome import ChromeDriverManager
import undetected_chromedriver as uc
from fake_useragent import UserAgent

from src.database.models import Account
from src.utils.logger import LoggerMixin
from src.config.settings import get_settings
from src.core.anti_detection import AntiDetectionEngine
import re


class ChromeProcessIdentifier:
    """Chrome进程精确识别器 - 基于端口和目录特征"""

    @staticmethod
    def is_program_chrome(process_cmdline, project_path=None):
        """判断是否是程序启动的Chrome - 基于端口和目录特征"""
        if not process_cmdline:
            return False

        cmdline_str = ' '.join(process_cmdline)

        # 特征1: 端口特征 - 程序使用9000-9999端口
        port_pattern = r'--remote-debugging-port=9\d{3}'
        has_program_port = re.search(port_pattern, cmdline_str)

        # 特征2: 目录特征 - 程序使用browser_profiles目录
        has_browser_profiles = 'browser_profiles' in cmdline_str

        # 特征3: 账号目录特征 - 包含account_前缀
        has_account_dir = 'account_' in cmdline_str

        # 特征4: 项目路径特征 - 包含项目目录
        if project_path:
            has_project_path = project_path.replace('\\', '/') in cmdline_str.replace('\\', '/')
        else:
            has_project_path = False

        # 满足任意一个明确特征就认为是程序Chrome
        program_indicators = [has_program_port, has_browser_profiles, has_account_dir, has_project_path]

        return any(program_indicators)

    @staticmethod
    def is_personal_chrome(process_cmdline):
        """判断是否是个人Chrome"""
        if not process_cmdline:
            return False

        cmdline_str = ' '.join(process_cmdline)

        # 个人Chrome特征
        personal_indicators = [
            'AppData\\Local\\Google\\Chrome\\User Data',
            'User Data\\Default',
            '--restore-last-session',
            '--flag-switches-begin'
        ]

        return any(indicator in cmdline_str for indicator in personal_indicators)


class ChromePortManager:
    """Chrome端口管理器 - 解决并发端口冲突"""

    def __init__(self):
        self.base_port = 9000
        self.max_port = 9999
        self.allocated_ports = set()
        self.account_ports = {}  # account_id -> port
        self.lock = asyncio.Lock()

    async def allocate_port(self, account_id: int) -> int:
        """为账号分配专用端口"""
        async with self.lock:
            # 如果账号已有端口，直接返回
            if account_id in self.account_ports:
                port = self.account_ports[account_id]
                if await self._is_port_available(port):
                    return port
                else:
                    # 端口被占用，重新分配
                    self.allocated_ports.discard(port)
                    del self.account_ports[account_id]

            # 分配新端口
            for port in range(self.base_port, self.max_port + 1):
                if port not in self.allocated_ports and await self._is_port_available(port):
                    self.allocated_ports.add(port)
                    self.account_ports[account_id] = port
                    return port

            raise Exception("无可用端口")

    async def release_port(self, account_id: int):
        """释放账号端口"""
        async with self.lock:
            if account_id in self.account_ports:
                port = self.account_ports[account_id]
                self.allocated_ports.discard(port)
                del self.account_ports[account_id]

    async def _is_port_available(self, port: int) -> bool:
        """检查端口是否可用"""
        import socket
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('127.0.0.1', port))
            sock.close()
            return result != 0  # 连接失败说明端口可用
        except Exception:
            return False


class SeleniumDriverWrapper:
    """Selenium WebDriver包装器，提供异步接口"""

    def __init__(self, driver: webdriver.Chrome, account_id: int):
        self.driver = driver
        self.account_id = account_id
        self.executor = ThreadPoolExecutor(max_workers=1)
        self._closed = False

    async def execute_async(self, func, *args, **kwargs):
        """异步执行同步函数"""
        if self._closed:
            raise WebDriverException("Driver已关闭")

        try:
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(self.executor, func, *args, **kwargs)
        except Exception as e:
            # 检查是否是会话相关的错误
            error_msg = str(e).lower()
            if any(keyword in error_msg for keyword in [
                'invalid session id', 'session deleted', 'disconnected',
                'not connected to devtools', 'chrome not reachable'
            ]):
                self._closed = True
                raise WebDriverException("Driver会话已断开")
            raise

    async def get(self, url: str):
        """导航到URL"""
        return await self.execute_async(self.driver.get, url)

    async def find_element(self, by: By, value: str):
        """查找元素"""
        return await self.execute_async(self.driver.find_element, by, value)

    async def find_elements(self, by: By, value: str):
        """查找多个元素"""
        return await self.execute_async(self.driver.find_elements, by, value)

    async def execute_script(self, script: str, *args):
        """执行JavaScript"""
        return await self.execute_async(self.driver.execute_script, script, *args)

    async def is_session_valid(self) -> bool:
        """检查会话是否有效 - 增强版"""
        if self._closed:
            return False

        try:
            # 使用多种方法检查会话有效性
            # 方法1: 检查当前URL
            current_url = await self.execute_async(lambda: self.driver.current_url)
            if not current_url or current_url == 'data:,':
                return False

            # 方法2: 检查窗口句柄
            handles = await self.execute_async(lambda: self.driver.window_handles)
            if not handles:
                return False

            # 方法3: 执行简单的JavaScript
            result = await self.execute_async(
                lambda: self.driver.execute_script("return document.readyState")
            )
            if not result:
                return False

            return True

        except Exception as e:
            # 检查是否是会话相关的错误
            error_msg = str(e).lower()
            if any(keyword in error_msg for keyword in [
                'invalid session id', 'session deleted', 'disconnected',
                'not connected to devtools', 'chrome not reachable',
                'driver已关闭', 'driver会话已断开'
            ]):
                self._closed = True
            return False

    async def close(self):
        """关闭驱动"""
        if not self._closed:
            self._closed = True
            await self.execute_async(self.driver.quit)
            self.executor.shutdown(wait=True)

    @property
    def current_url(self) -> str:
        """当前URL"""
        return self.driver.current_url

    @property
    def page_source(self) -> str:
        """页面源码"""
        return self.driver.page_source


class BrowserPool(LoggerMixin):
    """浏览器池管理器 - Selenium版本"""

    def __init__(self, max_browsers: int = None):
        self.settings = get_settings()
        self.max_browsers = max_browsers or self.settings.max_browsers
        self.drivers: Dict[int, SeleniumDriverWrapper] = {}  # account_id -> driver_wrapper
        self.lock = asyncio.Lock()
        self.anti_detection = AntiDetectionEngine()
        self.user_agent = UserAgent()
        self.protected_processes: Dict[int, set] = {}  # account_id -> set of protected PIDs

        # 🚀 端口管理器 - 解决并发端口冲突
        self.port_manager = ChromePortManager()

        # 🚀 启动信号量 - 控制并发启动数量
        self.startup_semaphore = asyncio.Semaphore(2)  # 最多同时启动2个Chrome

    async def initialize(self):
        """初始化浏览器池 - 优化启动速度"""
        try:
            # 🚀 预热ChromeDriver - 避免每次启动时检查
            await self._preload_chromedriver()

            # 🚀 预创建用户数据目录模板
            await self._prepare_user_data_templates()

            self.logger.info("浏览器池初始化完成")
        except Exception as e:
            self.logger.error(f"浏览器池初始化失败: {e}")
            raise

    async def _preload_chromedriver(self):
        """预加载ChromeDriver，避免启动时延迟"""
        try:
            import concurrent.futures
            loop = asyncio.get_event_loop()

            # 在后台线程中预加载
            def preload():
                try:
                    ChromeDriverManager().install()
                    return True
                except Exception as e:
                    self.logger.warning(f"ChromeDriver预加载失败: {e}")
                    return False

            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(preload)
                success = await loop.run_in_executor(None, lambda: future.result(timeout=10))

            if success:
                self.logger.info("✅ ChromeDriver预加载成功")
            else:
                self.logger.warning("⚠️ ChromeDriver预加载失败，将在启动时重试")

        except Exception as e:
            self.logger.warning(f"ChromeDriver预加载异常: {e}")

    async def _prepare_user_data_templates(self):
        """预创建用户数据目录模板，加速启动"""
        try:
            base_dir = Path(self.settings.browser_user_data_dir)
            base_dir.mkdir(parents=True, exist_ok=True)

            # 创建模板目录
            template_dir = base_dir / "template"
            if not template_dir.exists():
                template_dir.mkdir(parents=True, exist_ok=True)

                # 创建基本的Chrome配置文件
                prefs_file = template_dir / "Default" / "Preferences"
                prefs_file.parent.mkdir(parents=True, exist_ok=True)

                # 基本的Chrome偏好设置
                basic_prefs = {
                    "profile": {
                        "default_content_setting_values": {
                            "notifications": 2,  # 禁用通知
                            "geolocation": 2,    # 禁用地理位置
                        },
                        "managed_default_content_settings": {
                            "images": 1  # 允许图片（调试时需要）
                        }
                    },
                    "browser": {
                        "check_default_browser": False
                    }
                }

                import json
                with open(prefs_file, 'w', encoding='utf-8') as f:
                    json.dump(basic_prefs, f, indent=2)

                self.logger.info("✅ 用户数据目录模板创建成功")

        except Exception as e:
            self.logger.warning(f"用户数据目录模板创建失败: {e}")
    
    async def get_driver(self, account: Account) -> Optional[SeleniumDriverWrapper]:
        """
        获取账号对应的WebDriver - 增强Chrome连接稳定性

        Args:
            account: 账号对象

        Returns:
            WebDriver包装器
        """
        async with self.lock:
            if account.id in self.drivers:
                # 检查驱动是否仍然有效
                driver_wrapper = self.drivers[account.id]
                try:
                    if await driver_wrapper.is_session_valid():
                        self.logger.debug(f"重用现有WebDriver: {account.username}")
                        return driver_wrapper
                    else:
                        # 驱动无效，需要重新创建
                        self.logger.warning(f"WebDriver会话无效，重新创建: {account.username}")
                        await self._cleanup_account_driver(account.id)
                except Exception as e:
                    self.logger.warning(f"检查WebDriver会话失败: {e}")
                    await self._cleanup_account_driver(account.id)

            # 🔧 只在第一次创建时进行预清理
            # 注释掉预清理，避免误杀正常的Chrome进程
            # await self._pre_cleanup_chrome_conflicts(account.id)

            # 🚀 使用启动信号量控制并发
            async with self.startup_semaphore:
                # 🚀 为账号分配专用端口
                debug_port = await self.port_manager.allocate_port(account.id)
                self.logger.info(f"为账号 {account.username} 分配端口: {debug_port}")

                # 🚀 智能创建策略 - 解决Chrome连接问题和并发冲突
                for attempt in range(3):
                    try:
                        self.logger.info(f"创建WebDriver尝试 {attempt + 1}/3: {account.username} (端口: {debug_port})")

                        # 🔧 并发环境下的智能延迟策略
                        if attempt == 0 and len(self.drivers) > 0:
                            # 如果已有其他浏览器在运行，添加随机延迟避免资源竞争
                            import random
                            delay = random.uniform(1.0, 3.0)
                            self.logger.debug(f"并发创建延迟: {delay:.2f}秒 (当前浏览器数: {len(self.drivers)})")
                            await asyncio.sleep(delay)

                        # 🔧 每次尝试前都进行清理
                        if attempt > 0:
                            self.logger.info(f"尝试 {attempt + 1}: 清理Chrome进程...")
                            self._cleanup_chrome_processes_for_account(account.id)
                            await asyncio.sleep(2)  # 等待清理完成

                        # 🚀 优先使用快速创建方法
                        if attempt == 0:
                            driver_wrapper = await self._fast_create_driver(account, debug_port)
                        else:
                            # 备用方法：传统创建
                            driver_wrapper = await self._create_driver(account, debug_port)

                        if driver_wrapper:
                            # 🔧 创建成功后立即验证稳定性
                            try:
                                await driver_wrapper.execute_script('return document.readyState')
                                await driver_wrapper.execute_script('return window.location.href')
                            except Exception as e:
                                self.logger.warning(f"WebDriver创建后稳定性检查失败: {e}")
                                try:
                                    await driver_wrapper.close()
                                except Exception:
                                    pass
                                if attempt < 2:
                                    continue
                                else:
                                    raise Exception("WebDriver稳定性检查失败")

                            self.drivers[account.id] = driver_wrapper

                            # 🔒 保护当前账号的Chrome进程
                            await self._protect_chrome_processes(account.id)

                            # 检查是否超过最大数量
                            if len(self.drivers) > self.max_browsers:
                                await self._cleanup_oldest_driver()

                            self.logger.info(f"✅ WebDriver创建成功: {account.username}")
                            return driver_wrapper

                    except Exception as e:
                        self.logger.warning(f"创建WebDriver失败 (尝试 {attempt + 1}/3): {e}")

                        # 🔧 只在失败时清理，成功时不清理
                        if attempt < 2:  # 不是最后一次尝试
                            self.logger.info(f"尝试 {attempt + 1} 失败，清理后重试...")
                            self._cleanup_chrome_processes_for_account(account.id)
                            await asyncio.sleep(2)  # 增加重试等待时间
                        continue

            self.logger.error(f"创建WebDriver最终失败: {account.username}")
            return None
    
    async def _create_driver(self, account: Account, debug_port: int) -> Optional[SeleniumDriverWrapper]:
        """
        创建WebDriver

        Args:
            account: 账号对象

        Returns:
            WebDriver包装器
        """
        try:
            # 生成浏览器指纹
            fingerprint = self.anti_detection.generate_fingerprint(account.proxy)

            # 🚀 智能用户数据目录选择 - 优先使用最可靠的目录
            user_data_dir = self._get_optimal_user_data_dir(account.id)

            # 配置Chrome选项
            options = uc.ChromeOptions()

            # 基本选项
            options.add_argument(f"--user-data-dir={user_data_dir}")
            options.add_argument(f"--user-agent={fingerprint['userAgent']}")
            options.add_argument(f"--window-size={fingerprint['viewport']['width']},{fingerprint['viewport']['height']}")

            # 配置代理
            if account.proxy:
                proxy_url = account.proxy.proxy_url
                if account.proxy.username and account.proxy.password:
                    # 如果有用户名密码，需要特殊处理
                    proxy_url = f"{account.proxy.username}:{account.proxy.password}@{proxy_url}"
                options.add_argument(f"--proxy-server={proxy_url}")

            # 简化的反检测选项 - 只保留必要的选项
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--no-first-run')
            options.add_argument('--no-default-browser-check')
            options.add_argument('--disable-infobars')
            options.add_argument('--disable-extensions')

            # 如果是有头模式，移除一些可能导致问题的选项
            if not self.settings.browser_headless:
                self.logger.info("使用有头模式，简化Chrome选项")
            else:
                # 无头模式需要的额外选项
                options.add_argument('--disable-gpu')
                options.add_argument('--no-sandbox')
            options.add_argument('--disable-features=TranslateUI')
            options.add_argument('--disable-hang-monitor')
            options.add_argument('--disable-prompt-on-repost')
            options.add_argument('--disable-background-mode')
            options.add_argument('--force-color-profile=srgb')
            options.add_argument('--metrics-recording-only')
            options.add_argument('--safebrowsing-disable-auto-update')
            options.add_argument('--enable-automation=false')
            options.add_argument('--password-store=basic')
            options.add_argument('--use-mock-keychain')

            # 设置headless模式
            if self.settings.browser_headless:
                options.add_argument('--headless=new')

            # 设置语言和时区
            options.add_argument(f"--lang={fingerprint.get('language', 'en-US')}")

            # 禁用图片加载以提高速度（可选）
            # options.add_argument('--blink-settings=imagesEnabled=false')

            # 简化实验性选项配置
            try:
                # 设置prefs - 只设置基本的偏好设置
                prefs = {
                    "profile.default_content_setting_values": {
                        "notifications": 2,  # 禁用通知
                    }
                }
                options.add_experimental_option("prefs", prefs)
            except Exception as e:
                self.logger.warning(f"设置实验性选项失败，跳过: {e}")
            
            # 🚀 优化的Chrome创建函数
            def create_driver():
                try:
                    import time
                    start_time = time.time()

                    # 🚀 快速创建Chrome选项
                    options = self._create_optimized_chrome_options(account, fingerprint, user_data_dir)

                    self.logger.info("🚀 创建Chrome实例...")

                    # 🚀 优化的Chrome实例创建
                    driver = uc.Chrome(
                        options=options,
                        version_main=None,
                        suppress_welcome=True,
                        use_subprocess=False,
                        debug=False,
                        service_args=['--silent', '--log-level=3', '--disable-logging']  # 减少日志输出
                    )

                    end_time = time.time()
                    startup_time = end_time - start_time
                    self.logger.info(f"✅ Chrome创建成功！耗时: {startup_time:.2f}秒")

                    return driver

                except Exception as e:
                    self.logger.error(f"Chrome创建失败: {e}")
                    raise

            # 在线程池中创建驱动
            loop = asyncio.get_event_loop()
            driver = await loop.run_in_executor(None, create_driver)

            # 创建包装器
            driver_wrapper = SeleniumDriverWrapper(driver, account.id)

            # 注入反检测脚本
            await self._inject_stealth_scripts(driver_wrapper)

            # 设置cookies
            if account.cookies:
                await self._set_cookies(driver_wrapper, account.cookies)

            # 保存指纹信息
            from src.core.account_manager import AccountManager
            account_manager = AccountManager()
            account_manager.update_account_fingerprint(account.id, fingerprint)

            self.logger.info(f"创建WebDriver成功: {account.username}")
            return driver_wrapper

        except Exception as e:
            self.logger.error(f"创建WebDriver失败: {e}")
            return None

    def _create_optimized_chrome_options(self, account: Account, fingerprint: dict, user_data_dir: Path, debug_port: int = None):
        """🚀 创建优化的Chrome选项 - 解决连接和兼容性问题"""
        options = uc.ChromeOptions()

        # 🎯 核心必需选项
        options.add_argument(f"--user-data-dir={user_data_dir}")
        options.add_argument(f"--user-agent={fingerprint['userAgent']}")
        options.add_argument(f"--window-size={fingerprint['viewport']['width']},{fingerprint['viewport']['height']}")

        # 🚀 专用调试端口 - 解决并发冲突
        if debug_port:
            options.add_argument(f"--remote-debugging-port={debug_port}")

        # 🌐 代理设置
        if account.proxy:
            proxy_url = account.proxy.proxy_url
            if account.proxy.username and account.proxy.password:
                proxy_url = f"{account.proxy.username}:{account.proxy.password}@{proxy_url}"
            options.add_argument(f"--proxy-server={proxy_url}")

        # 🔧 连接稳定性参数 - 解决Chrome连接问题
        stability_args = [
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu-sandbox',
            '--disable-software-rasterizer',
            '--disable-background-timer-throttling',
            '--disable-renderer-backgrounding',
            '--disable-backgrounding-occluded-windows',
            '--disable-features=VizDisplayCompositor',
            '--disable-features=TranslateUI',
            '--disable-ipc-flooding-protection',
            '--disable-hang-monitor',
            '--disable-prompt-on-repost',
            '--disable-domain-reliability',
            '--disable-component-extensions-with-background-pages',
            '--disable-background-networking',
            '--disable-sync',
            '--disable-default-apps',
            '--disable-extensions',
            '--disable-plugins-discovery',
            '--disable-translate',
            '--no-first-run',
            '--no-default-browser-check',
            '--disable-infobars',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--disable-logging',
            '--silent'
        ]

        # 🛡️ 反检测参数
        anti_detection_args = [
            '--disable-blink-features=AutomationControlled',
            '--exclude-switches=enable-automation',
            '--disable-extensions-file-access-check',
            '--disable-extensions-http-throttling'
        ]

        # 🚀 性能优化参数
        if self.settings.browser_headless:
            performance_args = [
                '--headless=new',
                '--disable-gpu',
                '--disable-images',
                '--disable-plugins',
                '--disable-java',
                '--disable-flash'
            ]
        else:
            performance_args = [
                '--disable-plugins',
                '--disable-java',
                '--disable-flash'
            ]

        # 🔧 批量添加参数 - 避免重复
        all_args = stability_args + anti_detection_args + performance_args
        unique_args = list(dict.fromkeys(all_args))  # 去重

        for arg in unique_args:
            try:
                options.add_argument(arg)
            except Exception as e:
                self.logger.debug(f"添加Chrome参数失败: {arg}, 错误: {e}")
                continue

        # 🎛️ 性能偏好设置
        prefs = {
            "profile.default_content_setting_values": {
                "notifications": 2,  # 禁用通知
                "geolocation": 2,    # 禁用地理位置
                "media_stream": 2,   # 禁用摄像头/麦克风
            },
            "profile.managed_default_content_settings": {
                "images": 1 if not self.settings.browser_headless else 2  # 有头模式保留图片
            }
        }
        options.add_experimental_option("prefs", prefs)

        # 🔧 反检测已在essential_args中包含，无需额外设置

        return options

    async def _fast_create_driver(self, account: Account, debug_port: int) -> Optional[SeleniumDriverWrapper]:
        """🚀 快速创建WebDriver - 解决Chrome连接问题"""
        try:
            import time
            total_start_time = time.time()

            # 🔧 步骤0: 预清理可能的Chrome进程冲突
            await self._pre_cleanup_chrome_conflicts(account.id)

            # 🚀 步骤1: 快速生成指纹（缓存优化）
            fingerprint = self.anti_detection.generate_fingerprint(account.proxy)

            # 🚀 步骤2: 快速获取用户数据目录
            user_data_dir = self._get_optimal_user_data_dir(account.id)

            # 🚀 步骤3: 预创建Chrome选项
            options = self._create_optimized_chrome_options(account, fingerprint, user_data_dir, debug_port)

            # 🚀 步骤4: 彻底重构Chrome创建 - 解决所有连接问题
            def create_chrome_with_complete_cleanup():
                max_attempts = 3
                for attempt in range(max_attempts):
                    try:
                        self.logger.info(f"🚀 Chrome启动尝试 {attempt + 1}/{max_attempts}...")
                        start = time.time()

                        # 🔧 每次尝试前彻底清理
                        if attempt > 0:
                            self.logger.info(f"尝试 {attempt + 1}: 彻底清理Chrome环境...")
                            self._complete_chrome_cleanup(account.id)
                            time.sleep(3)  # 增加等待时间

                        # 🔧 清理可能的端口冲突
                        self._cleanup_chrome_ports()

                        # 🔧 重新创建Chrome选项 - 解决重用问题
                        fresh_options = self._create_fresh_chrome_options(account, fingerprint, user_data_dir, debug_port)

                        # 🚀 使用最稳定的Chrome创建方式
                        driver = None
                        try:
                            # 🔧 添加更多稳定性参数
                            fresh_options.add_argument("--no-first-run")
                            fresh_options.add_argument("--no-default-browser-check")
                            fresh_options.add_argument("--disable-default-apps")
                            fresh_options.add_argument("--disable-popup-blocking")
                            fresh_options.add_argument("--disable-translate")
                            fresh_options.add_argument("--disable-background-timer-throttling")
                            fresh_options.add_argument("--disable-renderer-backgrounding")
                            fresh_options.add_argument("--disable-backgrounding-occluded-windows")

                            # 🔧 首先尝试标准Selenium（更稳定）
                            from selenium import webdriver
                            from selenium.webdriver.chrome.service import Service

                            try:
                                service = Service()
                                driver = webdriver.Chrome(service=service, options=fresh_options)
                                self.logger.info("✅ 使用标准Selenium创建Chrome成功")
                            except Exception as selenium_error:
                                self.logger.warning(f"标准Selenium失败，尝试undetected-chromedriver: {selenium_error}")
                                # 备用方案：使用undetected-chromedriver
                                driver = uc.Chrome(
                                    options=fresh_options,
                                    version_main=None,
                                    suppress_welcome=True,
                                    use_subprocess=False,  # 避免子进程问题
                                    debug=False
                                )
                                self.logger.info("✅ 使用undetected-chromedriver创建Chrome成功")

                        except Exception as chrome_error:
                            self.logger.error(f"所有Chrome创建方式都失败: {chrome_error}")
                            raise

                        if driver:
                            end = time.time()
                            self.logger.info(f"✅ Chrome启动成功！耗时: {end - start:.2f}秒")
                            return driver
                        else:
                            raise Exception("Chrome驱动创建失败")

                    except Exception as e:
                        self.logger.warning(f"Chrome启动尝试 {attempt + 1} 失败: {e}")
                        if attempt < max_attempts - 1:
                            # 彻底清理后重试
                            self._complete_chrome_cleanup(account.id)
                            time.sleep(5)  # 增加等待时间
                        else:
                            # 最后一次尝试失败，记录详细错误
                            self.logger.error(f"Chrome启动彻底失败: {e}")
                            raise

                raise Exception("所有Chrome启动尝试都失败")

            # 在线程池中创建
            loop = asyncio.get_event_loop()
            driver = await loop.run_in_executor(None, create_chrome_with_complete_cleanup)

            # 🚀 步骤5: 创建包装器
            driver_wrapper = SeleniumDriverWrapper(driver, account.id)

            # 🚀 步骤6: 同步注入脚本（避免会话断开）
            try:
                await self._inject_stealth_scripts(driver_wrapper)
                self.logger.debug("反检测脚本注入成功")
            except Exception as e:
                self.logger.warning(f"注入反检测脚本失败: {e}")

            # 🚀 步骤7: 同步设置cookies（避免会话断开）
            if account.cookies:
                try:
                    await self._set_cookies(driver_wrapper, account.cookies)
                    self.logger.debug("Cookies设置成功")
                except Exception as e:
                    self.logger.warning(f"设置cookies失败: {e}")

            # 🔧 等待Driver完全稳定
            await asyncio.sleep(1)

            # 🔧 验证Driver会话稳定性
            try:
                is_valid = await driver_wrapper.is_session_valid()
                if not is_valid:
                    raise Exception("Driver会话创建后立即失效")
                self.logger.debug("Driver会话稳定性验证通过")
            except Exception as e:
                self.logger.warning(f"Driver会话稳定性验证失败: {e}")
                # 不抛出异常，继续使用

            total_end_time = time.time()
            total_time = total_end_time - total_start_time
            self.logger.info(f"🎉 WebDriver快速创建完成！总耗时: {total_time:.2f}秒")

            return driver_wrapper

        except Exception as e:
            self.logger.error(f"快速创建WebDriver失败: {e}")
            # 最后清理
            self._cleanup_chrome_processes_for_account(account.id)
            return None

    async def _pre_cleanup_chrome_conflicts(self, account_id: int):
        """保守的预清理Chrome进程冲突 - 只清理明确有问题的进程"""
        try:
            self.logger.debug(f"保守预清理Chrome冲突 (账号 {account_id})...")

            # 只清理端口冲突，不清理正常的Chrome进程
            self._cleanup_port_conflicts()

            # 只清理明确崩溃或僵死的Chrome进程
            self._cleanup_dead_chrome_processes(account_id)

            # 等待清理完成
            await asyncio.sleep(0.3)

        except Exception as e:
            self.logger.debug(f"预清理Chrome冲突失败: {e}")

    def _cleanup_chrome_processes_for_account(self, account_id: int):
        """安全清理特定账号的Chrome进程（保护个人浏览器）"""
        try:
            import psutil
            import os

            cleaned_count = 0
            protected_count = 0
            project_path = os.getcwd()
            user_data_pattern = f"account_{account_id}"

            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                        cmdline = proc.info['cmdline'] or []
                        cmdline_str = ' '.join(cmdline)

                        # 首先检查是否是该账号的Chrome进程
                        if user_data_pattern in cmdline_str:
                            # 🔒 检查进程是否被保护
                            if self._is_process_protected(account_id, proc.info['pid']):
                                self.logger.debug(f"跳过保护的Chrome进程: {proc.info['pid']}")
                                continue

                            self.logger.debug(f"终止账号 {account_id} 的Chrome进程: {proc.info['pid']}")
                            proc.terminate()
                            cleaned_count += 1

                        # 使用特征识别进行额外的安全检查
                        elif ChromeProcessIdentifier.is_program_chrome(cmdline, project_path):
                            # 这是程序Chrome但不是当前账号的，也可以清理（可能是残留）
                            self.logger.debug(f"清理程序Chrome残留进程: {proc.info['pid']}")
                            proc.terminate()
                            cleaned_count += 1
                        elif ChromeProcessIdentifier.is_personal_chrome(cmdline):
                            # 这是个人Chrome，绝对保护
                            self.logger.debug(f"保护个人Chrome进程: {proc.info['pid']}")
                            protected_count += 1

                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                except Exception as e:
                    self.logger.debug(f"清理进程失败: {e}")
                    continue

            if cleaned_count > 0:
                self.logger.info(f"安全清理了 {cleaned_count} 个程序Chrome进程")
            if protected_count > 0:
                self.logger.debug(f"保护了 {protected_count} 个个人Chrome进程")

        except Exception as e:
            self.logger.debug(f"清理账号Chrome进程失败: {e}")

    def _cleanup_port_conflicts(self):
        """清理端口冲突"""
        try:
            import psutil

            # 检查常用的Chrome调试端口
            common_ports = [9222, 9223, 9224, 9225, 9226]

            for port in common_ports:
                try:
                    for conn in psutil.net_connections():
                        if conn.laddr.port == port and conn.status == 'LISTEN':
                            try:
                                proc = psutil.Process(conn.pid)
                                if 'chrome' in proc.name().lower():
                                    self.logger.debug(f"终止占用端口 {port} 的Chrome进程: {conn.pid}")
                                    proc.terminate()
                            except (psutil.NoSuchProcess, psutil.AccessDenied):
                                continue
                except Exception:
                    continue

        except Exception as e:
            self.logger.debug(f"清理端口冲突失败: {e}")

    def _cleanup_dead_chrome_processes(self, account_id: int):
        """只清理明确崩溃或僵死的Chrome进程"""
        try:
            import psutil

            cleaned_count = 0
            user_data_pattern = f"account_{account_id}"

            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'status']):
                try:
                    if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                        cmdline = proc.info['cmdline'] or []
                        cmdline_str = ' '.join(cmdline)

                        # 只处理该账号的Chrome进程
                        if user_data_pattern in cmdline_str:
                            # 检查进程状态
                            status = proc.info.get('status', '')

                            # 只清理明确有问题的进程
                            if status in ['zombie', 'stopped'] or not proc.is_running():
                                self.logger.debug(f"清理僵死Chrome进程: {proc.info['pid']} (状态: {status})")
                                proc.terminate()
                                cleaned_count += 1
                            else:
                                # 检查进程是否响应
                                try:
                                    # 尝试获取进程信息，如果失败说明进程有问题
                                    proc.memory_info()
                                except (psutil.NoSuchProcess, psutil.AccessDenied):
                                    self.logger.debug(f"清理无响应Chrome进程: {proc.info['pid']}")
                                    proc.terminate()
                                    cleaned_count += 1

                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                except Exception as e:
                    self.logger.debug(f"检查进程状态失败: {e}")
                    continue

            if cleaned_count > 0:
                self.logger.info(f"清理了 {cleaned_count} 个有问题的账号 {account_id} Chrome进程")

        except Exception as e:
            self.logger.debug(f"清理僵死Chrome进程失败: {e}")

    async def _protect_chrome_processes(self, account_id: int):
        """保护当前账号的Chrome进程，避免被误清理"""
        try:
            import psutil

            protected_pids = set()
            user_data_pattern = f"account_{account_id}"

            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                        cmdline = proc.info['cmdline'] or []
                        cmdline_str = ' '.join(cmdline)

                        # 保护该账号的Chrome进程
                        if user_data_pattern in cmdline_str:
                            protected_pids.add(proc.info['pid'])

                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                except Exception:
                    continue

            self.protected_processes[account_id] = protected_pids
            if protected_pids:
                self.logger.debug(f"保护账号 {account_id} 的 {len(protected_pids)} 个Chrome进程")

        except Exception as e:
            self.logger.debug(f"保护Chrome进程失败: {e}")

    def _is_process_protected(self, account_id: int, pid: int) -> bool:
        """检查进程是否被保护"""
        return account_id in self.protected_processes and pid in self.protected_processes[account_id]

    def _complete_chrome_cleanup(self, account_id: int):
        """彻底清理Chrome环境 - 解决所有连接问题"""
        try:
            import subprocess
            import psutil
            import time

            self.logger.info(f"开始彻底清理Chrome环境 (账号 {account_id})...")

            # 步骤1: 清理特定账号的Chrome进程
            self._cleanup_chrome_processes_for_account(account_id)

            # 步骤2: 清理所有自动化Chrome进程
            automation_count = 0
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                        cmdline = proc.info['cmdline'] or []
                        cmdline_str = ' '.join(cmdline)

                        # 检查是否是自动化进程
                        automation_indicators = [
                            '--remote-debugging-port',
                            '--disable-blink-features=AutomationControlled',
                            '--test-type',
                            '--enable-automation',
                            'chromedriver'
                        ]

                        if any(indicator in cmdline_str for indicator in automation_indicators):
                            self.logger.debug(f"清理自动化Chrome进程: {proc.info['pid']}")
                            proc.terminate()
                            automation_count += 1

                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                except Exception:
                    continue

            if automation_count > 0:
                self.logger.info(f"清理了 {automation_count} 个自动化Chrome进程")

            # 步骤3: 清理端口冲突
            self._cleanup_port_conflicts()

            # 步骤4: 清理ChromeDriver进程
            try:
                for proc in psutil.process_iter(['pid', 'name']):
                    if proc.info['name'] and 'chromedriver' in proc.info['name'].lower():
                        self.logger.debug(f"清理ChromeDriver进程: {proc.info['pid']}")
                        proc.terminate()
            except Exception as e:
                self.logger.debug(f"清理ChromeDriver进程失败: {e}")

            # 步骤5: 等待进程完全终止
            time.sleep(2)

            self.logger.info("Chrome环境彻底清理完成")

        except Exception as e:
            self.logger.error(f"彻底清理Chrome环境失败: {e}")

    def _create_fresh_chrome_options(self, account: Account, fingerprint: dict, user_data_dir: Path, debug_port: int = None):
        """创建全新的Chrome选项 - 解决重用问题"""
        try:
            # 每次都创建全新的选项对象
            options = uc.ChromeOptions()

            # 🎯 核心必需选项
            options.add_argument(f"--user-data-dir={user_data_dir}")
            options.add_argument(f"--user-agent={fingerprint['userAgent']}")
            options.add_argument(f"--window-size={fingerprint['viewport']['width']},{fingerprint['viewport']['height']}")

            # 🚀 专用调试端口 - 解决并发冲突
            if debug_port:
                options.add_argument(f"--remote-debugging-port={debug_port}")

            # 🌐 代理设置
            if account.proxy:
                proxy_url = account.proxy.proxy_url
                if account.proxy.username and account.proxy.password:
                    proxy_url = f"{account.proxy.username}:{account.proxy.password}@{proxy_url}"
                options.add_argument(f"--proxy-server={proxy_url}")

            # 🔧 最小化但稳定的参数集
            essential_args = [
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--disable-blink-features=AutomationControlled',
                '--no-first-run',
                '--no-default-browser-check',
                '--disable-infobars',
                '--disable-extensions',
                '--disable-default-apps',
                '--disable-sync',
                '--disable-translate',
                '--disable-logging',
                '--silent',
                '--disable-gpu-sandbox'
            ]

            # 🚀 性能优化参数
            if self.settings.browser_headless:
                essential_args.extend([
                    '--headless=new',
                    '--disable-gpu'
                ])

            # 批量添加参数
            for arg in essential_args:
                try:
                    options.add_argument(arg)
                except Exception as e:
                    self.logger.debug(f"添加Chrome参数失败: {arg}, 错误: {e}")
                    continue

            # 🎛️ 基础偏好设置
            prefs = {
                "profile.default_content_setting_values": {
                    "notifications": 2,
                    "geolocation": 2,
                    "media_stream": 2,
                }
            }
            options.add_experimental_option("prefs", prefs)

            return options

        except Exception as e:
            self.logger.error(f"创建Chrome选项失败: {e}")
            # 返回最基础的选项
            basic_options = uc.ChromeOptions()
            basic_options.add_argument(f"--user-data-dir={user_data_dir}")
            basic_options.add_argument('--no-sandbox')
            basic_options.add_argument('--disable-dev-shm-usage')
            return basic_options

    def _smart_cleanup_chrome_processes(self, account_id: int):
        """智能清理Chrome进程 - 只清理可能冲突的进程"""
        try:
            import subprocess
            import psutil
            import time

            # 检查是否有Chrome进程占用相关端口
            chrome_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                        cmdline = proc.info['cmdline'] or []
                        cmdline_str = ' '.join(cmdline)

                        # 检查是否是自动化相关的Chrome进程
                        if any(keyword in cmdline_str for keyword in [
                            '--remote-debugging-port',
                            '--user-data-dir',
                            'chromedriver',
                            f'account_{account_id}'
                        ]):
                            chrome_processes.append(proc.info['pid'])
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            if chrome_processes:
                self.logger.info(f"发现 {len(chrome_processes)} 个可能冲突的Chrome进程，正在清理...")

                # 温和地终止进程
                for pid in chrome_processes:
                    try:
                        proc = psutil.Process(pid)
                        proc.terminate()  # 发送SIGTERM
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue

                # 等待进程退出
                time.sleep(2)

                # 强制杀死仍然存在的进程
                for pid in chrome_processes:
                    try:
                        proc = psutil.Process(pid)
                        if proc.is_running():
                            proc.kill()  # 强制杀死
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue

                self.logger.info("Chrome进程清理完成")
            else:
                self.logger.debug("未发现冲突的Chrome进程")

        except ImportError:
            # 如果psutil不可用，使用简单的清理方式
            self.logger.debug("psutil不可用，使用简单清理方式")
            try:
                import subprocess
                subprocess.run(['taskkill', '/f', '/im', 'chromedriver.exe'],
                             capture_output=True, check=False, timeout=5)
            except Exception:
                pass
        except Exception as e:
            self.logger.debug(f"智能清理失败，继续启动: {e}")

    def _get_optimal_user_data_dir(self, account_id: int) -> Path:
        """获取最优的用户数据目录 - 优先使用项目目录"""
        import tempfile
        import os
        import stat

        # 获取程序根目录
        program_root = Path(__file__).parent.parent.parent

        # 🎯 优先使用项目目录（便携性和性能最佳）
        project_data_dir = program_root / "data" / "browser_profiles" / f"account_{account_id}"

        try:
            # 创建项目数据目录
            project_data_dir.mkdir(parents=True, exist_ok=True)

            # 测试写入权限
            test_file = project_data_dir / "permission_test.tmp"
            test_file.write_text("test")
            test_file.unlink()  # 删除测试文件

            # 设置目录权限（如果可能）
            try:
                os.chmod(project_data_dir, stat.S_IRWXU | stat.S_IRWXG | stat.S_IRWXO)
            except Exception:
                pass  # 权限设置失败不影响使用

            self.logger.info(f"✅ 使用项目数据目录: {project_data_dir}")
            return project_data_dir

        except Exception as e:
            self.logger.warning(f"⚠️ 项目目录不可用: {e}")

            # 备选方案1: 项目临时目录
            try:
                project_temp_dir = program_root / "temp" / "chrome_profiles" / f"account_{account_id}"
                project_temp_dir.mkdir(parents=True, exist_ok=True)

                # 测试写入权限
                test_file = project_temp_dir / "permission_test.tmp"
                test_file.write_text("test")
                test_file.unlink()

                self.logger.info(f"✅ 使用项目临时目录: {project_temp_dir}")
                return project_temp_dir

            except Exception as e2:
                self.logger.warning(f"⚠️ 项目临时目录不可用: {e2}")

                # 最后备选方案: 系统临时目录
                temp_dir = Path(tempfile.mkdtemp(prefix=f"chrome_account_{account_id}_"))
                self.logger.warning(f"🔄 使用系统临时目录: {temp_dir}")
                return temp_dir

    async def _inject_stealth_scripts(self, driver_wrapper: SeleniumDriverWrapper):
        """
        注入反检测脚本

        Args:
            driver_wrapper: WebDriver包装器
        """
        try:
            # 注入增强反检测脚本
            stealth_script = """
            // 隐藏webdriver属性
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });

            // 删除webdriver相关属性
            delete navigator.__proto__.webdriver;

            // 修改plugins
            Object.defineProperty(navigator, 'plugins', {
                get: () => [
                    {
                        0: {type: "application/x-google-chrome-pdf", suffixes: "pdf", description: "Portable Document Format"},
                        description: "Portable Document Format",
                        filename: "internal-pdf-viewer",
                        length: 1,
                        name: "Chrome PDF Plugin"
                    },
                    {
                        0: {type: "application/pdf", suffixes: "pdf", description: "Portable Document Format"},
                        description: "Portable Document Format",
                        filename: "mhjfbmdgcfjbbpaeojofohoefgiehjai",
                        length: 1,
                        name: "Chrome PDF Viewer"
                    }
                ],
            });

            // 修改languages
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en'],
            });

            // 隐藏自动化相关属性
            window.chrome = {
                runtime: {},
                loadTimes: function() {},
                csi: function() {},
                app: {}
            };

            // 修改权限查询
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );

            // 隐藏自动化检测
            Object.defineProperty(window, 'outerHeight', {
                get: function() {
                    return window.innerHeight;
                }
            });

            Object.defineProperty(window, 'outerWidth', {
                get: function() {
                    return window.innerWidth;
                }
            });

            // 修改User-Agent相关检测
            Object.defineProperty(navigator, 'platform', {
                get: () => 'Win32',
            });

            // 隐藏Selenium特征
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;

            // 修改toString方法
            window.navigator.chrome = window.chrome;
            window.navigator.chrome.runtime = {
                onConnect: undefined,
                onMessage: undefined,
                sendMessage: undefined,
            };

            // 伪造battery API
            Object.defineProperty(navigator, 'getBattery', {
                get: () => () => Promise.resolve({
                    charging: true,
                    chargingTime: 0,
                    dischargingTime: Infinity,
                    level: Math.random()
                }),
            });

            // 修改iframe检测
            Object.defineProperty(HTMLIFrameElement.prototype, 'contentWindow', {
                get: function() {
                    return window;
                }
            });

            // 隐藏headless特征
            Object.defineProperty(navigator, 'hardwareConcurrency', {
                get: () => 4,
            });

            Object.defineProperty(navigator, 'deviceMemory', {
                get: () => 8,
            });

            // 修改Date对象以避免时区检测
            const originalDate = Date;
            Date = class extends originalDate {
                constructor(...args) {
                    if (args.length === 0) {
                        super();
                    } else {
                        super(...args);
                    }
                }

                getTimezoneOffset() {
                    return 300; // EST timezone
                }
            };

            Date.now = originalDate.now;
            Date.parse = originalDate.parse;
            Date.UTC = originalDate.UTC;

            // 修改screen对象
            Object.defineProperty(screen, 'availHeight', {
                get: () => screen.height,
            });

            Object.defineProperty(screen, 'availWidth', {
                get: () => screen.width,
            });

            // 隐藏Selenium自动化特征
            Object.defineProperty(document, '$cdc_asdjflasutopfhvcZLmcfl_', {
                get: () => undefined,
            });

            Object.defineProperty(document, '$chrome_asyncScriptInfo', {
                get: () => undefined,
            });
            """

            # 执行反检测脚本
            await driver_wrapper.execute_script(stealth_script)

        except Exception as e:
            self.logger.warning(f"注入反检测脚本失败: {e}")

    async def _set_cookies(self, driver_wrapper: SeleniumDriverWrapper, cookies_data: str):
        """
        设置cookies

        Args:
            driver_wrapper: WebDriver包装器
            cookies_data: cookies数据
        """
        try:
            cookies = json.loads(cookies_data)
            if isinstance(cookies, list) and cookies:
                # 先访问域名以设置cookies
                await driver_wrapper.get("https://x.com")

                for cookie in cookies:
                    if isinstance(cookie, dict) and 'name' in cookie and 'value' in cookie:
                        cookie_dict = {
                            'name': cookie['name'],
                            'value': cookie['value'],
                            'domain': cookie.get('domain', '.x.com'),
                            'path': cookie.get('path', '/'),
                        }
                        if 'expiry' in cookie:
                            cookie_dict['expiry'] = cookie['expiry']

                        await driver_wrapper.execute_async(
                            driver_wrapper.driver.add_cookie, cookie_dict
                        )

                self.logger.debug("已设置cookies")

        except Exception as e:
            self.logger.warning(f"设置cookies失败: {e}")
    
    async def _cleanup_oldest_driver(self):
        """清理最旧的WebDriver"""
        if not self.drivers:
            return

        # 简单实现：清理第一个
        oldest_account_id = next(iter(self.drivers))
        await self._cleanup_account_driver(oldest_account_id)

    async def _cleanup_account_driver(self, account_id: int):
        """
        清理指定账号的WebDriver

        Args:
            account_id: 账号ID
        """
        try:
            if account_id in self.drivers:
                driver_wrapper = self.drivers[account_id]
                await driver_wrapper.close()
                del self.drivers[account_id]

            # 🔒 清除进程保护
            if account_id in self.protected_processes:
                del self.protected_processes[account_id]

            # 🚀 释放端口
            await self.port_manager.release_port(account_id)

            self.logger.debug(f"清理账号WebDriver: {account_id}")

        except Exception as e:
            self.logger.warning(f"清理账号WebDriver失败: {e}")

    async def release_driver(self, account_id: int):
        """
        释放指定账号的WebDriver - 为并发执行器提供的接口

        Args:
            account_id: 账号ID
        """
        await self._cleanup_account_driver(account_id)

    def _cleanup_chrome_ports(self):
        """安全清理程序Chrome端口（保护个人浏览器）"""
        try:
            import psutil
            import socket

            # 检查程序使用的Chrome调试端口
            program_ports = list(range(9000, 10000))  # 程序端口范围

            for port in program_ports:
                try:
                    # 检查端口是否被占用
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(1)
                    result = sock.connect_ex(('127.0.0.1', port))
                    sock.close()

                    if result == 0:  # 端口被占用
                        self.logger.debug(f"发现占用程序端口 {port}，尝试清理...")

                        # 查找占用端口的进程
                        for proc in psutil.process_iter(['pid', 'name', 'connections', 'cmdline']):
                            try:
                                for conn in proc.info['connections'] or []:
                                    if conn.laddr.port == port:
                                        if 'chrome' in proc.info['name'].lower():
                                            # 检查是否是程序启动的Chrome
                                            cmdline_str = ' '.join(proc.info['cmdline'] or [])
                                            program_indicators = [
                                                '--remote-debugging-port',
                                                'browser_profiles',
                                                'account_',
                                                '--user-data-dir'
                                            ]

                                            is_program_chrome = any(indicator in cmdline_str for indicator in program_indicators)

                                            if is_program_chrome:
                                                self.logger.debug(f"终止程序Chrome进程: PID {proc.info['pid']}, 端口 {port}")
                                                proc.terminate()
                                            else:
                                                self.logger.debug(f"保护个人Chrome进程: PID {proc.info['pid']}, 端口 {port}")
                                            break
                            except (psutil.NoSuchProcess, psutil.AccessDenied):
                                continue

                except Exception as e:
                    self.logger.debug(f"清理端口 {port} 时出错: {e}")
                    continue

        except ImportError:
            self.logger.debug("psutil未安装，跳过端口清理")
        except Exception as e:
            self.logger.debug(f"端口清理失败: {e}")
    
    async def close_all(self):
        """关闭所有WebDriver"""
        try:
            self.logger.info(f"开始关闭 {len(self.drivers)} 个WebDriver实例")

            # 并发关闭所有WebDriver
            if self.drivers:
                driver_tasks = []
                for account_id, driver_wrapper in self.drivers.items():
                    async def close_driver(dw, aid):
                        try:
                            # 检查WebDriver是否仍然有效
                            if await dw.is_session_valid():
                                await asyncio.wait_for(dw.close(), timeout=10.0)
                                self.logger.debug(f"WebDriver {aid} 已关闭")
                            else:
                                self.logger.debug(f"WebDriver {aid} 会话已无效，跳过关闭")
                        except asyncio.TimeoutError:
                            self.logger.warning(f"WebDriver {aid} 关闭超时")
                        except Exception as e:
                            # 如果是会话已关闭的错误，不记录为警告
                            error_msg = str(e).lower()
                            if "driver已关闭" in error_msg or "session" in error_msg:
                                self.logger.debug(f"WebDriver {aid} 已经关闭: {e}")
                            else:
                                self.logger.warning(f"关闭WebDriver {aid} 失败: {e}")

                    driver_tasks.append(close_driver(driver_wrapper, account_id))

                # 等待所有WebDriver关闭完成
                await asyncio.gather(*driver_tasks, return_exceptions=True)

            # 清理状态
            self.drivers.clear()

            self.logger.info("所有WebDriver已关闭")

        except Exception as e:
            self.logger.error(f"关闭WebDriver失败: {e}")
            # 强制清理状态
            self.drivers.clear()
    
    def reset_browser_pool(self):
        """重置浏览器池状态"""
        try:
            # 清理所有状态
            self.drivers.clear()
            self.logger.info("浏览器池状态已重置")
        except Exception as e:
            self.logger.error(f"重置浏览器池失败: {e}")

    async def get_driver_for_account(self, account: Account) -> Optional[SeleniumDriverWrapper]:
        """
        获取账号对应的WebDriver（兼容旧接口）

        Args:
            account: 账号对象

        Returns:
            WebDriver包装器
        """
        for attempt in range(3):
            try:
                driver_wrapper = await self.get_driver(account)
                if not driver_wrapper:
                    self.logger.error(f"无法获取WebDriver: {account.username}")
                    return None

                self.logger.debug(f"成功获取WebDriver: {account.username}")
                return driver_wrapper

            except Exception as e:
                self.logger.warning(f"获取WebDriver失败 (尝试 {attempt + 1}/3): {e}")
                if attempt < 2:  # 不是最后一次尝试
                    # 清理可能损坏的驱动
                    await self._cleanup_account_driver(account.id)
                    await asyncio.sleep(2)  # 等待2秒后重试
                continue

        self.logger.error(f"获取WebDriver最终失败: {account.username}")
        return None
    
    def get_driver_count(self) -> int:
        """获取当前WebDriver数量"""
        return len(self.drivers)

    def get_browser_count(self) -> int:
        """获取当前浏览器数量（兼容旧接口）"""
        return len(self.drivers)

    async def cleanup_inactive_drivers(self, inactive_hours: int = 2):
        """
        清理不活跃的WebDriver

        Args:
            inactive_hours: 不活跃小时数
        """
        try:
            cutoff_time = datetime.now() - timedelta(hours=inactive_hours)

            # 这里需要根据实际情况实现清理逻辑
            # 可以基于账号的最后活跃时间来判断

            self.logger.info("清理不活跃WebDriver完成")

        except Exception as e:
            self.logger.error(f"清理不活跃WebDriver失败: {e}")

    async def cleanup_inactive_browsers(self, inactive_hours: int = 2):
        """清理不活跃的浏览器（兼容旧接口）"""
        await self.cleanup_inactive_drivers(inactive_hours)


# 全局浏览器池实例
_browser_pool = None


def get_browser_pool() -> BrowserPool:
    """获取全局浏览器池实例"""
    global _browser_pool
    if _browser_pool is None:
        _browser_pool = BrowserPool()
    return _browser_pool
