#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复系统中的高优先级问题
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def fix_thread_pool_leak():
    """修复线程池资源泄漏问题"""
    print("🔧 修复线程池资源泄漏问题...")
    
    # 读取当前文件
    file_path = "src/modules/interaction/concurrent_executor.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经修复
        if "_thread_pool" in content:
            print("  ✅ 线程池资源泄漏已修复")
            return True
        
        # 添加共享线程池
        init_method_start = content.find("def __init__(self):")
        if init_method_start == -1:
            print("  ❌ 找不到__init__方法")
            return False
        
        # 找到__init__方法的结束位置
        init_end = content.find("\n    def ", init_method_start + 1)
        if init_end == -1:
            init_end = len(content)
        
        # 在__init__方法中添加线程池
        init_content = content[init_method_start:init_end]
        if "# 错误统计" in init_content:
            insert_pos = content.find("# 错误统计", init_method_start)
            new_content = (
                content[:insert_pos] +
                "        # 🚀 共享线程池 - 避免资源泄漏\n" +
                "        self._thread_pool = concurrent.futures.ThreadPoolExecutor(max_workers=5)\n" +
                "        self._recovery_locks = {}  # account_id -> asyncio.Lock\n\n" +
                "        " +
                content[insert_pos:]
            )
            
            # 添加清理方法
            cleanup_method = '''
    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口 - 清理资源"""
        try:
            if hasattr(self, '_thread_pool'):
                self._thread_pool.shutdown(wait=True)
                self.logger.debug("线程池已清理")
        except Exception as e:
            self.logger.warning(f"清理线程池失败: {e}")
'''
            
            # 在类的最后添加清理方法
            class_end = new_content.rfind("\n    def ")
            next_method_end = new_content.find("\n\n", class_end)
            if next_method_end == -1:
                next_method_end = len(new_content)
            
            final_content = (
                new_content[:next_method_end] +
                cleanup_method +
                new_content[next_method_end:]
            )
            
            # 修复线程池使用
            final_content = final_content.replace(
                "with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:",
                "# 使用共享线程池避免资源泄漏"
            )
            
            final_content = final_content.replace(
                "future = executor.submit(run_in_thread)",
                "future = self._thread_pool.submit(run_in_thread)"
            )
            
            # 写入文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(final_content)
            
            print("  ✅ 线程池资源泄漏修复完成")
            return True
        else:
            print("  ❌ 找不到合适的插入位置")
            return False
            
    except Exception as e:
        print(f"  ❌ 修复线程池资源泄漏失败: {e}")
        return False

def fix_event_loop_leak():
    """修复事件循环泄漏问题"""
    print("🔧 修复事件循环泄漏问题...")
    
    file_path = "src/modules/interaction/concurrent_executor.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经修复
        if "asyncio.set_event_loop(None)" in content:
            print("  ✅ 事件循环泄漏已修复")
            return True
        
        # 找到run_in_thread函数
        run_in_thread_start = content.find("def run_in_thread():")
        if run_in_thread_start == -1:
            print("  ❌ 找不到run_in_thread函数")
            return False
        
        # 找到finally块
        finally_start = content.find("finally:", run_in_thread_start)
        if finally_start == -1:
            print("  ❌ 找不到finally块")
            return False
        
        # 找到finally块的结束
        finally_end = content.find("\n                except", finally_start)
        if finally_end == -1:
            finally_end = content.find("\n            # 在线程池中执行", finally_start)
        
        if finally_end == -1:
            print("  ❌ 找不到finally块结束位置")
            return False
        
        # 替换finally块内容
        new_finally_content = '''finally:
                    try:
                        # 取消所有未完成的任务
                        pending = asyncio.all_tasks(loop)
                        for task in pending:
                            task.cancel()
                        # 等待任务取消完成
                        if pending:
                            loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
                    except Exception as e:
                        self.logger.debug(f"清理事件循环任务失败: {e}")
                    finally:
                        loop.close()
                        asyncio.set_event_loop(None)'''
        
        new_content = (
            content[:finally_start] +
            new_finally_content +
            content[finally_end:]
        )
        
        # 写入文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print("  ✅ 事件循环泄漏修复完成")
        return True
        
    except Exception as e:
        print(f"  ❌ 修复事件循环泄漏失败: {e}")
        return False

def fix_browser_session_deadlock():
    """修复浏览器会话恢复死锁问题"""
    print("🔧 修复浏览器会话恢复死锁问题...")
    
    file_path = "src/modules/interaction/concurrent_executor.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经修复
        if "_recovery_locks" in content and "async with self._recovery_locks" in content:
            print("  ✅ 浏览器会话恢复死锁已修复")
            return True
        
        # 找到_recover_browser_session方法
        method_start = content.find("async def _recover_browser_session(self, account):")
        if method_start == -1:
            print("  ❌ 找不到_recover_browser_session方法")
            return False
        
        # 找到方法体开始
        method_body_start = content.find('"""', method_start)
        if method_body_start != -1:
            method_body_start = content.find('"""', method_body_start + 3) + 3
        else:
            method_body_start = content.find(":", method_start) + 1
        
        # 找到方法结束
        method_end = content.find("\n    async def ", method_start + 1)
        if method_end == -1:
            method_end = content.find("\n    def ", method_start + 1)
        if method_end == -1:
            method_end = len(content)
        
        # 创建新的方法体
        new_method_body = '''
        """恢复浏览器会话 - 带死锁保护"""
        try:
            # 使用账号级别的锁避免死锁
            if account.id not in self._recovery_locks:
                self._recovery_locks[account.id] = asyncio.Lock()
            
            async with self._recovery_locks[account.id]:
                self.logger.info(f"🔄 恢复浏览器会话: {account.username}")
                
                # 清理当前会话
                await self.browser_pool.release_driver(account.id)
                
                # 等待一段时间让资源完全释放
                await asyncio.sleep(1)
                
                # 重新获取WebDriver
                driver_wrapper = await self.browser_pool.get_driver(account)
                if driver_wrapper:
                    self.logger.info(f"✅ 浏览器会话恢复成功: {account.username}")
                    return True
                else:
                    self.logger.warning(f"❌ 浏览器会话恢复失败: {account.username}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"恢复浏览器会话异常: {account.username}, {e}")
            return False'''
        
        new_content = (
            content[:method_body_start] +
            new_method_body +
            content[method_end:]
        )
        
        # 写入文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print("  ✅ 浏览器会话恢复死锁修复完成")
        return True
        
    except Exception as e:
        print(f"  ❌ 修复浏览器会话恢复死锁失败: {e}")
        return False

def main():
    """主修复函数"""
    print("🚀 开始修复系统高优先级问题")
    print("=" * 50)
    
    results = []
    
    # 修复1: 线程池资源泄漏
    results.append(fix_thread_pool_leak())
    
    # 修复2: 事件循环泄漏
    results.append(fix_event_loop_leak())
    
    # 修复3: 浏览器会话恢复死锁
    results.append(fix_browser_session_deadlock())
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 修复结果总结")
    print("=" * 50)
    
    success_count = sum(results)
    total_count = len(results)
    
    print(f"  修复成功: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("\n🎉 所有高优先级问题修复完成！")
        print("\n✅ 修复内容:")
        print("  - ✅ 线程池资源泄漏")
        print("  - ✅ 事件循环泄漏")
        print("  - ✅ 浏览器会话恢复死锁")
        print("\n📋 建议:")
        print("  - 重启应用程序以应用修复")
        print("  - 监控系统资源使用情况")
        print("  - 继续修复中等优先级问题")
    else:
        print("\n⚠️ 部分问题修复失败")
        print("  - 请检查文件权限")
        print("  - 手动应用修复建议")
        print("  - 联系技术支持")
    
    return success_count == total_count

if __name__ == "__main__":
    try:
        result = main()
        exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断修复")
        exit(1)
    except Exception as e:
        print(f"\n❌ 修复过程异常: {e}")
        exit(1)
