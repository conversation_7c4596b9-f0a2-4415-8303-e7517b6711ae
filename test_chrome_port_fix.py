#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Chrome端口冲突修复
"""

import asyncio
import sys
import os
import time
from dataclasses import dataclass

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

@dataclass
class MockAccount:
    """完整的模拟账号类"""
    id: int
    username: str
    password: str = "test_password"
    email: str = "<EMAIL>"
    proxy: str = None
    user_agent: str = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    cookies: str = None
    cookies_dict: dict = None
    token: str = None
    status: str = "active"
    group_id: int = 1
    created_at: str = "2024-01-01"
    updated_at: str = "2024-01-01"

async def test_port_manager():
    """测试端口管理器"""
    try:
        from src.core.browser_manager import ChromePortManager
        
        print("🔧 测试端口管理器...")
        
        port_manager = ChromePortManager()
        
        # 测试端口分配
        print("1. 测试端口分配...")
        port1 = await port_manager.allocate_port(1)
        port2 = await port_manager.allocate_port(2)
        port3 = await port_manager.allocate_port(3)
        
        print(f"  账号1端口: {port1}")
        print(f"  账号2端口: {port2}")
        print(f"  账号3端口: {port3}")
        
        # 验证端口不重复
        if len({port1, port2, port3}) == 3:
            print("  ✅ 端口分配无冲突")
        else:
            print("  ❌ 端口分配有冲突")
            return False
        
        # 测试端口释放
        print("2. 测试端口释放...")
        await port_manager.release_port(2)
        
        # 重新分配应该得到相同端口
        port2_new = await port_manager.allocate_port(2)
        print(f"  账号2重新分配端口: {port2_new}")
        
        return True
        
    except Exception as e:
        print(f"❌ 端口管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_concurrent_port_allocation():
    """测试并发端口分配"""
    try:
        from src.core.browser_manager import ChromePortManager
        
        print("\n🚀 测试并发端口分配...")
        
        port_manager = ChromePortManager()
        
        # 模拟5个账号同时请求端口
        async def allocate_port_for_account(account_id):
            port = await port_manager.allocate_port(account_id)
            print(f"  账号{account_id}获得端口: {port}")
            return port
        
        # 并发分配端口
        tasks = [allocate_port_for_account(i) for i in range(1, 6)]
        ports = await asyncio.gather(*tasks)
        
        # 验证所有端口都不同
        if len(set(ports)) == len(ports):
            print("  ✅ 并发端口分配无冲突")
            print(f"  分配的端口: {ports}")
            return True
        else:
            print("  ❌ 并发端口分配有冲突")
            print(f"  分配的端口: {ports}")
            return False
        
    except Exception as e:
        print(f"❌ 并发端口分配测试失败: {e}")
        return False

async def test_browser_pool_startup_control():
    """测试浏览器池启动控制"""
    try:
        from src.core.browser_manager import BrowserPool
        
        print("\n🔧 测试浏览器池启动控制...")
        
        # 创建浏览器池
        browser_pool = BrowserPool()
        
        # 测试端口管理器
        print("1. 测试端口管理器集成...")
        port1 = await browser_pool.port_manager.allocate_port(1)
        port2 = await browser_pool.port_manager.allocate_port(2)
        
        print(f"  浏览器池分配端口1: {port1}")
        print(f"  浏览器池分配端口2: {port2}")
        
        if port1 != port2:
            print("  ✅ 浏览器池端口管理正常")
        else:
            print("  ❌ 浏览器池端口管理有问题")
            return False
        
        # 测试启动信号量
        print("2. 测试启动信号量...")
        semaphore_count = browser_pool.startup_semaphore._value
        print(f"  启动信号量计数: {semaphore_count}")
        
        if semaphore_count == 2:
            print("  ✅ 启动信号量配置正确")
        else:
            print("  ❌ 启动信号量配置错误")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 浏览器池测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🚀 测试Chrome端口冲突修复")
    print("=" * 50)
    
    try:
        # 测试1: 端口管理器
        test1_result = await test_port_manager()
        
        # 测试2: 并发端口分配
        test2_result = await test_concurrent_port_allocation()
        
        # 测试3: 浏览器池集成
        test3_result = await test_browser_pool_startup_control()
        
        # 总结
        print("\n" + "=" * 50)
        print("📊 测试结果总结")
        print("=" * 50)
        print(f"  端口管理器测试: {'✅ 通过' if test1_result else '❌ 失败'}")
        print(f"  并发端口分配测试: {'✅ 通过' if test2_result else '❌ 失败'}")
        print(f"  浏览器池集成测试: {'✅ 通过' if test3_result else '❌ 失败'}")
        
        overall_success = test1_result and test2_result and test3_result
        
        if overall_success:
            print("\n🎉 Chrome端口冲突修复成功！")
            print("\n✅ 修复内容:")
            print("  - ✅ 实现了专用端口管理器")
            print("  - ✅ 每个账号分配独立端口")
            print("  - ✅ 并发端口分配无冲突")
            print("  - ✅ 启动信号量控制并发数量")
            print("  - ✅ 端口自动释放和重用")
            print("\n📋 解决的问题:")
            print("  - 🔧 端口冲突：每个账号独立端口")
            print("  - 🔧 并发冲突：信号量控制启动数量")
            print("  - 🔧 资源泄漏：自动端口释放")
            print("  - 🔧 用户数据目录：已有独立目录")
        else:
            print("\n❌ 部分测试未通过，需要进一步调试")
        
        return overall_success
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        return False

if __name__ == "__main__":
    # 运行测试
    result = asyncio.run(main())
    print(f"\n🏁 测试完成，结果: {'成功' if result else '失败'}")
    exit(0 if result else 1)
