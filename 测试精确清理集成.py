#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试精确清理功能集成效果
"""

import asyncio
import sys
import os
from dataclasses import dataclass

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

@dataclass
class MockAccount:
    """完整的模拟账号类"""
    id: int
    username: str
    password: str = "test_password"
    email: str = "<EMAIL>"
    proxy: str = None
    user_agent: str = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    cookies: str = None
    cookies_dict: dict = None
    token: str = None
    status: str = "active"
    group_id: int = 1
    created_at: str = "2024-01-01"
    updated_at: str = "2024-01-01"

async def test_chrome_process_identifier():
    """测试Chrome进程识别器"""
    print("🔧 测试Chrome进程精确识别器...")
    
    try:
        from src.core.browser_manager import ChromeProcessIdentifier
        
        # 测试用例
        test_cases = [
            {
                'name': '程序Chrome - 端口特征',
                'cmdline': ['chrome.exe', '--remote-debugging-port=9001', '--user-data-dir=D:/project/data/browser_profiles/account_1'],
                'expected': 'program'
            },
            {
                'name': '程序Chrome - 目录特征',
                'cmdline': ['chrome.exe', '--user-data-dir=D:/project/data/browser_profiles/account_2'],
                'expected': 'program'
            },
            {
                'name': '个人Chrome',
                'cmdline': ['chrome.exe', '--user-data-dir=C:/Users/<USER>/AppData/Local/Google/Chrome/User Data', '--restore-last-session'],
                'expected': 'personal'
            }
        ]
        
        project_path = os.getcwd()
        success_count = 0
        
        for test_case in test_cases:
            cmdline = test_case['cmdline']
            expected = test_case['expected']
            
            is_program = ChromeProcessIdentifier.is_program_chrome(cmdline, project_path)
            is_personal = ChromeProcessIdentifier.is_personal_chrome(cmdline)
            
            if expected == 'program' and is_program:
                result = "✅ 正确"
                success_count += 1
            elif expected == 'personal' and is_personal:
                result = "✅ 正确"
                success_count += 1
            else:
                result = "❌ 错误"
            
            print(f"  {test_case['name']}: {result}")
        
        print(f"\n📊 识别准确率: {success_count}/{len(test_cases)} ({success_count/len(test_cases)*100:.1f}%)")
        return success_count == len(test_cases)
        
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_browser_pool_integration():
    """测试浏览器池集成"""
    print("\n🔧 测试浏览器池精确清理集成...")
    
    try:
        from src.core.browser_manager import BrowserPool
        
        # 创建浏览器池
        browser_pool = BrowserPool()
        
        # 测试精确清理方法是否存在
        print("1. 检查精确清理方法集成...")
        
        # 检查ChromeProcessIdentifier是否可用
        from src.core.browser_manager import ChromeProcessIdentifier
        print("  ✅ ChromeProcessIdentifier已集成")
        
        # 测试清理方法
        print("2. 测试安全清理方法...")
        
        # 模拟清理（不会实际清理，因为没有Chrome进程）
        try:
            browser_pool._cleanup_chrome_processes_for_account(999)  # 使用不存在的账号ID
            print("  ✅ 安全清理方法运行正常")
        except Exception as e:
            print(f"  ❌ 安全清理方法异常: {e}")
            return False
        
        # 测试端口管理器
        print("3. 测试端口管理器...")
        port1 = await browser_pool.port_manager.allocate_port(1)
        port2 = await browser_pool.port_manager.allocate_port(2)
        
        print(f"  分配端口1: {port1}")
        print(f"  分配端口2: {port2}")
        
        if port1 != port2 and 9000 <= port1 <= 9999 and 9000 <= port2 <= 9999:
            print("  ✅ 端口管理器正常")
            return True
        else:
            print("  ❌ 端口管理器异常")
            return False
            
    except Exception as e:
        print(f"  ❌ 浏览器池集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_safe_chrome_cleanup():
    """测试安全Chrome清理"""
    print("\n🔧 测试安全Chrome清理功能...")
    
    try:
        # 运行精确清理工具
        print("1. 运行精确清理分析...")
        
        import subprocess
        result = subprocess.run([
            'python', '精确Chrome清理工具.py'
        ], capture_output=True, text=True, input='n\n')  # 自动回答'n'不清理
        
        if result.returncode == 0:
            print("  ✅ 精确清理工具运行正常")
            
            # 检查输出中是否包含特征识别
            output = result.stdout
            if '基于端口和目录特征' in output and '特征识别准确性' in output:
                print("  ✅ 特征识别功能正常")
                return True
            else:
                print("  ⚠️  特征识别功能可能有问题")
                return False
        else:
            print(f"  ❌ 精确清理工具运行失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"  ❌ 安全清理测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🎯 测试精确清理功能集成效果")
    print("=" * 60)
    
    test_results = []
    
    # 测试1: Chrome进程识别器
    test_results.append(await test_chrome_process_identifier())
    
    # 测试2: 浏览器池集成
    test_results.append(await test_browser_pool_integration())
    
    # 测试3: 安全Chrome清理
    test_results.append(await test_safe_chrome_cleanup())
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 精确清理集成测试总结")
    print("=" * 60)
    
    test_names = [
        "Chrome进程精确识别",
        "浏览器池集成",
        "安全Chrome清理"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {name}: {status}")
    
    success_count = sum(test_results)
    total_count = len(test_results)
    
    print(f"\n📈 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        print("\n🎉 精确清理功能集成成功！")
        print("\n✅ 集成验证:")
        print("  - ✅ 基于端口特征(9000-9999)精确识别程序Chrome")
        print("  - ✅ 基于目录特征(browser_profiles)精确识别程序Chrome")
        print("  - ✅ 个人Chrome完全受保护，不会被误清理")
        print("  - ✅ 浏览器池集成精确清理逻辑")
        print("  - ✅ 端口管理器正常工作")
        print("\n🛡️ 安全保障:")
        print("  - 您的个人Chrome浏览器现在完全安全")
        print("  - 程序只会清理自己启动的Chrome进程")
        print("  - 基于多重特征识别，准确率极高")
        print("\n📋 使用建议:")
        print("  - 现在可以放心使用互动功能")
        print("  - 不用担心个人浏览器被影响")
        print("  - 系统会自动保护个人Chrome进程")
    else:
        print("\n⚠️ 部分集成测试未通过")
        print("  - 建议检查失败的测试项")
        print("  - 可能需要进一步调试")
    
    return success_count >= total_count * 0.8

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        print(f"\n🏁 测试完成，结果: {'成功' if result else '需要改进'}")
        exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断测试")
        exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程异常: {e}")
        import traceback
        traceback.print_exc()
        exit(1)
