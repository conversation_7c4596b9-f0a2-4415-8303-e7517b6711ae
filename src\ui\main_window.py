#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口模块
"""

import sys
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTabWidget, QMenuBar, QStatusBar, QToolBar,
    QMessageBox, QSplitter, QTextEdit, QDialog,
    QLabel, QPushButton, QProgressBar
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QIcon, QFont, QAction

from src.config.settings import Settings
from src.utils.logger import LoggerMixin
from src.ui.widgets.account_widget import AccountWidget
from src.ui.widgets.proxy_widget import ProxyWidget
from src.ui.widgets.posting_widget import PostingWidget
from src.ui.widgets.interaction_widget import InteractionWidget
from src.ui.widgets.log_widget import LogWidget


class MainWindow(QMainWindow, LoggerMixin):
    """主窗口类"""
    
    # 信号定义
    status_updated = pyqtSignal(str)
    progress_updated = pyqtSignal(int)
    
    def __init__(self, settings: Settings):
        super().__init__()
        print("DEBUG: MainWindow.__init__ 开始")
        self.settings = settings
        print("DEBUG: MainWindow 基础属性设置完成")
        
        # 初始化UI
        self.init_ui()
        print("DEBUG: MainWindow.init_ui 完成")
        
        # 设置定时器更新状态
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(5000)  # 每5秒更新一次
        
        self.logger.info("主窗口初始化完成")
    
    def init_ui(self):
        """初始化用户界面"""
        print("DEBUG: init_ui 开始")
        # 设置窗口属性
        self.setWindowTitle(f"{self.settings.app_name} v{self.settings.app_version}")
        self.setGeometry(100, 100, 1200, 800)
        self.setMinimumSize(1000, 600)
        print("DEBUG: 窗口属性设置完成")
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        print("DEBUG: 中央部件创建完成")
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        print("DEBUG: 主布局创建完成")
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Vertical)
        main_layout.addWidget(splitter)
        print("DEBUG: 分割器创建完成")
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        splitter.addWidget(self.tab_widget)
        print("DEBUG: 标签页容器创建完成")
        
        # 创建各个功能页面
        print("DEBUG: 开始创建各个功能页面")
        self.create_tabs()
        print("DEBUG: 各个功能页面创建完成")
        
        # 创建日志区域
        print("DEBUG: 开始创建日志区域")
        self.log_widget = LogWidget()
        splitter.addWidget(self.log_widget)
        print("DEBUG: 日志区域创建完成")
        
        # 设置分割器比例
        splitter.setSizes([600, 200])
        print("DEBUG: 分割器比例设置完成")
        
        # 创建菜单栏
        print("DEBUG: 开始创建菜单栏")
        self.create_menu_bar()
        print("DEBUG: 菜单栏创建完成")
        
        # 创建工具栏
        print("DEBUG: 开始创建工具栏")
        self.create_tool_bar()
        print("DEBUG: 工具栏创建完成")
        
        # 创建状态栏
        print("DEBUG: 开始创建状态栏")
        self.create_status_bar()
        print("DEBUG: 状态栏创建完成")
        
        # 连接信号
        print("DEBUG: 开始连接信号")
        self.connect_signals()
        print("DEBUG: 信号连接完成")
    
    def create_tabs(self):
        """创建标签页"""
        try:
            # 账号管理页面
            print("DEBUG: 开始创建账号管理标签页")
            self.account_widget = AccountWidget()
            self.tab_widget.addTab(self.account_widget, "账号管理")
            print("DEBUG: 账号管理标签页创建完成")
            
            # 代理管理页面
            print("DEBUG: 开始创建代理管理标签页")
            self.proxy_widget = ProxyWidget()
            self.tab_widget.addTab(self.proxy_widget, "代理管理")
            print("DEBUG: 代理管理标签页创建完成")
            
            # 发帖管理页面
            print("DEBUG: 开始创建发帖管理标签页")
            self.posting_widget = PostingWidget()
            self.tab_widget.addTab(self.posting_widget, "发帖管理")
            print("DEBUG: 发帖管理标签页创建完成")
            
            # 互动管理页面
            print("DEBUG: 开始创建互动管理标签页")
            self.interaction_widget = InteractionWidget()
            self.tab_widget.addTab(self.interaction_widget, "互动管理")
            print("DEBUG: 互动管理标签页创建完成")
            
        except Exception as e:
            print(f"DEBUG: 创建标签页时发生错误: {e}")
            import traceback
            traceback.print_exc()
            self.logger.error(f"创建标签页失败: {e}")
            QMessageBox.critical(self, "错误", f"创建标签页失败: {e}")
    
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件')
        
        # 导入账号
        import_accounts_action = QAction('导入账号', self)
        import_accounts_action.setShortcut('Ctrl+I')
        import_accounts_action.triggered.connect(self.import_accounts)
        file_menu.addAction(import_accounts_action)
        
        # 导入代理
        import_proxies_action = QAction('导入代理', self)
        import_proxies_action.triggered.connect(self.import_proxies)
        file_menu.addAction(import_proxies_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction('退出', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 工具菜单
        tools_menu = menubar.addMenu('工具')
        
        # 变量管理
        variable_manager_action = QAction('变量管理', self)
        variable_manager_action.triggered.connect(self.open_variable_manager)
        tools_menu.addAction(variable_manager_action)
        
        tools_menu.addSeparator()
        
        # 测试代理
        test_proxies_action = QAction('测试代理', self)
        test_proxies_action.triggered.connect(self.test_proxies)
        tools_menu.addAction(test_proxies_action)
        
        # 清理日志
        clear_logs_action = QAction('清理日志', self)
        clear_logs_action.triggered.connect(self.clear_logs)
        tools_menu.addAction(clear_logs_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu('帮助')
        
        # 关于
        about_action = QAction('关于', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def create_tool_bar(self):
        """创建工具栏"""
        toolbar = QToolBar()
        self.addToolBar(toolbar)
        
        # 开始发帖按钮
        start_posting_action = QAction('开始发帖', self)
        start_posting_action.triggered.connect(self.start_posting)
        toolbar.addAction(start_posting_action)
        
        # 停止任务按钮
        stop_tasks_action = QAction('停止任务', self)
        stop_tasks_action.triggered.connect(self.stop_tasks)
        toolbar.addAction(stop_tasks_action)
        
        toolbar.addSeparator()
        
        # 刷新按钮
        refresh_action = QAction('刷新', self)
        refresh_action.setShortcut('F5')
        refresh_action.triggered.connect(self.refresh_data)
        toolbar.addAction(refresh_action)
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_bar.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)
        
        # 统计信息
        self.stats_label = QLabel("账号: 0 | 代理: 0 | 任务: 0")
        self.status_bar.addPermanentWidget(self.stats_label)
    
    def connect_signals(self):
        """连接信号"""
        # 连接状态更新信号
        self.status_updated.connect(self.status_label.setText)
        self.progress_updated.connect(self.update_progress)
        
        # 连接各个组件的信号
        if hasattr(self, 'account_widget'):
            self.account_widget.status_updated.connect(self.status_updated.emit)
        
        if hasattr(self, 'proxy_widget'):
            self.proxy_widget.status_updated.connect(self.status_updated.emit)
        
        if hasattr(self, 'posting_widget'):
            self.posting_widget.status_updated.connect(self.status_updated.emit)
            self.posting_widget.progress_updated.connect(self.progress_updated.emit)
        
        if hasattr(self, 'interaction_widget'):
            self.interaction_widget.status_updated.connect(self.status_updated.emit)
    
    def update_status(self):
        """更新状态信息"""
        try:
            # 获取统计信息
            from src.core.account_manager import AccountManager
            from src.core.proxy_manager import ProxyManager
            from src.core.task_scheduler import get_task_scheduler
            
            account_manager = AccountManager()
            proxy_manager = ProxyManager()
            task_scheduler = get_task_scheduler()
            
            # 获取账号数量
            accounts, account_count = account_manager.get_accounts()
            
            # 获取代理数量
            proxies, proxy_count = proxy_manager.get_proxies()
            
            # 获取任务统计
            task_stats = task_scheduler.get_statistics()
            
            # 更新状态栏
            stats_text = f"账号: {account_count} | 代理: {proxy_count} | 任务: {task_stats.get('pending_tasks', 0)}"
            self.stats_label.setText(stats_text)
            
        except Exception as e:
            self.logger.error(f"更新状态失败: {e}")
    
    def update_progress(self, value: int):
        """更新进度条"""
        if value >= 0:
            self.progress_bar.setValue(value)
            self.progress_bar.setVisible(True)
        else:
            self.progress_bar.setVisible(False)
    
    def import_accounts(self):
        """导入账号"""
        try:
            if hasattr(self, 'account_widget'):
                self.account_widget.import_accounts()
        except Exception as e:
            self.logger.error(f"导入账号失败: {e}")
            QMessageBox.critical(self, "错误", f"导入账号失败: {e}")
    
    def import_proxies(self):
        """导入代理"""
        try:
            if hasattr(self, 'proxy_widget'):
                self.proxy_widget.import_proxies()
        except Exception as e:
            self.logger.error(f"导入代理失败: {e}")
            QMessageBox.critical(self, "错误", f"导入代理失败: {e}")
    
    def test_proxies(self):
        """测试代理"""
        try:
            if hasattr(self, 'proxy_widget'):
                self.proxy_widget.test_proxies()
        except Exception as e:
            self.logger.error(f"测试代理失败: {e}")
            QMessageBox.critical(self, "错误", f"测试代理失败: {e}")
    
    def start_posting(self):
        """开始发帖"""
        try:
            if hasattr(self, 'posting_widget'):
                self.posting_widget.start_posting()
        except Exception as e:
            self.logger.error(f"开始发帖失败: {e}")
            QMessageBox.critical(self, "错误", f"开始发帖失败: {e}")
    
    def stop_tasks(self):
        """停止任务"""
        try:
            from src.core.task_scheduler import get_task_scheduler
            task_scheduler = get_task_scheduler()
            
            # 这里可以添加停止所有任务的逻辑
            self.status_updated.emit("正在停止任务...")
            
            QMessageBox.information(self, "信息", "任务停止请求已发送")
            
        except Exception as e:
            self.logger.error(f"停止任务失败: {e}")
            QMessageBox.critical(self, "错误", f"停止任务失败: {e}")
    
    def refresh_data(self):
        """刷新数据"""
        try:
            self.status_updated.emit("正在刷新数据...")
            
            # 刷新各个组件的数据
            if hasattr(self, 'account_widget'):
                self.account_widget.refresh_data()
            
            if hasattr(self, 'proxy_widget'):
                self.proxy_widget.refresh_data()
            
            if hasattr(self, 'posting_widget'):
                self.posting_widget.refresh_data()
            
            if hasattr(self, 'interaction_widget'):
                self.interaction_widget.refresh_data()
            
            self.status_updated.emit("数据刷新完成")
            
        except Exception as e:
            self.logger.error(f"刷新数据失败: {e}")
            QMessageBox.critical(self, "错误", f"刷新数据失败: {e}")
    

    
    def open_variable_manager(self):
        """打开变量管理对话框"""
        try:
            from simple_variables_dialog import SimpleVariablesDialog
            
            dialog = SimpleVariablesDialog(self)
            dialog.exec()
            
        except Exception as e:
            self.logger.error(f"打开变量管理失败: {e}")
            QMessageBox.critical(self, "错误", f"打开变量管理失败: {e}")
    
    def clear_logs(self):
        """清理日志"""
        try:
            if hasattr(self, 'log_widget'):
                self.log_widget.clear_logs()
            
            QMessageBox.information(self, "信息", "日志已清理")
            
        except Exception as e:
            self.logger.error(f"清理日志失败: {e}")
            QMessageBox.critical(self, "错误", f"清理日志失败: {e}")
    
    def show_about(self):
        """显示关于对话框"""
        about_text = f"""
        <h2>{self.settings.app_name}</h2>
        <p>版本: {self.settings.app_version}</p>
        <p>一个功能完善的X（原Twitter）自动化发帖程序</p>
        <p>支持账号批量管理、自动发帖、智能互动等功能</p>
        <br>
        <p>技术特点:</p>
        <ul>
        <li>不依赖X官方API</li>
        <li>基于浏览器自动化技术</li>
        <li>支持多线程并发操作</li>
        <li>完善的防封机制</li>
        </ul>
        """
        
        QMessageBox.about(self, "关于", about_text)
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            self.logger.info("开始程序退出流程...")
            
            # 停止定时器
            if hasattr(self, 'status_timer'):
                self.status_timer.stop()
            
            # 清理资源
            from src.core.browser_manager import get_browser_pool
            from src.core.task_scheduler import get_task_scheduler
            from src.core.system_monitor import get_system_monitor
            
            # 停止系统监控
            try:
                system_monitor = get_system_monitor()
                system_monitor.stop_monitoring()
                self.logger.debug("系统监控已停止")
            except Exception as e:
                self.logger.warning(f"停止系统监控失败: {e}")
            
            # 停止任务调度器
            try:
                task_scheduler = get_task_scheduler()
                task_scheduler.stop()
                self.logger.debug("任务调度器已停止")
            except Exception as e:
                self.logger.warning(f"停止任务调度器失败: {e}")

            # 🔧 停止浏览器稳定性监控器
            try:
                from src.core.browser_stability_monitor import stop_browser_monitoring
                stop_browser_monitoring()
                self.logger.debug("浏览器稳定性监控已停止")
            except Exception as e:
                self.logger.warning(f"停止浏览器稳定性监控失败: {e}")

            # 异步清理浏览器池
            try:
                browser_pool = get_browser_pool()
                self._cleanup_browser_pool_safely(browser_pool)
                self.logger.debug("浏览器池清理完成")
            except Exception as e:
                self.logger.warning(f"清理浏览器池失败: {e}")

            # 🔧 强制清理Chrome进程 - 防止进程残留
            try:
                self._force_cleanup_chrome_processes()
            except Exception as e:
                self.logger.warning(f"强制清理Chrome进程失败: {e}")

            self.logger.info("程序正常退出")
            event.accept()

            # 🔧 确保Qt应用程序完全退出
            from PyQt6.QtWidgets import QApplication
            QApplication.quit()

        except Exception as e:
            self.logger.error(f"程序退出异常: {e}")
            event.accept()

            # 🔧 即使异常也要退出Qt应用程序
            from PyQt6.QtWidgets import QApplication
            QApplication.quit()

    def _force_cleanup_chrome_processes(self):
        """强制清理Chrome进程 - 防止进程残留"""
        try:
            import psutil
            import os

            current_dir = os.getcwd()
            cleaned_count = 0

            self.logger.debug("开始强制清理Chrome进程...")

            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if 'chrome' in proc.info['name'].lower():
                        cmdline = ' '.join(proc.info['cmdline'] or [])

                        # 检查是否是程序启动的Chrome
                        if any(indicator in cmdline for indicator in [
                            'browser_profiles',
                            'account_',
                            'remote-debugging-port=9',
                            current_dir.replace('\\', '/')
                        ]):
                            proc.terminate()
                            self.logger.debug(f"强制清理Chrome进程: PID {proc.info['pid']}")
                            cleaned_count += 1

                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                except Exception as e:
                    self.logger.debug(f"清理Chrome进程失败: {e}")

            if cleaned_count > 0:
                self.logger.info(f"强制清理了 {cleaned_count} 个Chrome进程")
            else:
                self.logger.debug("没有发现需要清理的Chrome进程")

        except Exception as e:
            self.logger.warning(f"强制清理Chrome进程异常: {e}")

    def _cleanup_browser_pool_safely(self, browser_pool):
        """安全地清理浏览器池"""
        import asyncio
        import threading
        import time
        
        def cleanup_task():
            """在新线程中执行清理任务"""
            try:
                # 创建新的事件循环
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                # 设置超时时间
                async def cleanup_with_timeout():
                    try:
                        await asyncio.wait_for(browser_pool.close_all(), timeout=10.0)
                    except asyncio.TimeoutError:
                        self.logger.warning("浏览器池清理超时，强制关闭")
                    except Exception as e:
                        self.logger.warning(f"浏览器池清理异常: {e}")
                
                # 运行清理任务
                loop.run_until_complete(cleanup_with_timeout())
                
            except Exception as e:
                self.logger.warning(f"清理线程异常: {e}")
            finally:
                try:
                    # 确保事件循环正确关闭
                    if not loop.is_closed():
                        loop.close()
                except Exception:
                    pass
        
        # 在新线程中执行清理
        cleanup_thread = threading.Thread(target=cleanup_task, daemon=True)
        cleanup_thread.start()
        
        # 等待清理完成，但不超过5秒
        cleanup_thread.join(timeout=5.0)

        if cleanup_thread.is_alive():
            self.logger.warning("浏览器池清理线程未在预期时间内完成")
            # 🔧 强制终止清理线程相关的进程
            try:
                import psutil
                import os
                current_pid = os.getpid()
                for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                    try:
                        if 'chrome' in proc.info['name'].lower():
                            cmdline = ' '.join(proc.info['cmdline'] or [])
                            if any(indicator in cmdline for indicator in [
                                '--remote-debugging-port',
                                'browser_profiles',
                                '--disable-blink-features=AutomationControlled'
                            ]):
                                proc.terminate()
                                self.logger.debug(f"强制终止Chrome进程: PID {proc.info['pid']}")
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue
            except Exception as e:
                self.logger.warning(f"强制清理Chrome进程失败: {e}")
