#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于端口和目录特征的精确Chrome清理工具
"""

import psutil
import os
import re
from datetime import datetime

def is_program_chrome(process_cmdline, project_path=None):
    """判断是否是程序启动的Chrome - 基于端口和目录特征"""
    if not process_cmdline:
        return False
    
    cmdline_str = ' '.join(process_cmdline)
    
    # 特征1: 端口特征 - 程序使用9000-9999端口
    port_pattern = r'--remote-debugging-port=9\d{3}'
    has_program_port = re.search(port_pattern, cmdline_str)
    
    # 特征2: 目录特征 - 程序使用browser_profiles目录
    has_browser_profiles = 'browser_profiles' in cmdline_str
    
    # 特征3: 账号目录特征 - 包含account_前缀
    has_account_dir = 'account_' in cmdline_str
    
    # 特征4: 项目路径特征 - 包含项目目录
    if project_path:
        has_project_path = project_path.replace('\\', '/') in cmdline_str.replace('\\', '/')
    else:
        has_project_path = False
    
    # 特征5: 程序特有参数
    program_args = [
        '--disable-blink-features',
        '--disable-dev-shm-usage',
        '--no-first-run',
        '--disable-default-browser-check'
    ]
    has_program_args = any(arg in cmdline_str for arg in program_args)
    
    # 满足任意一个明确特征就认为是程序Chrome
    program_indicators = [has_program_port, has_browser_profiles, has_account_dir, has_project_path]
    
    return any(program_indicators)

def is_personal_chrome(process_cmdline):
    """判断是否是个人Chrome"""
    if not process_cmdline:
        return False
    
    cmdline_str = ' '.join(process_cmdline)
    
    # 个人Chrome特征
    personal_indicators = [
        'AppData\\Local\\Google\\Chrome\\User Data',
        'User Data\\Default',
        '--restore-last-session',
        '--flag-switches-begin',
        '--enable-audio-service-sandbox'
    ]
    
    return any(indicator in cmdline_str for indicator in personal_indicators)

def analyze_chrome_processes_with_features():
    """基于特征分析Chrome进程"""
    print("🔍 基于端口和目录特征分析Chrome进程...")
    print("=" * 70)
    
    project_path = os.getcwd()
    print(f"项目路径: {project_path}")
    print()
    
    chrome_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time']):
        try:
            if 'chrome' in proc.info['name'].lower():
                chrome_processes.append({
                    'pid': proc.info['pid'],
                    'name': proc.info['name'],
                    'cmdline': proc.info['cmdline'] or [],
                    'create_time': proc.info['create_time']
                })
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    if not chrome_processes:
        print("❌ 没有发现Chrome进程")
        return {'personal': [], 'program': [], 'unknown': []}
    
    print(f"📊 发现 {len(chrome_processes)} 个Chrome进程")
    print()
    
    # 分类Chrome进程
    personal_chrome = []
    program_chrome = []
    unknown_chrome = []
    
    for proc in chrome_processes:
        if is_program_chrome(proc['cmdline'], project_path):
            program_chrome.append(proc)
        elif is_personal_chrome(proc['cmdline']):
            personal_chrome.append(proc)
        else:
            unknown_chrome.append(proc)
    
    # 显示分析结果
    print("👤 个人Chrome进程:")
    if personal_chrome:
        for proc in personal_chrome:
            create_time = datetime.fromtimestamp(proc['create_time']).strftime('%H:%M:%S')
            print(f"  PID: {proc['pid']}, 启动时间: {create_time}")
            
            # 显示关键特征
            cmdline_str = ' '.join(proc['cmdline'])
            if 'User Data' in cmdline_str:
                print(f"    特征: 个人用户数据目录")
    else:
        print("  ✅ 没有发现个人Chrome进程")
    
    print()
    print("🤖 程序Chrome进程:")
    if program_chrome:
        for proc in program_chrome:
            create_time = datetime.fromtimestamp(proc['create_time']).strftime('%H:%M:%S')
            print(f"  PID: {proc['pid']}, 启动时间: {create_time}")
            
            # 显示识别特征
            cmdline_str = ' '.join(proc['cmdline'])
            features = []
            
            # 检查端口特征
            port_match = re.search(r'--remote-debugging-port=(\d+)', cmdline_str)
            if port_match:
                port = port_match.group(1)
                if port.startswith('9'):
                    features.append(f"程序端口: {port}")
            
            # 检查目录特征
            if 'browser_profiles' in cmdline_str:
                features.append("程序目录: browser_profiles")
            
            if 'account_' in cmdline_str:
                features.append("账号目录")
            
            if features:
                print(f"    识别特征: {', '.join(features)}")
    else:
        print("  ✅ 没有发现程序Chrome进程")
    
    print()
    print("❓ 未知Chrome进程:")
    if unknown_chrome:
        for proc in unknown_chrome:
            create_time = datetime.fromtimestamp(proc['create_time']).strftime('%H:%M:%S')
            print(f"  PID: {proc['pid']}, 启动时间: {create_time}")
            cmdline_str = ' '.join(proc['cmdline'][:3]) + "..." if len(proc['cmdline']) > 3 else ' '.join(proc['cmdline'])
            print(f"    命令行: {cmdline_str}")
    else:
        print("  ✅ 没有发现未知Chrome进程")
    
    return {
        'personal': personal_chrome,
        'program': program_chrome,
        'unknown': unknown_chrome
    }

def safe_cleanup_program_chrome_by_features():
    """基于特征安全清理程序Chrome进程"""
    print("\n🔧 基于特征安全清理程序Chrome进程...")
    
    project_path = os.getcwd()
    cleaned_count = 0
    protected_count = 0
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if 'chrome' in proc.info['name'].lower():
                if is_program_chrome(proc.info['cmdline'], project_path):
                    # 这是程序Chrome，可以清理
                    try:
                        process = psutil.Process(proc.info['pid'])
                        process.terminate()
                        print(f"  ✅ 清理程序Chrome: PID {proc.info['pid']}")
                        cleaned_count += 1
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        print(f"  ❌ 无法清理进程: PID {proc.info['pid']}")
                elif is_personal_chrome(proc.info['cmdline']):
                    # 这是个人Chrome，保护
                    print(f"  🛡️  保护个人Chrome: PID {proc.info['pid']}")
                    protected_count += 1
                else:
                    # 未知Chrome，保守处理，不清理
                    print(f"  ❓ 跳过未知Chrome: PID {proc.info['pid']}")
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    print(f"\n📊 清理结果:")
    print(f"  清理程序Chrome: {cleaned_count} 个")
    print(f"  保护个人Chrome: {protected_count} 个")
    
    return cleaned_count

def test_feature_recognition():
    """测试特征识别准确性"""
    print("\n🧪 测试特征识别准确性...")
    
    # 测试用例
    test_cases = [
        {
            'name': '程序Chrome - 端口特征',
            'cmdline': ['chrome.exe', '--remote-debugging-port=9001', '--user-data-dir=D:/project/data/browser_profiles/account_1'],
            'expected': 'program'
        },
        {
            'name': '程序Chrome - 目录特征',
            'cmdline': ['chrome.exe', '--user-data-dir=D:/project/data/browser_profiles/account_2'],
            'expected': 'program'
        },
        {
            'name': '个人Chrome',
            'cmdline': ['chrome.exe', '--user-data-dir=C:/Users/<USER>/AppData/Local/Google/Chrome/User Data'],
            'expected': 'personal'
        },
        {
            'name': '个人Chrome - 默认',
            'cmdline': ['chrome.exe', '--restore-last-session', '--flag-switches-begin'],
            'expected': 'personal'
        }
    ]
    
    project_path = os.getcwd()
    
    for test_case in test_cases:
        cmdline = test_case['cmdline']
        expected = test_case['expected']
        
        is_program = is_program_chrome(cmdline, project_path)
        is_personal = is_personal_chrome(cmdline)
        
        if expected == 'program' and is_program:
            result = "✅ 正确"
        elif expected == 'personal' and is_personal:
            result = "✅ 正确"
        else:
            result = "❌ 错误"
        
        print(f"  {test_case['name']}: {result}")

def main():
    """主函数"""
    print("🎯 基于端口和目录特征的精确Chrome清理工具")
    print("=" * 70)
    print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)
    
    # 测试特征识别
    test_feature_recognition()
    
    # 分析当前Chrome进程
    analysis = analyze_chrome_processes_with_features()
    
    # 提供清理选项
    program_count = len(analysis['program'])
    personal_count = len(analysis['personal'])
    
    print(f"\n📊 当前状态:")
    print(f"  个人Chrome进程: {personal_count} 个")
    print(f"  程序Chrome进程: {program_count} 个")
    
    if program_count > 0:
        print(f"\n⚠️ 发现 {program_count} 个程序Chrome进程")
        print("基于端口(9000-9999)和目录(browser_profiles)特征识别")
        
        response = input("\n是否要清理程序Chrome进程？(y/n): ").lower().strip()
        if response == 'y':
            cleaned = safe_cleanup_program_chrome_by_features()
            print(f"\n✅ 清理完成，基于特征精确识别")
            print("您的个人Chrome浏览器完全不受影响")
        else:
            print("跳过清理")
    else:
        print("\n✅ 没有发现程序Chrome进程残留")
        print("当前状态良好，无需清理")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断")
    except Exception as e:
        print(f"\n❌ 异常: {e}")
        import traceback
        traceback.print_exc()
