#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对比AD和AE文件的具体差异
"""

import os
from pathlib import Path

def analyze_file_bytes(file_path, file_name):
    """分析文件的字节内容"""
    print(f"\n分析 {file_name} 文件:")
    print("-" * 30)
    
    if not file_path.exists():
        print(f"{file_name} 文件不存在")
        return None
    
    try:
        # 读取文件大小
        file_size = file_path.stat().st_size
        print(f"文件大小: {file_size} bytes")
        
        # 读取前100字节
        with open(file_path, 'rb') as f:
            first_bytes = f.read(100)
        
        print(f"前20字节 (hex): {first_bytes[:20].hex()}")
        print(f"前20字节 (repr): {repr(first_bytes[:20])}")
        
        # 尝试UTF-8解码
        try:
            decoded = first_bytes.decode('utf-8', errors='replace')
            print(f"UTF-8解码: {repr(decoded[:50])}")
        except Exception as e:
            print(f"UTF-8解码失败: {e}")
        
        # 检查文件头模式
        if first_bytes.startswith(b'\xef\xbb\xbf'):
            print("检测到BOM (UTF-8)")
        elif first_bytes.startswith(b'\xff\xfe'):
            print("检测到BOM (UTF-16 LE)")
        elif first_bytes.startswith(b'\xfe\xff'):
            print("检测到BOM (UTF-16 BE)")
        
        return first_bytes
        
    except Exception as e:
        print(f"分析 {file_name} 文件失败: {e}")
        return None

def compare_file_headers(ad_bytes, ae_bytes):
    """对比文件头"""
    print(f"\n文件头对比:")
    print("-" * 30)
    
    if ad_bytes and ae_bytes:
        print("AD文件头:")
        print(f"  Hex: {ad_bytes[:10].hex()}")
        print(f"  Repr: {repr(ad_bytes[:10])}")
        
        print("AE文件头:")
        print(f"  Hex: {ae_bytes[:10].hex()}")
        print(f"  Repr: {repr(ae_bytes[:10])}")
        
        # 查找差异
        min_len = min(len(ad_bytes), len(ae_bytes))
        for i in range(min_len):
            if ad_bytes[i] != ae_bytes[i]:
                print(f"第一个差异在位置 {i}:")
                print(f"  AD: 0x{ad_bytes[i]:02x} ({chr(ad_bytes[i]) if 32 <= ad_bytes[i] <= 126 else '?'})")
                print(f"  AE: 0x{ae_bytes[i]:02x} ({chr(ae_bytes[i]) if 32 <= ae_bytes[i] <= 126 else '?'})")
                break

def check_file_type_detection():
    """检查文件类型检测"""
    print(f"\n文件类型检测:")
    print("-" * 30)
    
    ad_path = Path("data/browser_profiles/account_10/AutofillStates/2025.6.13.84507/AD")
    ae_path = Path("data/browser_profiles/account_10/AutofillStates/2025.6.13.84507/AE")
    
    for file_path, name in [(ad_path, "AD"), (ae_path, "AE")]:
        if file_path.exists():
            try:
                # 检查文件权限
                stat = file_path.stat()
                print(f"{name} 文件权限: {oct(stat.st_mode)}")
                
                # 检查文件是否可执行
                if os.access(file_path, os.X_OK):
                    print(f"{name} 文件被标记为可执行 ← 这可能是问题！")
                else:
                    print(f"{name} 文件不可执行 (正常)")
                    
            except Exception as e:
                print(f"检查 {name} 文件属性失败: {e}")

def analyze_content_structure():
    """分析内容结构"""
    print(f"\n内容结构分析:")
    print("-" * 30)
    
    ad_path = Path("data/browser_profiles/account_10/AutofillStates/2025.6.13.84507/AD")
    ae_path = Path("data/browser_profiles/account_10/AutofillStates/2025.6.13.84507/AE")
    
    for file_path, name in [(ad_path, "AD"), (ae_path, "AE")]:
        if file_path.exists():
            try:
                with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                    lines = f.readlines()[:5]  # 只读前5行
                
                print(f"{name} 文件前5行:")
                for i, line in enumerate(lines, 1):
                    clean_line = line.strip()
                    print(f"  第{i}行: {repr(clean_line)}")
                    
                    # 分析第一行的结构
                    if i == 1:
                        if '���' in clean_line:
                            parts = clean_line.split('���')
                            print(f"    分割后: {parts}")
                            
                            # 检查预期格式
                            if name == "AE" and len(parts) >= 3 and parts[1] == "AE":
                                print(f"    ✅ {name} 格式正常: 国家代码在正确位置")
                            elif name == "AD" and "ADocs" in clean_line:
                                print(f"    ❌ {name} 格式异常: 应该是'AD'但显示'ADocs'")
                
            except Exception as e:
                print(f"分析 {name} 内容失败: {e}")

def main():
    """主函数"""
    print("AD vs AE 文件对比分析")
    print("=" * 50)
    
    ad_path = Path("data/browser_profiles/account_10/AutofillStates/2025.6.13.84507/AD")
    ae_path = Path("data/browser_profiles/account_10/AutofillStates/2025.6.13.84507/AE")
    
    # 1. 分析字节内容
    ad_bytes = analyze_file_bytes(ad_path, "AD")
    ae_bytes = analyze_file_bytes(ae_path, "AE")
    
    # 2. 对比文件头
    compare_file_headers(ad_bytes, ae_bytes)
    
    # 3. 检查文件类型检测
    check_file_type_detection()
    
    # 4. 分析内容结构
    analyze_content_structure()
    
    # 5. 总结问题
    print(f"\n问题总结:")
    print("=" * 50)
    print("根据对比分析，AD文件的问题可能是:")
    print("1. 文件头损坏: 'AD' 变成了 'ADocs'")
    print("2. 文件格式识别异常: 被系统识别为Python文件")
    print("3. 可能的原因:")
    print("   - Chrome下载AD文件时被中断")
    print("   - 文件写入时发生冲突")
    print("   - 磁盘错误导致数据损坏")
    print("4. 解决方案:")
    print("   - 删除损坏的AD文件")
    print("   - Chrome会重新下载正确的AD文件")
    print("   - 或者从AE文件模板修复AD文件头")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"\n异常: {e}")
        import traceback
        traceback.print_exc()
