#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Chrome连接问题诊断和修复工具
"""

import os
import sys
import subprocess
import time
import requests

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_chrome_version():
    """检查Chrome版本"""
    print("🔍 检查Chrome版本...")
    
    try:
        # Windows Chrome路径
        chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME'))
        ]
        
        chrome_path = None
        for path in chrome_paths:
            if os.path.exists(path):
                chrome_path = path
                break
        
        if not chrome_path:
            print("  ❌ 找不到Chrome安装路径")
            return None
        
        # 获取Chrome版本
        result = subprocess.run([chrome_path, "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"  ✅ Chrome版本: {version}")
            return version
        else:
            print("  ❌ 无法获取Chrome版本")
            return None
            
    except Exception as e:
        print(f"  ❌ 检查Chrome版本失败: {e}")
        return None

def check_chromedriver_version():
    """检查ChromeDriver版本"""
    print("\n🔍 检查ChromeDriver版本...")
    
    try:
        result = subprocess.run(["chromedriver", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"  ✅ ChromeDriver版本: {version}")
            return version
        else:
            print("  ❌ ChromeDriver不在PATH中")
            return None
            
    except FileNotFoundError:
        print("  ❌ 找不到ChromeDriver")
        return None
    except Exception as e:
        print(f"  ❌ 检查ChromeDriver版本失败: {e}")
        return None

def test_basic_chrome_startup():
    """测试基础Chrome启动"""
    print("\n🧪 测试基础Chrome启动...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        
        # 创建最简单的Chrome选项
        options = Options()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--remote-debugging-port=9999')
        
        print("  🚀 尝试启动Chrome...")
        
        try:
            service = Service()
            driver = webdriver.Chrome(service=service, options=options)
            
            print("  ✅ Chrome启动成功")
            
            # 测试基本操作
            driver.get("data:text/html,<html><body><h1>Test</h1></body></html>")
            title = driver.title
            print(f"  📄 页面标题: '{title}'")
            
            driver.quit()
            print("  ✅ Chrome关闭成功")
            return True
            
        except Exception as e:
            print(f"  ❌ Chrome启动失败: {e}")
            return False
            
    except ImportError as e:
        print(f"  ❌ 导入Selenium失败: {e}")
        return False

def test_chrome_with_user_data():
    """测试带用户数据目录的Chrome启动"""
    print("\n🧪 测试带用户数据目录的Chrome启动...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        
        # 创建测试用户数据目录
        test_dir = os.path.join(os.getcwd(), "test_chrome_profile")
        os.makedirs(test_dir, exist_ok=True)
        
        options = Options()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument(f'--user-data-dir={test_dir}')
        options.add_argument('--remote-debugging-port=9998')
        
        print(f"  🚀 尝试启动Chrome (用户数据目录: {test_dir})...")
        
        try:
            service = Service()
            driver = webdriver.Chrome(service=service, options=options)
            
            print("  ✅ 带用户数据目录的Chrome启动成功")
            
            driver.get("data:text/html,<html><body><h1>Test with Profile</h1></body></html>")
            title = driver.title
            print(f"  📄 页面标题: '{title}'")
            
            driver.quit()
            print("  ✅ Chrome关闭成功")
            
            # 清理测试目录
            import shutil
            try:
                shutil.rmtree(test_dir)
                print("  🧹 测试目录已清理")
            except Exception:
                print("  ⚠️  测试目录清理失败（可能被占用）")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 带用户数据目录的Chrome启动失败: {e}")
            return False
            
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        return False

def check_port_availability():
    """检查端口可用性"""
    print("\n🔍 检查端口可用性...")
    
    import socket
    
    test_ports = [9000, 9001, 9002, 9998, 9999, 13874]
    
    for port in test_ports:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('127.0.0.1', port))
            sock.close()
            
            if result == 0:
                print(f"  ⚠️  端口 {port} 被占用")
            else:
                print(f"  ✅ 端口 {port} 可用")
                
        except Exception as e:
            print(f"  ❌ 检查端口 {port} 时出错: {e}")

def fix_chrome_connection_issues():
    """修复Chrome连接问题"""
    print("\n🔧 尝试修复Chrome连接问题...")
    
    fixes_applied = []
    
    # 修复1: 清理Chrome进程
    try:
        import psutil
        chrome_count = 0
        for proc in psutil.process_iter(['pid', 'name']):
            if 'chrome' in proc.info['name'].lower():
                try:
                    proc.terminate()
                    chrome_count += 1
                except:
                    pass
        
        if chrome_count > 0:
            print(f"  ✅ 清理了 {chrome_count} 个Chrome进程")
            fixes_applied.append("清理Chrome进程")
            time.sleep(3)
        else:
            print("  ℹ️  没有发现需要清理的Chrome进程")
            
    except Exception as e:
        print(f"  ⚠️  清理Chrome进程失败: {e}")
    
    # 修复2: 清理ChromeDriver进程
    try:
        import psutil
        driver_count = 0
        for proc in psutil.process_iter(['pid', 'name']):
            if 'chromedriver' in proc.info['name'].lower():
                try:
                    proc.terminate()
                    driver_count += 1
                except:
                    pass
        
        if driver_count > 0:
            print(f"  ✅ 清理了 {driver_count} 个ChromeDriver进程")
            fixes_applied.append("清理ChromeDriver进程")
        else:
            print("  ℹ️  没有发现需要清理的ChromeDriver进程")
            
    except Exception as e:
        print(f"  ⚠️  清理ChromeDriver进程失败: {e}")
    
    # 修复3: 清理临时文件
    try:
        temp_dirs = [
            os.path.expanduser("~/AppData/Local/Temp"),
            "data/browser_profiles"
        ]
        
        cleaned_files = 0
        for temp_dir in temp_dirs:
            if os.path.exists(temp_dir):
                for root, dirs, files in os.walk(temp_dir):
                    for file in files:
                        if 'chrome' in file.lower() and ('.tmp' in file or '.lock' in file):
                            try:
                                os.remove(os.path.join(root, file))
                                cleaned_files += 1
                            except:
                                continue
        
        if cleaned_files > 0:
            print(f"  ✅ 清理了 {cleaned_files} 个临时文件")
            fixes_applied.append("清理临时文件")
        else:
            print("  ℹ️  没有发现需要清理的临时文件")
            
    except Exception as e:
        print(f"  ⚠️  清理临时文件失败: {e}")
    
    return fixes_applied

def main():
    """主诊断函数"""
    print("🔧 Chrome连接问题诊断和修复工具")
    print("=" * 60)
    
    # 步骤1: 检查版本
    chrome_version = check_chrome_version()
    chromedriver_version = check_chromedriver_version()
    
    # 步骤2: 检查端口
    check_port_availability()
    
    # 步骤3: 修复问题
    fixes = fix_chrome_connection_issues()
    
    # 步骤4: 测试基础启动
    basic_test = test_basic_chrome_startup()
    
    # 步骤5: 测试用户数据目录
    profile_test = test_chrome_with_user_data()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 诊断结果总结")
    print("=" * 60)
    
    print(f"Chrome版本检查: {'✅ 正常' if chrome_version else '❌ 异常'}")
    print(f"ChromeDriver版本检查: {'✅ 正常' if chromedriver_version else '❌ 异常'}")
    print(f"基础Chrome启动: {'✅ 成功' if basic_test else '❌ 失败'}")
    print(f"用户数据目录启动: {'✅ 成功' if profile_test else '❌ 失败'}")
    
    if fixes:
        print(f"\n🔧 应用的修复: {', '.join(fixes)}")
    
    if basic_test and profile_test:
        print("\n🎉 Chrome连接问题已修复！")
        print("\n✅ 建议:")
        print("  - Chrome现在可以正常启动")
        print("  - 可以重新尝试互动功能")
        print("  - 系统应该能正常工作")
    else:
        print("\n❌ Chrome连接问题仍存在")
        print("\n🔧 进一步建议:")
        print("  1. 重启计算机")
        print("  2. 更新Chrome到最新版本")
        print("  3. 重新安装ChromeDriver")
        print("  4. 检查防火墙设置")
        print("  5. 尝试以管理员身份运行")
    
    return basic_test and profile_test

if __name__ == "__main__":
    try:
        result = main()
        exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⏹️  用户中断诊断")
        exit(1)
    except Exception as e:
        print(f"\n❌ 诊断工具异常: {e}")
        exit(1)
